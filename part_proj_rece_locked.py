#coding:utf-8
import zipfile
####################################################抬头
import threading
import pandas as pd
import numpy as np
import time
import os
import xlsxwriter
from pathlib import Path
import warnings
import tkinter as tk
import tkinter.messagebox #弹出框
from tkinter import *

from PIL import Image, ImageTk, ImageSequence
import xlsxwriter
import traceback
import datetime
from tkinter import scrolledtext
from datetime import datetime
from picture import load_image
from wholeuse import*
year1='2025'
num_list=["集团收入","软件收入","硬件收入",'成本', '料', '料(采购单价)','工单料', '设变料', '采购PO', '生产工', '交付工','设计工', '其他费'
                        , 'M2-3成本合计', 'M2-3料', 'M2-3工单料', 'M2-3设变料', 'M2-3采购PO',  'M2-3生产工', 'M2-3交付工',  'M2-3设计工','M2-3其他费'
                        , 'M4成本合计', 'M4料', 'M4工单料', 'M4设变料', 'M4采购PO',  'M4生产工', 'M4交付工',  'M4设计工', 'M4其他费'
                        , 'M5成本合计', 'M5料', 'M5工单料', 'M5设变料', 'M5采购PO',  'M5生产工', 'M5交付工',  'M5设计工', 'M5其他费'
                        , '工时(生产交付)', '生产工时', '交付工时','设计工时',year1+'工时(生产交付)',year1+'生产工时',year1+'交付工时',year1+'设计工时','24年制费公摊金额'
                        ,'LOSS工成本','LOSS生产工','LOSS交付工','LOSS设计工','LOSS工时(生产交付)'
                        ,'LOSS生产工时','LOSS交付工时','LOSS设计工时',year1+'LOSS工时(生产交付)',year1+'LOSS生产工时',year1+'LOSS交付工时',year1+'LOSS设计工时'
                        , 'M2-3生产工LOSS', 'M2-3交付工LOSS', 'M2-3设计工LOSS', 'M4生产工LOSS', 'M4交付工LOSS'
                        , 'M4设计工LOSS', 'M5生产工LOSS', 'M5交付工LOSS', 'M5设计工LOSS']
def rece_lock():
    time_start = time.time()
    
    default_date = pd.Timestamp(1990, 1, 1)
    frame_total.create_image(740, 60, image=load_image(r'0、软件附带文件\框子.png'), anchor="n")
    screm_total = scrolledtext.ScrolledText(frame_total, bg='powderblue',  # 标签背景颜色
                                            highlightthickness=0,
                                            font=('微软雅黑', 12),  # 字体和字体大小
                                            width=72, height=14  # 标签长宽(以字符长度计算)
                                            )
    # roll = Scrollbar(window_total, orient='vertical', command=frame_total.yview)
    screm_total_transform=frame_total.create_window(750, 240, window=screm_total)
    button_texts.append(screm_total_transform)

    try:
        screm_total.insert(INSERT, '1：读取两个表', '\n')
        window_total.update()
        path_cal = r'8、转移变更处理-数据\核算汇总表'
        # need_price = ['料件编号', '最新价格']
        report_cal = []
        index = 0
        file_cal = os.listdir(path_cal)

        for i in file_cal:
            if '~$' in i:
                file_cal.remove(i)
        type='大项目号'
        if os.listdir(path_cal):
            for i in range(len(file_cal)):
                if str(file_cal[i]).count('~$') == 0:
                    if '核算项目号' in str(file_cal[i]):
                        type='核算项目号'
                    # report_item = pd.read_excel(filePath1 + '/' + str(file_name1[i]))
                    report_cal = pd.read_excel(path_cal + '\\' + file_cal[i], header=2)
                    for col in num_list:
                        report_cal[col]=report_cal[col].fillna(0)
                    for dat in ['工单开立时间', '工单完工时间', '系统出货时间', '实际出货时间', '系统验收时间',
                                '实际验收时间']:
                        report_cal[dat] = report_cal[dat].fillna(default_date)
                        report_cal[dat] = pd.to_datetime(report_cal[dat], errors='coerce').dt.strftime(
                            '%Y-%m-%d').astype(str)
                    report_cal =report_cal.fillna('')
        path_rece_locked = r'8、转移变更处理-数据\已验收项目冻结成本清单'
        # need_price = ['料件编号', '最新价格']
        report_rece_locked = []
        index = 0
        file_rece_locked = os.listdir(path_rece_locked)

        for i in file_rece_locked:
            if '~$' in i:
                file_rece_locked.remove(i)

        if os.listdir(path_rece_locked):
            for i in range(len(file_rece_locked)):
                if str(file_rece_locked[i]).count('~$') == 0:
                    # report_item = pd.read_excel(filePath1 + '/' + str(file_name1[i]))
                    if '核算项目号' in str(file_rece_locked[i]) or '方装' in str(file_rece_locked[i]):
                        type='核算项目号'
                    report_rece_locked = pd.read_excel(path_rece_locked + '\\' + file_rece_locked[i], header=2)
                    print( report_rece_locked.columns)
                    for col in num_list:
                        report_rece_locked[col]=report_rece_locked[col].fillna(0)
                    report_rece_locked =report_rece_locked.fillna('')

        screm_total.insert(INSERT, '\n2：数据处理', '\n')
        window_total.update()
        report_rece_locked = report_rece_locked.drop_duplicates(subset=['序列号']).reset_index(drop=True)

        report_cal['排个序'] = 0
        for i in range(len(report_cal)):
            report_cal.loc[i, '排个序'] = i + 1
        #report_cal_use = report_cal[report_cal['核算项目号'].isin(report_rece_locked['核算项目号'])].reset_index(drop=True)
        for i in range(len(report_rece_locked)):
            report_cal_need1=report_cal[(report_cal[type]==report_rece_locked.loc[i,type])&(~report_cal['序列号'].isin(report_rece_locked['序列号']))].reset_index(drop=True)
            report_cal_deal = report_cal[report_cal['序列号'] == report_rece_locked.loc[i, '序列号']].reset_index(drop=True)
            report_cal_need = pd.concat([report_cal_need1, report_cal_deal]).reset_index(drop=True)
            report_cal_extra = report_cal[~report_cal['排个序'].isin(report_cal_need['排个序'])].reset_index(drop=True)
            if len(report_cal_deal)>0:
                report_extra = pd.DataFrame(report_rece_locked.iloc[i]).T.reset_index(drop=True)
                if len(report_cal_need)>1:
                    report_extra[num_list]=(report_cal_deal[num_list]-report_extra[num_list])/ (len(report_cal_need) - 1)
                for j in range(len(report_cal_need)):
                    if report_cal_need.loc[j,'序列号']==report_rece_locked.loc[i, '序列号']:
                        for col in report_rece_locked.columns:
                            report_cal_need.loc[j,col]=report_rece_locked.loc[i, col]
                    if report_cal_need.loc[j,'序列号'] not in report_rece_locked.loc[i, '序列号']:
                        for num in num_list:
                            report_cal_need.loc[j, num] = report_cal_need.loc[j, num]+report_extra.loc[0, num]
                report_cal=pd.concat([report_cal_need,report_cal_extra]).reset_index(drop=True)

        report_cal = report_cal.sort_values(by=['排个序'], ascending=True).reset_index(drop=True)
        del report_cal['排个序']

        for dat in ['工单开立时间', '工单完工时间', '系统出货时间', '实际出货时间', '系统验收时间', '实际验收时间']:
            report_cal.loc[report_cal[dat]=='',dat]=default_date
            report_cal[dat] = report_cal[dat].fillna(default_date)
            report_cal[dat] = pd.to_datetime(report_cal[dat], errors='coerce').dt.strftime('%Y-%m-%d').astype(str)
            report_cal[dat] = ['' if i == '1990-01-01' else i for i in report_cal[dat]]
        report_cal=report_cal.fillna('')
        l = 0
        screm_total.insert(INSERT, '\n3：数据输出', '\n')
        window_total.update()
        def writer_contents(sheet, array, start_row, start_col, format=None, percent_format=None, percentlist=[]):
            start_col = 0
            for col in array:
                if percentlist and (start_col in percentlist):
                    sheet.write_column(start_row, start_col, col, percent_format)
                else:
                    sheet.write_column(start_row, start_col, col, format)
                start_col += 1
        ####################################
        now_time = time.strftime("%Y-%m-%d-%H", time.localtime(time.time()))
        book_name = '8、转移变更处理-数据\成本汇总验收固定' + now_time
        workbook = xlsxwriter.Workbook(book_name + '.xlsx', {'nan_inf_to_errors': True})
        worksheet0 = workbook.add_worksheet('成本汇总')

        title_format = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 10,
                                            'font_color': 'white',
                                            'bg_color': '#1F4E78',
                                            'bold': True,
                                            'align': 'center',
                                            'valign': 'vcenter',
                                            'border': 1,
                                            'border_color': 'white'
                                            })
        title_format.set_align('vcenter')
        title_format1 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#006666',
                                             'bold': True,

                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format1.set_align('vcenter')
        title_format2 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#008000',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format2.set_align('vcenter')
        title_format3 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#7030A0',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format3.set_align('vcenter')

        title_format4 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#2F75B5',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format4.set_align('vcenter')
        title_format5 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#305496',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format5.set_align('vcenter')
        title_format6 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#333F4F',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format6.set_align('vcenter')
        col_format = workbook.add_format({'font_name': 'Arial',
                                          'font_size': 8,
                                          'font_color': 'white',
                                          'bg_color': '#1F4E78',
                                          'text_wrap': True,
                                          'border': 1,
                                          'border_color': 'white',
                                          'align': 'center',
                                          'valign': 'vcenter'
                                          })

        data_format = workbook.add_format({'font_name': 'Arial',
                                           'font_size': 10,
                                           'align': 'left',
                                           'valign': 'vcenter'
                                           })
        data_format2 = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 10,
                                            'align': 'center',
                                            'valign': 'vcenter'
                                            })
        data_format2.set_num_format('0.00')

        data_format1 = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 10,
                                            'align': 'center',
                                            'valign': 'vcenter'
                                            })
        data_format_percent = workbook.add_format({'font_name': 'Arial',
                                                   'font_size': 10,
                                                   'align': 'center',
                                                   'valign': 'vcenter'
                                                   })
        data_format_percent.set_num_format('0.00%')
        num_percent_data_format = workbook.add_format({'font_name': 'Arial',
                                                       'font_size': 10,
                                                       'align': 'center',
                                                       'valign': 'vcenter',
                                                       'num_format': '0.00%'
                                                       })
        statis_format2 = workbook.add_format({'font_name': 'Arial',  # 系列总计
                                              'font_size': 9,
                                              'align': 'center',
                                              'valign': 'vcenter',
                                              'bg_color': '#92CDDC'
                                              })
        worksheet0.write_row("A3", report_cal, title_format)

        writer_contents(sheet=worksheet0, array=report_cal.T.values, start_row=3, start_col=0)
        worksheet0.write_row("AE3:AM3",['M2-3成本合计', 'M2-3料', 'M2-3工单料', 'M2-3设变料', 'M2-3采购PO', 'M2-3生产工','M2-3交付工', 'M2-3设计工', 'M2-3其他费'], title_format4)
        worksheet0.write_row("AN3:BA3",['一般工单号601/608', '工单开立时间', '工单完工时间', 'M4成本合计', 'M4料', 'M4工单料',
                              'M4设变料', 'M4采购PO', 'M4生产工', 'M4交付工', 'M4设计工', 'M4其他费', '系统出货时间', '实际出货时间'],
                             title_format5)
        worksheet0.write_row("BB3:BM3",['返工工单号603', 'M5成本合计', 'M5料', 'M5工单料', 'M5设变料', 'M5采购PO', 'M5生产工',
                              'M5交付工','M5设计工', 'M5其他费', '系统验收时间', '实际验收时间'], title_format6)

        worksheet0.merge_range("A1:CX1","成本汇总——————————————————————————成本汇总——————————————————————————成本汇总——————————————————————————成本汇总——————————————————————————成本汇总———————————————————————————成本汇总——————————————————————————成本汇总——————————————————————————成本汇总——————————————————————————成本汇总——————————————————————————成本汇总——————————————————————————成本汇总",
                               title_format)

        worksheet0.merge_range("A2:O2", "项目基本信息", title_format)
        worksheet0.merge_range("P2:AD2", "项目核算毛利", title_format)
        worksheet0.merge_range("AE2:AM2", "M2-3阶段信息", title_format4)
        worksheet0.merge_range("AN2:BA2", "M4阶段信息", title_format5)
        worksheet0.merge_range("BB2:BM2", "M5阶段信息", title_format6)
        worksheet0.merge_range("BN2:BS2", "辅助信息", title_format)
        worksheet0.merge_range("BT2:BW2", "工时汇总", title_format)
        worksheet0.merge_range("BX2:CA2", year1+"年工时汇总", title_format)
        worksheet0.merge_range("CC2:CF2", "LOSS工时成本汇总", title_format)
        worksheet0.merge_range("CG2:CJ2", "LOSS工时汇总", title_format)
        worksheet0.merge_range("CK2:CN2", year1+"LOSS工时汇总", title_format)
        worksheet0.merge_range("CO2:CQ2", "M2-3LOSS工", title_format)
        worksheet0.merge_range("CR2:CT2", "M4LOSS工", title_format)
        worksheet0.merge_range("CU2:CW2", "M5LOSS工", title_format)
        worksheet0.write("CB2", "", title_format)
        worksheet0.write("CB3", "24年制费公摊金额", title_format)
        worksheet0.write("CX2", "", title_format)
        worksheet0.set_row(0, 25)
        worksheet0.set_row(1, 22)
        worksheet0.set_column('A:K', 8, data_format)
        worksheet0.set_column('L:N', 10, data_format2)
        worksheet0.set_column('O:O', 8, data_format)
        worksheet0.set_column('P:T', 8, data_format2)
        worksheet0.set_column('U:U', 8, data_format_percent)
        worksheet0.set_column('V:AM', 8, data_format2)
        worksheet0.set_column('AN:AP', 10, data_format)
        worksheet0.set_column('AQ:AY', 8, data_format2)
        worksheet0.set_column('AZ:BB', 10, data_format)
        worksheet0.set_column('BC:BK', 8, data_format2)
        worksheet0.set_column('BL:BS', 9, data_format)
        worksheet0.set_column('BT:CX', 8, data_format2)
        workbook.close()
    except Exception as f:
        # print('异常信息为:', e)  # 异常信息为: division by zero
        print('——#@*&程序报错，异常信息为:' + traceback.format_exc())
        screm_total.insert(INSERT, '\n——#@*&程序报错，异常信息为:' + traceback.format_exc(), '\n')
        window_total.update()

    time_end = time.time()
    print('执行完成！！！！！')
    print('执行总时长:%d秒' % (time_end - time_start))
    screm_total.insert(INSERT, '\n执行完成！！！！！', '\n')
    screm_total.insert(INSERT, '\n执行总时长:%d秒' % (time_end - time_start), '\n')
    window_total.update()