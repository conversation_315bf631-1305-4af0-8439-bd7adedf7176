import pandas as pd
import os
import glob
from pathlib import Path
import gc
import warnings
warnings.filterwarnings('ignore')
def process_project_data():
    """
    根据项目号清单从四个文件夹中筛选数据并生成对应的CSV文件
    采用分块读取处理大文件，保留所有匹配的原始数据
    """

    # 1. 读取项目号清单
    try:
        project_list = pd.read_excel('项目号查询清单.xlsx')
        # 获取项目号整理列的所有项目号，转换为set提高查找效率
        project_numbers = set(project_list['项目号整理'].dropna().astype(str))
        print(f"读取到 {len(project_numbers)} 个项目号")
    except Exception as e:
        print(f"读取项目号清单失败: {e}")
        return

    # 2. 定义四个文件夹
    folders = ['采购PO', '料', '工', '费']

    # 3. 处理每个文件夹
    for folder in folders:
        print(f"\n正在处理文件夹: {folder}")

        if not os.path.exists(folder):
            print(f"文件夹 {folder} 不存在，跳过")
            continue

        # 获取文件夹中的所有CSV文件
        csv_files = glob.glob(os.path.join(folder, "*.csv"))

        if not csv_files:
            print(f"文件夹 {folder} 中没有CSV文件")
            continue

        # 输出文件名
        output_filename = f"{folder}_项目号整理.csv"

        # 初始化输出文件
        output_initialized = False
        total_matched_records = 0

        # 逐个处理CSV文件
        for csv_file in csv_files:
            try:
                print(f"  处理文件: {os.path.basename(csv_file)}")
                file_matched_records = 0

                # 先读取一小部分来检测列名和编码
                try:
                    sample_df = pd.read_csv(csv_file, nrows=10, encoding='utf-8')
                    encoding = 'utf-8'
                except:
                    try:
                        sample_df = pd.read_csv(csv_file, nrows=10, encoding='gb18030')
                        encoding = 'gb18030'
                    except:
                        sample_df = pd.read_csv(csv_file, nrows=10, encoding='utf-8-sig')
                        encoding = 'utf-8-sig'

                # 自动检测可能的项目号列名
                possible_keywords = ['项目号整理']
                project_column = None

                for col in sample_df.columns:
                    col_str = str(col).lower()
                    if any(keyword in col_str for keyword in possible_keywords):
                        project_column = col
                        break

                if project_column is None:
                    print(f"    警告: 未找到项目号列，跳过文件")
                    continue

                print(f"    使用项目号列: {project_column}")

                # 分块读取大文件
                chunk_size = 200000  # 每次读取1万行

                for chunk_num, chunk in enumerate(pd.read_csv(csv_file,
                                                              chunksize=chunk_size,
                                                              encoding=encoding)):

                    # 将项目号列转换为字符串类型进行比较
                    chunk[project_column] = chunk[project_column].astype(str)

                    # 筛选匹配的项目号
                    filtered_chunk = chunk[chunk[project_column].isin(project_numbers)]

                    if not filtered_chunk.empty:
                        # 保存匹配的数据
                        if not output_initialized:
                            # 第一次写入，包含表头
                            filtered_chunk.to_csv(output_filename,
                                                  index=False,
                                                  encoding='utf-8-sig',
                                                  mode='w')
                            output_initialized = True
                        else:
                            # 追加写入，不包含表头
                            filtered_chunk.to_csv(output_filename,
                                                  index=False,
                                                  encoding='utf-8-sig',
                                                  mode='a',
                                                  header=False)

                        file_matched_records += len(filtered_chunk)
                        total_matched_records += len(filtered_chunk)

                    # 显示处理进度
                    if chunk_num % 10 == 0 and chunk_num > 0:
                        print(f"    已处理 {(chunk_num + 1) * chunk_size} 行...")

                    # 清理内存
                    del chunk, filtered_chunk
                    gc.collect()

                print(f"    找到 {file_matched_records} 条匹配记录")

            except Exception as e:
                print(f"    处理文件 {csv_file} 失败: {e}")
                continue

        # 处理结果
        if total_matched_records > 0:
            print(f"  ✓ 已生成文件: {output_filename} (共 {total_matched_records} 条记录)")
        else:
            print(f"  文件夹 {folder} 中没有匹配的数据")

    print("\n处理完成！")


def get_file_size_mb(filepath):
    """获取文件大小(MB)"""
    return os.path.getsize(filepath) / (1024 * 1024)




def check_large_files():
    """
    检查大文件情况
    """
    folders = ['采购PO', '料', '工', '费']

    print("\n检查文件大小:")
    for folder in folders:
        if os.path.exists(folder):
            csv_files = glob.glob(os.path.join(folder, "*.csv"))
            for csv_file in csv_files:
                size_mb = get_file_size_mb(csv_file)
                size_info = f"{size_mb:.1f}MB"
                if size_mb > 100:
                    size_info += " (大文件)"
                print(f"  {csv_file}: {size_info}")


if __name__ == "__main__":
    print("=== 大文件项目号数据筛选工具 ===")



    print("\n2. 检查大文件情况...")
    check_large_files()

    print("\n3. 开始处理数据...")
    print("注意: 使用分块读取处理大文件，保留所有原始数据...")
    process_project_data()

    print("\n=== 处理完成 ===")
    input('*************&&&请点击回车关闭程序...')