#coding:utf-8
import time
import zipfile
####################################################抬头
import threading
import pandas as pd
import numpy as np
import time
import os
import xlsxwriter
from pathlib import Path
import warnings
import tkinter as tk
import tkinter.messagebox #弹出框
from tkinter import *
from PIL import Image, ImageTk, ImageSequence
import xlsxwriter
import traceback
import datetime
from tkinter import scrolledtext
from datetime import datetime

from picture import load_image

from wholeuse import*

# 为全局变量赋值
############################################################################################################底表
def base():
    time_start = time.time()
    '''
    image_led= ImageTk.PhotoImage(file=r'软件附带文件\框子.png')
    frame_total.create_image(740, 60, anchor='n', image=image_led)
    '''

    frame_total.create_image(740, 60, image=load_image(r'软件附带文件\框子.png'), anchor="n")
    screm_total = scrolledtext.ScrolledText(frame_total, bg='powderblue',  # 标签背景颜色
                    highlightthickness=0,
                    font=('微软雅黑', 12),  # 字体和字体大小
                    width=72, height=14 # 标签长宽(以字符长度计算)
                    )
    #roll = Scrollbar(window_total, orient='vertical', command=frame_total.yview)
    frame_total.create_window(740, 246, window=screm_total)

    try:
        #########################################################一
        print("一、数据读取..")
        screm_total.insert(INSERT, '一、数据读取..', '\n')
        window_total.update()
        time1_start = time.time()
        print("1.1正在读取对照表..")
        screm_total.insert(INSERT, '\n1.1正在读取对照表..', '\n')
        window_total.update()
        #########################读取项目对照表
        filePath_item = r'底表-数据\对照表'
        file_name_item = os.listdir(filePath_item)
        for i in range(len(file_name_item)):
            if str(file_name_item[i]).count('~$') == 0:
                # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
                item = pd.read_excel(filePath_item + '/' + str(file_name_item[i]), sheet_name='项目对照表', header=5)
                item_transform = pd.read_excel(filePath_item + '/' + str(file_name_item[i]),
                                               sheet_name='转移变更表')  # [['大项目号', '大项目名称', '产品线', '产品项目号', '产品项目名称', '设备类型', '生产料号', '生产数量']]
        if '产品线重分类' in item_transform.columns:
            del item_transform['产品线']
            item_transform = item_transform.rename(columns={'产品线重分类': '产品线'})
        item_transform = item_transform[['大项目号', '大项目名称', '产品线', '产品项目号', '产品项目名称', '设备类型', '生产料号', '生产数量']]
        if '产品线重分类' in item.columns:
            del item['产品线']
            item = item.rename(columns={'产品线重分类': '产品线'})
        #########################读取台账拆分表
        print("1.2正在读取台账拆分表..")
        screm_total.insert(INSERT, '\n1.2正在读取台账拆分表..', '\n')
        window_total.update()
        filePath_split = r'底表-数据\台账拆分表'
        file_name_split = os.listdir(filePath_split)
        for i in range(len(file_name_split)):
            if str(file_name_split[i]).count('~$') == 0:
                # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
                bill_split = pd.read_excel(filePath_split + '/' + str(file_name_split[i]), header=2)[
                    ['大项目号', '产品项目号', '未税金额_x', '未税金额_y']]

        #########################读取工单开立表
        print("1.3正在读取工单开立表..")
        screm_total.insert(INSERT, '\n1.3正在读取工单开立表..', '\n')
        window_total.update()
        need_time = ['大项目号', '工单单号', '项目编号', '单据日期', '过账日期', '单号', '关联的一般工单', '生产料号', '生产数量', '状态码']
        path_time_start = r'IT补充数据源\工单开立时间'
        file_time_start = os.listdir(path_time_start)
        for i in file_time_start:
            if '~$' in i:
                file_time_start.remove(i)
        report_time_star = []
        if os.listdir(path_time_start):
            for name in file_time_start:
                if 'csv' in name and '~$' not in name:
                    # print('读取费用报销表第' + str(index + 1) + '份' + name)
                    # df = pd.read_excel(os.path.join(path_cost, name), header=2,thousands=',')
                    with open(os.path.join(path_time_start, name), encoding='utf-8', errors='ignore') as f:
                        # 再解决部分报错行如 ParserError：Error tokenizing data.C error:Expected 2 fields in line 407,saw 3.
                        df12 = pd.read_csv(f, sep=',', error_bad_lines=False, low_memory=False, thousands=',')
                        # df12 = df12[:-4]
                        for col in df12.columns:
                            if col not in need_time:
                                del df12[col]
                        report_time_star.append(df12)
        report_time_start = pd.concat(report_time_star).reset_index(drop=True)

        #########################读取工单完工表
        print("1.4正在读取工单完工表..")
        screm_total.insert(INSERT, '\n1.4正在读取工单完工表..', '\n')
        window_total.update()
        need_time = ['大项目号', '工单单号', '项目编号', '单据日期', '过账日期', '单号', '数量']
        path_time_end = r'IT补充数据源\工单完工时间'
        file_time_end = os.listdir(path_time_end)
        for i in file_time_end:
            if '~$' in i:
                file_time_end.remove(i)
        report_time_en = []
        if os.listdir(path_time_end):
            for name in file_time_end:
                if 'csv' in name and '~$' not in name:
                    # print('读取费用报销表第' + str(index + 1) + '份' + name)
                    # df = pd.read_excel(os.path.join(path_cost, name), header=2,thousands=',')
                    with open(os.path.join(path_time_end, name), encoding='utf-8', errors='ignore') as f:
                        # 再解决部分报错行如 ParserError：Error tokenizing data.C error:Expected 2 fields in line 407,saw 3.
                        df13 = pd.read_csv(f, sep=',', error_bad_lines=False, low_memory=False, thousands=',')
                        # df13 = df13[:-2]
                        for col in df13.columns:
                            if col not in need_time:
                                del df13[col]
                    report_time_en.append(df13)
        report_time_end = pd.concat(report_time_en).reset_index(drop=True)

        #########################读取系统出货表
        print("1.5正在读取系统出货表..")
        screm_total.insert(INSERT, '\n1.5正在读取系统出货表..', '\n')
        window_total.update()
        path_out_time = r'IT补充数据源\系统出货表'
        index = 0
        out = []
        out_time_file = os.listdir(path_out_time)
        for i in out_time_file:
            if '~$' in i:
                out_time_file.remove(i)
        for name in out_time_file:
            if 'csv' in name and '~$' not in name:
                # print('正在读取第' + str(index + 1) + '份采购明细:' + name)
                # screm.insert(INSERT, '\n正在读取第' + str(index + 1) + '份系统验收数据:' + name, '\n')
                # window.update()
                # df = pd.read_excel(os.path.join(dir, name), header=2)
                with open(os.path.join(path_out_time, name), encoding='utf-8', errors='ignore') as f:
                    # 再解决部分报错行如 ParserError：Error tokenizing data.C error:Expected 2 fields in line 407,saw 3.
                    df14 = pd.read_csv(f, sep=',', error_bad_lines=False, low_memory=False, thousands=',')
                out.append(df14)
                index += 1  # 为了查看合并到第几个表格了
            report_outtime = pd.concat(out)

        #########################读取系统验收表
        print("1.6正在读取系统验收表..")
        screm_total.insert(INSERT, '\n1.6正在读取系统验收表..', '\n')
        window_total.update()
        path_rece_time = r'IT补充数据源\系统验收表'
        index = 0
        rece = []
        rece_time_file = os.listdir(path_rece_time)
        for i in rece_time_file:
            if '~$' in i:
                rece_time_file.remove(i)
        for name in rece_time_file:
            if 'csv' in name and '~$' not in name:
                # print('正在读取第' + str(index + 1) + '份采购明细:' + name)
                # screm.insert(INSERT, '\n正在读取第' + str(index + 1) + '份系统验收数据:' + name, '\n')
                # window.update()
                # df = pd.read_excel(os.path.join(dir, name), header=2)
                with open(os.path.join(path_rece_time, name), encoding='utf-8', errors='ignore') as f:
                    # 再解决部分报错行如 ParserError：Error tokenizing data.C error:Expected 2 fields in line 407,saw 3.
                    df11 = pd.read_csv(f, sep=',', error_bad_lines=False, low_memory=False, thousands=',')
                # df11['来源'] = name
                rece.append(df11)
                index += 1  # 为了查看合并到第几个表格了
        report_recetime = pd.concat(rece).reset_index(drop=True)

        ####读取台账验收表和出货表
        #########################读取台账
        print("1.7正在读取台账..")
        screm_total.insert(INSERT, '\n1.7正在读取台账..', '\n')
        window_total.update()
        path_bill_time = r'台账'
        bill_time_file = os.listdir(path_bill_time)
        for i in bill_time_file:
            if '~$' in i:
                bill_time_file.remove(i)
        for name in bill_time_file:
            if 'xlsm' in name and '~$' not in name:
                report_bill_out = pd.read_excel(path_bill_time + '\\' + name, sheet_name='2.出货')[
                    ['大项目号', '项目号', '实际数量', '实际出货日期', '料号']]
                report_bill_rece = pd.read_excel(path_bill_time + '\\' + name, sheet_name='3.验收')[
                    ['大项目号', '项目号', '数量', '终验收时间', '系统录入日期', '料号', '是否预验收']]

        #########################读取历史底表
        print("1.8正在读取历史底表..")
        screm_total.insert(INSERT, '\n1.8正在读取历史底表..', '\n')
        window_total.update()
        filePath_old = r'底表-数据\历史底表'
        old_file = os.listdir(filePath_old)
        for i in old_file:
            if '~$' in i:
                old_file.remove(i)
        for name in old_file:
            if 'xlsx' in name and '~$' not in name:
                report_old = pd.read_excel(filePath_old + '\\' + name)

        time1_end = time.time()
        print('一阶段执行时长:%d秒' % (time1_end - time1_start))
        screm_total.insert(INSERT, '\n一阶段执行时长:%d秒' % (time1_end - time1_start), '\n')
        window_total.update()

        ####################################################################################################################################################二
        time2_start = time.time()
        print("二、数据格式处理..")
        screm_total.insert(INSERT, '\n二、数据格式处理..', '\n')
        window_total.update()
        print("2-1：对照表格式..")
        screm_total.insert(INSERT, '\n2-1：对照表格式..', '\n')
        window_total.update()
        #########################对照表格式
        if '产能' not in item.columns:
            item['产能'] = ''
        if '工艺' not in item.columns:
            item['工艺'] = ''
        if '全面预算有无' not in item.columns:
            item['全面预算有无'] = ''


        if '区域' not in item.columns:
            item['区域'] = ''
        if '行业中心' not in item.columns:
            item['行业中心'] = ''
        if '客户' in item.columns:
            del item['客户']
        item = item.rename(
            columns={'产品项目名称': '设备名称', '大项目号': '线体', '自制否': '自制/外包', '大项目名称': '大项目', '生产数量': '数量', '生产料号': '母件料号',
                     '产品项目号': '核算项目号', '产品线代码': '产品线编码', '客户简称': '客户'})
        if '产品线名称' in item.columns:
            item = item.rename(columns={'产品线名称': '产品线'})
        item = item[
            ['区域', '行业中心', '设备类型', "核算项目号", '子项目号', "线体", "大项目", "设备名称", "产品线", '产品线编码', "产能", "工艺", '客户', '母件料号',
             '自制/外包', '全面预算有无', '数量', 'OA状态', '不核算']]

        print("2-1-1：正在按照对照表字段类型做数据填充..")
        screm_total.insert(INSERT, '\n2-1-1：正在按照对照表字段类型做数据填充..', '\n')
        window_total.update()
        item['线体'] = item['线体'].fillna('/')
        item_str = ['区域', '行业中心', '设备类型', "核算项目号", '子项目号', "线体", "大项目", "设备名称", "产品线", '产品线编码', "产能", "工艺", '客户',
                    '母件料号',
                    '自制/外包', '全面预算有无', 'OA状态', '不核算']
        item[item_str] = item[item_str].fillna('')
        item["核算项目号"] = item["核算项目号"].str.strip()
        item["核算项目号"] = item["核算项目号"].replace(' ', '', regex=True).astype(str)
        item["线体"] = item["线体"].str.strip()
        item["线体"] = item["线体"].replace(' ', '', regex=True).astype(str)
        item["子项目号"] = item["子项目号"].str.strip()
        item["子项目号"] = item["子项目号"].replace(' ', '', regex=True).astype(str)
        item['数量'] = item['数量'].fillna(0)
        ####去掉不核算项目号
        item = item[item['不核算'].astype(str).str.contains('是') == False].reset_index(drop=True)
        item = item[item['设备类型'].astype(str).str.contains('配件') == False].reset_index(drop=True)

        print("2-1-2：转移变更表格式..")
        screm_total.insert(INSERT, '\n2-1-2：转移变更表格式..', '\n')
        window_total.update()
        item_transform['生产数量'] = item_transform['生产数量'].fillna(0)
        item_transform[['大项目号', '大项目名称', '产品线', '产品项目号', '产品项目名称', '设备类型', '生产料号']] = item_transform[
            ['大项目号', '大项目名称', '产品线', '产品项目号', '产品项目名称', '设备类型', '生产料号']].fillna('')
        item_transform = item_transform.rename(
            columns={'产品项目名称': '设备名称', '大项目号': '线体', '大项目名称': '大项目', '生产数量': '数量', '生产料号': '母件料号',
                     '产品项目号': '核算项目号'})
        ##item_transform[['设备名称', '线体','大项目','产品线','数量','母件料号','设备类型','核算项目号']]

        print("2-2：台账拆分表格式..")
        screm_total.insert(INSERT, '\n2-2：台账拆分表格式..', '\n')
        window_total.update()
        #########################台账拆分表格式
        bill_split[['大项目号', '产品项目号']] = bill_split[['大项目号', '产品项目号']].fillna('')
        bill_split[['未税金额_x', '未税金额_y']] = bill_split[['未税金额_x', '未税金额_y']].fillna(0)

        print("2-3： 工单开立表格式..")
        screm_total.insert(INSERT, '\n2-3： 工单开立表格式..', '\n')
        window_total.update()
        #########################工单开立表格式
        report_time_start[['状态码', '大项目号', '工单单号', '项目编号', '关联的一般工单', '生产料号']] = report_time_start[
            ['状态码', '大项目号', '工单单号', '项目编号', '关联的一般工单', '生产料号']].fillna('')
        report_time_start = report_time_start[
            report_time_start['状态码'].astype(str).str.contains('作废') == False].reset_index(drop=True)
        report_time_start['生产数量'] = report_time_start['生产数量'].fillna(0)
        #####去掉604-，26A的工单
        report_time_start = report_time_start[
            report_time_start['工单单号'].astype(str).str.contains('604-|26A-|606-') == False].reset_index(drop=True)  ##602
        report_time_start = report_time_start.rename(columns={'单据日期': '工单开立时间'})

        print("2-4： 工单完工表格式..")
        screm_total.insert(INSERT, '\n2-4： 工单完工表格式..', '\n')
        window_total.update()
        #########################工单完工表格式
        report_time_end[['工单单号', '项目编号']] = report_time_end[['工单单号', '项目编号']].fillna('')
        report_time_end = report_time_end[report_time_end['工单单号'].isin(report_time_start['工单单号'])].reset_index(
            drop=True)
        #####去掉602-、604-，26A的工单
        report_time_end = report_time_end[
            report_time_end['工单单号'].astype(str).str.contains('603-|604-|26A-') == False].reset_index(drop=True)
        report_time_end = report_time_end.rename(columns={'过账日期': '工单完工时间'})
        default_date1 = pd.Timestamp(2090, 1, 1)
        report_time_end['工单完工时间'] = report_time_end['工单完工时间'].fillna(default_date1)
        report_time_end['工单完工时间'] = pd.to_datetime(report_time_end['工单完工时间'], errors='coerce')
        report_time_end['数量'] = report_time_end['数量'].fillna(0)
        report_time_end = report_time_end[report_time_end['数量'] > 0].reset_index(drop=True)

        print("2-5： 系统出货表格式..")
        screm_total.insert(INSERT, '\n2-5： 系统出货表格式..', '\n')
        window_total.update()
        #########################系统出货表格式
        ##扣除内部关联交易数据
        report_outtime['数量'] = report_outtime['数量'].fillna(0)
        report_outtime = report_outtime.rename(columns={'数量': '出货数量', '单据日期': '发货日期'})
        report_outtime[['新客户代码', '大项目号', '项目编号', '料件编号']] = report_outtime[['新客户代码', '大项目号', '项目编号', '料件编号']].fillna('')
        report_outtime = report_outtime[(report_outtime['新客户代码'].astype(str).str.contains(
            '1JS000201|1GD001342|1GD000228|1JS000180|1GD00223|1GD01332|1JS00200|1JS00179') == False) & (
                                                report_outtime['项目编号'].astype(str).str.contains(
                                                    '-R') == False)].reset_index(drop=True)[
            ['大项目号', '项目编号', '发货日期', '料件编号', '出货数量']]
        report_outtime = \
        report_outtime[(report_outtime['料件编号'].astype(str).str[:5].str.contains('311-') == False)].reset_index(
            drop=True)[['大项目号', '项目编号', '发货日期', '出货数量']]
        report_outtime = report_outtime.reset_index(drop=True)
        report_outtime = report_outtime.rename(columns={'发货日期': '系统出货时间'})
        default_date1 = pd.Timestamp(2090, 1, 1)
        report_outtime['系统出货时间'] = report_outtime['系统出货时间'].fillna(default_date1)
        report_outtime['系统出货时间'] = pd.to_datetime(report_outtime['系统出货时间'], errors='coerce')

        print("2-6： 系统验收表格式..")
        screm_total.insert(INSERT, '\n2-6： 系统验收表格式..', '\n')
        window_total.update()
        #########################系统验收表格式
        ##扣除内部关联交易数据
        report_recetime['出货数量'] = report_recetime['出货数量'].fillna(0)
        report_recetime[['新客户代码', '大项目号', '项目编号', '产品编号']] = report_recetime[['新客户代码', '大项目号', '项目编号', '产品编号']].fillna(
            '')
        #####验收做内部关联交易剔除
        report_recetime = report_recetime[(report_recetime['新客户代码'].astype(str).str.contains(
            '1JS000201|1GD001342|1GD000228|1JS000180|1GD00223|1GD01332|1JS00200|1JS00179') == False) & (
                                                  report_recetime['项目编号'].astype(str).str.contains(
                                                      '-R') == False)].reset_index(drop=True)[
            ['大项目号', '项目编号', '扣账日期', '产品编号', '出货数量']]
        report_recetime = \
            report_recetime[(report_recetime['产品编号'].astype(str).str[:5].str.contains('311-') == False)].reset_index(
                drop=True)[
                ['大项目号', '项目编号', '扣账日期', '出货数量']]
        report_recetime = report_recetime.reset_index(drop=True)
        report_recetime = report_recetime.rename(columns={'扣账日期': '系统验收时间'})
        report_recetime['系统验收时间'] = report_recetime['系统验收时间'].fillna(default_date1)
        report_recetime['系统验收时间'] = pd.to_datetime(report_recetime['系统验收时间'], errors='coerce')

        print("2-7： 台账格式..")
        screm_total.insert(INSERT, '\n2-7： 台账格式..', '\n')
        window_total.update()
        #########################台账格式
        report_bill_out[['大项目号', '项目号', '料号']] = report_bill_out[['大项目号', '项目号', '料号']].fillna('')
        report_bill_rece[['大项目号', '项目号', '料号', '是否预验收']] = report_bill_rece[['大项目号', '项目号', '料号', '是否预验收']].fillna('')
        report_bill_out["大项目号"] = report_bill_out["大项目号"].str.strip()
        report_bill_out["大项目号"] = report_bill_out["大项目号"].replace(' ', '', regex=True).astype(str)
        report_bill_out["项目号"] = report_bill_out["项目号"].str.strip()
        report_bill_out["项目号"] = report_bill_out["项目号"].replace(' ', '', regex=True).astype(str)
        report_bill_rece["大项目号"] = report_bill_rece["大项目号"].str.strip()
        report_bill_rece["大项目号"] = report_bill_rece["大项目号"].replace(' ', '', regex=True).astype(str)
        report_bill_rece["项目号"] = report_bill_rece["项目号"].str.strip()
        report_bill_rece["项目号"] = report_bill_rece["项目号"].replace(' ', '', regex=True).astype(str)

        report_bill_out['实际数量'] = report_bill_out['实际数量'].fillna(0)

        report_bill_out = report_bill_out[report_bill_out['料号'].astype(str).str[:5].str.contains('311-') == False][
            ['大项目号', '项目号', '实际数量', '实际出货日期']].reset_index(drop=True)
        default_date1 = pd.Timestamp(2090, 1, 1)
        report_bill_out['实际出货日期'] = report_bill_out['实际出货日期'].fillna(default_date1)
        report_bill_out['实际出货日期'] = pd.to_datetime(report_bill_out['实际出货日期'], errors='coerce')
        report_bill_out = report_bill_out.rename(columns={'实际出货日期': '实际出货时间'})

        report_bill_rece['数量'] = report_bill_rece['数量'].fillna(0)
        report_bill_rece = report_bill_rece[
            report_bill_rece['料号'].astype(str).str[:5].str.contains('311-') == False].reset_index(drop=True)
        default_date1 = pd.Timestamp(2090, 1, 1)
        report_bill_rece['终验收时间'] = report_bill_rece['终验收时间'].fillna(default_date1)
        report_bill_rece['终验收时间'] = pd.to_datetime(report_bill_rece['终验收时间'], errors='coerce')
        report_bill_rece['系统录入日期'] = report_bill_rece['系统录入日期'].fillna(default_date1)
        report_bill_rece['系统录入日期'] = pd.to_datetime(report_bill_rece['系统录入日期'], errors='coerce')
        report_bill_rece['是否预验未终验'] = ''
        report_bill_rece.loc[(report_bill_rece['是否预验收'].str.contains('Y|y')) & (
                    report_bill_rece['终验收时间'] == default_date1), '是否预验未终验'] = '是'
        report_bill_rece.loc[report_bill_rece['终验收时间'] == default_date1, '终验收时间'] = report_bill_rece['系统录入日期']
        del report_bill_rece['系统录入日期']
        report_bill_rece = report_bill_rece.rename(columns={'终验收时间': '实际验收时间'})

        print("2-8： 拆分表格式..")
        screm_total.insert(INSERT, '\n2-8： 拆分表格式..', '\n')
        window_total.update()
        #########################拆分表格式
        bill_split[['大项目号', '产品项目号']] = bill_split[['大项目号', '产品项目号']].fillna('')
        bill_split[['未税金额_x', '未税金额_y']] = bill_split[['未税金额_x', '未税金额_y']].fillna(0)
        bill_split['集团收入'] = bill_split['未税金额_x'] + bill_split['未税金额_y']
        bill_split['大小项目'] = bill_split['大项目号'].astype(str) + bill_split['产品项目号']
        bill_split_group = pd.DataFrame(bill_split.groupby(['大小项目'])['集团收入'].sum()).add_suffix('').reset_index()
        bill_split_group1 = pd.DataFrame(bill_split.groupby(['大小项目'])['未税金额_x'].sum()).add_suffix('').reset_index()
        bill_split_group2 = pd.DataFrame(bill_split.groupby(['大小项目'])['未税金额_y'].sum()).add_suffix('').reset_index()

        print("2-9： 历史底表格式..")
        screm_total.insert(INSERT, '\n2-9： 历史底表格式..', '\n')
        window_total.update()
        report_old = report_old.fillna('')
        #########################历史底表格式
        time2_end = time.time()
        print('二阶段执行时长:%d秒' % (time2_end - time2_start))
        screm_total.insert(INSERT, '\n二阶段执行时长:%d秒' % (time2_end - time2_start), '\n')
        window_total.update()

        ####################################################################################################################################################三
        print("三、数据处理..")
        screm_total.insert(INSERT, '\n三、数据处理..', '\n')
        window_total.update()
        time3_start = time.time()
        print("3-1:对照表..")
        screm_total.insert(INSERT, '\n3-1:对照表..', '\n')
        window_total.update()
        print("3-1-1:正在合并转移变更项目..")
        screm_total.insert(INSERT, '\n3-1-1:正在合并转移变更项目..', '\n')
        window_total.update()
        ##item_transform[['设备名称', '线体','大项目','产品线','数量','母件料号','设备类型','核算项目号']]
        # item = item[['区域','行业中心','设备类型',"核算项目号", '子项目号', "线体", "大项目",  "设备名称", "产品线", '产品线编码', "产能", "工艺", '客户', '母件料号', '自制/外包', '是否预验收',
        #    '全面预算有无', '数量','OA状态','不核算']]

        for i in range(len(item_transform)):
            transform = item[item['核算项目号'] == item_transform.loc[i, '核算项目号']].reset_index(drop=True)
            transform = transform.drop_duplicates(subset=['核算项目号']).reset_index(drop=True)
            for col_name in item_transform.columns:
                transform.loc[0, col_name] = item_transform.loc[i, col_name]
            transform['是否转移变更'] = '是'
            item = pd.concat([item, transform]).reset_index(drop=True)

        print("3-1-2:正在拆解子项目号..")
        screm_total.insert(INSERT, '\n3-1-2:正在拆解子项目号..', '\n')
        window_total.update()
        #####將存在的子項目号单领出来 ##大项目号+产品线编码+产品线做唯一判断，其他取第一个
        item_son = item[item['子项目号'] != ''].reset_index(drop=True)
        ####拆子项目号
        item_son1 = item_son.copy()
        if len(item_son[item_son['子项目号'].str.contains('&')]) > 0:
            item_son['子项目号'] = item_son['子项目号'].str.split('&', expand=True)[0]
        for i in range(len(item_son1)):
            if '&' in item_son1.loc[i, '子项目号']:
                son = item_son1.loc[[i]]
                for j in range(item_son1.loc[i, '子项目号'].count('&')):
                    son['子项目号'] = item_son1.loc[i, '子项目号'].split('&')[j + 1]
                    item_son = pd.concat([item_son, son]).reset_index(drop=True)
        ##置空同一子项目对应不同的大项目号、产品线编码、产品线

        print("3-1-3：正在分配子项目号基础信息..")
        screm_total.insert(INSERT, '\n3-1-3：正在分配子项目号基础信息..', '\n')
        window_total.update()
        for i in range(len(item_son)):
            son = item_son[item_son['子项目号'] == item_son.loc[i, '子项目号']].reset_index(drop=True)
            if len(son.drop_duplicates(subset=['线体']).reset_index(drop=True)) > 1:
                item_son.loc[i, '线体'] = ''
            if len(son.drop_duplicates(subset=['产品线编码']).reset_index(drop=True)) > 1:
                item_son.loc[i, '产品线编码'] = ''
            if len(son.drop_duplicates(subset=['产品线']).reset_index(drop=True)) > 1:
                item_son.loc[i, '产品线'] = ''
        item_son = item_son.sort_values(by=['子项目号'], inplace=False, ascending=True, na_position='last').reset_index(
            drop=True)
        item_son1 = item_son.copy()
        if len(item_son) > 0:
            del item_son['核算项目号']
            item_son = item_son.rename(columns={'子项目号': '核算项目号'})
            item_son = item_son.sort_values(by=['核算项目号'], inplace=False, ascending=True,
                                            na_position='last').reset_index(
                drop=True)
            item_son_use = item_son.drop_duplicates(subset=['核算项目号'], keep='first').reset_index(drop=True)
            item_son_use['数量'] = 1
            item_son_use['大项目'] = item_son_use['大项目'].str.split('-', expand=True)[0] + '-' + \
                                  item_son_use['大项目'].str.split('-', expand=True)[1] + '-' + \
                                  item_son_use['大项目'].str.split('-', expand=True)[2] + '-' + \
                                  item_son_use['大项目'].str.split('-', expand=True)[3]
            item_son_use['来源'] = '子项目号'
            item['来源'] = '核算项目号'
            item_use = pd.concat([item, item_son_use]).reset_index(drop=True)
        if len(item_son) == 0:
            item_use = item.copy()
        if '来源' not in item_use.columns:
            item_use['来源'] = '核算项目号'
        item_use = item_use[
            ['区域', '行业中心', '设备类型', "核算项目号", "线体", "大项目", "设备名称", "产品线", '产品线编码', "产能", "工艺", '客户', '母件料号', '自制/外包',
             '全面预算有无', '数量', 'OA状态', '来源', '是否转移变更']]
        item_str = ['区域', '行业中心', '设备类型', "核算项目号", "线体", "大项目", "设备名称", "产品线", '产品线编码', "产能", "工艺", '客户', '母件料号',
                    '自制/外包',  '全面预算有无', 'OA状态', '是否转移变更']
        item_use[item_str] = item_use[item_str].fillna('')
        item_use['数量'] = item_use['数量'].fillna(0)

        print("3-1-4：正在对对照表做项目号整理..")
        screm_total.insert(INSERT, '\n3-1-4：正在对对照表做项目号整理..', '\n')
        window_total.update()
        if len(item_use[item_use['核算项目号'].str.contains('-')]) > 0:
            item_use['项目号整理'] = ''
            item_use['项目整'] = item_use['核算项目号'].str.split('-', expand=True)[0]
            item_use['项目整1'] = item_use['核算项目号'].str.split('-', expand=True)[1]
            item_use['项目整1'] = item_use['项目整1'].fillna('空值')
            item_use['项目号整理'] = item_use['项目整']
            item_use.loc[(item_use['项目整1'].str.isdigit()) | (item_use['项目整1'].str.contains('SH')), '项目号整理'] = item_use['项目号整理'] + '-' + \
                                                                                                              item_use['项目整1']
        if len(item_use[item_use['核算项目号'].str.contains('-')]) == 0:
            item_use['项目号整理'] = item_use['核算项目号']
        item_use.loc[(item_use['项目号整理'].str[0] == 'F') & (
            item_use['项目号整理'].str[:3].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX')), '项目号整理'] = item_use['项目号整理'].str[3:]
        item_use.loc[
            (item_use['项目号整理'].str[:2].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX', na=False)), '项目号整理'] = \
            item_use['项目号整理'].str[2:]
        item_use['大小项目'] = item_use['线体'].astype(str) + item_use['核算项目号']
        item_use_group = pd.DataFrame(item_use.groupby(['大小项目'])['核算项目号'].count()).add_suffix('-统计').reset_index()
        item_use['计数'] = pd.merge(item_use, item_use_group, on='大小项目', how='left')['核算项目号-统计']

        print("3-1-5：正在将软硬件拆分表拉到对照表..")
        screm_total.insert(INSERT, '\n3-1-5：正在将软硬件拆分表拉到对照表..', '\n')
        window_total.update()
        item_use['集团收入'] = pd.merge(item_use, bill_split_group, on='大小项目', how='left')['集团收入']
        item_use['硬件收入'] = pd.merge(item_use, bill_split_group1, on='大小项目', how='left')['未税金额_x']
        item_use['软件收入'] = pd.merge(item_use, bill_split_group2, on='大小项目', how='left')['未税金额_y']
        item_use['集团收入'] = item_use['集团收入'].fillna(0)
        item_use['软件收入'] = item_use['软件收入'].fillna(0)
        item_use['硬件收入'] = item_use['硬件收入'].fillna(0)

        item_use['集团收入'] = item_use['集团收入'] / (item_use['计数'] * 10000)
        item_use['硬件收入'] = item_use['硬件收入'] / (item_use['计数'] * 10000)
        item_use['软件收入'] = item_use['软件收入'] / (item_use['计数'] * 10000)
        item_use = item_use[
            ['区域', '行业中心', '设备类型', "核算项目号", "线体", "大项目", "设备名称", "产品线", '产品线编码', "产能", "工艺", '客户', '母件料号', '自制/外包',
             '全面预算有无', '数量', '项目号整理', '集团收入', '硬件收入', '软件收入', 'OA状态', '来源', '是否转移变更']]

        report_item_cal_out = item_use[
            ['区域', '行业中心', '设备类型', '客户', '大项目', '线体', '产品线', '产品线编码', '核算项目号', '设备名称', '数量', '项目号整理', '母件料号',
             '全面预算有无', '集团收入', '硬件收入', '软件收入', 'OA状态', '自制/外包', '来源', '是否转移变更']]

        report_item_num_cal = report_item_cal_out.copy()
        report_item_num_cal['线号'] = report_item_num_cal['线体'].astype(str) + '-' + report_item_num_cal['项目号整理']
        report_item_num_cal['线号1'] = report_item_num_cal['线号']
        # report_item_num_cal_total = report_item_num_cal.groupby(['线号'])[['集团收入','软件收入','硬件收入']].sum().add_suffix('汇总').reset_index()
        report_item_num_cal_total = report_item_num_cal.groupby(['线号']).agg(
            {'集团收入': "sum", '软件收入': "sum", '硬件收入': "sum"}).add_suffix('汇总').reset_index()

        print("3-1-6：正在将对照表按数量拆行并重新计算收入..")
        screm_total.insert(INSERT, '\n3-1-6：正在将对照表按数量拆行并重新计算收入..', '\n')
        window_total.update()
        #######成本汇总明细目录:不带增值改造项目（即不带-的），其他的数量有多少就得拆成多少行
        # pd2=pd2.append([d]*5)  #每行复制5倍
        num_list = list(report_item_cal_out['数量'].drop_duplicates())
        for i in num_list:
            if i != 0 and i != 1:
                df = report_item_cal_out[report_item_cal_out['数量'] == i].reset_index(drop=True)
                report_item_cal_out = report_item_cal_out.append([df] * int(i - 1))
        report_item_cal_out['数量'] = 1
        ######################给出线号数量
        report_item_cal_out['线号'] = report_item_cal_out['线体'].astype(str) + '-' + report_item_cal_out['项目号整理']
        report_item_cal_out['线号1'] = report_item_cal_out['线号']
        report_item_num_cal_total_num = report_item_cal_out.groupby(['线号'])[['线号1']].count().add_suffix(
            '数量').reset_index()
        del report_item_cal_out['线号1']
        del report_item_cal_out['线号']
        report_item_num_cal_total['线号数量'] = \
            pd.merge(report_item_num_cal_total, report_item_num_cal_total_num, on='线号', how='left')['线号1数量']
        ###################给出线号数量
        report_item_cal_out = report_item_cal_out.rename(
            columns={'客户': '客户简称', '线体': '大项目号', '大项目': '大项目名称', '产品线': '产品线名称', '数量': '项目数量', '母件料号': "成品料号"})
        ##项目对照表排序：按项目号整理
        report_item_cal_out = report_item_cal_out.sort_values(by=['项目号整理'], inplace=False, ascending=True,
                                                              na_position='last').reset_index(drop=True)
        report_item_cal_out['线号'] = report_item_cal_out['大项目号'].astype(str) + '-' + report_item_cal_out['项目号整理']
        report_item_cal_out[['线号数量', '集团收入', '软件收入', '硬件收入']] = \
            pd.merge(report_item_cal_out, report_item_num_cal_total, on='线号', how='left')[
                ['线号数量', '集团收入汇总', '软件收入汇总', '硬件收入汇总']]
        del report_item_cal_out['线号']
        cal_list = ['集团收入', '软件收入', '硬件收入']
        for i in cal_list:
            report_item_cal_out[i] = report_item_cal_out[i] / report_item_cal_out['线号数量']
        del report_item_cal_out['线号数量']

        #####序列码
        print("3-1-7：正在将对照表生成序列号..")
        screm_total.insert(INSERT, '\n3-1-7：正在将对照表生成序列号..', '\n')
        window_total.update()
        report_item_cal_out = report_item_cal_out.sort_values(by=['大项目号', '项目号整理'], ascending=[True, True]).reset_index(
            drop=True)
        report_item_cal_out['序列号'] = report_item_cal_out['大项目号'].astype(str) + '--' + report_item_cal_out[
            '项目号整理'].astype(str)
        report_item_cal_out['序列号'] = report_item_cal_out['序列号'].replace('/', '', regex=True)
        report_item_cal_out['序列号1'] = report_item_cal_out['序列号'].copy()
        d = 1

        report_item_cal_out.loc[0, '序列号'] = report_item_cal_out.loc[0, '序列号'] + '--' + str(1)
        for i in range(1, len(report_item_cal_out)):
            if report_item_cal_out.loc[i - 1, "序列号1"] == report_item_cal_out.loc[i, "序列号1"]:
                d = d + 1
                report_item_cal_out.loc[i, "序列号"] = report_item_cal_out.loc[i, "序列号"] + '--' + str(d)
            if report_item_cal_out.loc[i - 1, "序列号1"] != report_item_cal_out.loc[i, "序列号1"]:
                d = 1
                report_item_cal_out.loc[i, "序列号"] = report_item_cal_out.loc[i, "序列号"] + '--' + str(d)

        print("3-2:工单开立表..")

        print("3-2-1：正在给工单表做项目号整理..")
        screm_total.insert(INSERT, '\n3-2:工单开立表..', '\n')
        screm_total.insert(INSERT, '\n3-2-1：正在给工单表做项目号整理..', '\n')
        window_total.update()
        if len(report_time_start[report_time_start['项目编号'].str.contains('-')]) > 0:
            report_time_start['项目号整理'] = ''
            report_time_start['项目整'] = report_time_start['项目编号'].str.split('-', expand=True)[0]
            report_time_start['项目整1'] = report_time_start['项目编号'].str.split('-', expand=True)[1]
            report_time_start['项目整1'] = report_time_start['项目整1'].fillna('空值')
            report_time_start['项目号整理'] = report_time_start['项目整']
            report_time_start.loc[
                (report_time_start['项目整1'].str.isdigit()) | (report_time_start['项目整1'].str.contains('SH')), '项目号整理'] = \
                report_time_start['项目号整理'] + '-' + report_time_start['项目整1']
        if len(report_time_start[report_time_start['项目编号'].str.contains('-')]) == 0:
            report_time_start['项目号整理'] = report_time_start['项目编号']
        report_time_start.loc[(report_time_start['项目号整理'].str[0] == 'F') & (
            report_time_start['项目号整理'].str[:3].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX')), '项目号整理'] = \
            report_time_start['项目号整理'].str[3:]
        report_time_start.loc[(report_time_start['项目号整理'].str[:2].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX',
                                                                               na=False)), '项目号整理'] = report_time_start[
                                                                                                          '项目号整理'].str[2:]

        print("3-2-2：正在给工单表做优先级排序..")
        screm_total.insert(INSERT, '\n3-2-2：正在给工单表做优先级排序..', '\n')
        window_total.update()
        report_time_start_cal = report_time_start[
            report_time_start['项目号整理'].isin(report_item_cal_out['项目号整理'])].reset_index(drop=True)
        report_time_start_cal['项目排序'] = 2
        report_time_start_cal['工单排序'] = 15
        report_time_start_cal.loc[report_time_start_cal['项目号整理'].str.contains('LEW') == False, '项目排序'] = 1
        report_time_start_cal.loc[(report_time_start_cal['工单单号'].str.contains('601-')) & (
                report_time_start_cal['项目号整理'].str.contains('LEW') == False), '工单排序'] = 1
        report_time_start_cal.loc[(report_time_start_cal['工单单号'].str.contains('608-')) & (
                report_time_start_cal['项目号整理'].str.contains('LEW') == False), '工单排序'] = 2
        report_time_start_cal.loc[(report_time_start_cal['工单单号'].str.contains('605-')) & (
                report_time_start_cal['项目号整理'].str.contains('LEW') == False), '工单排序'] = 3
        report_time_start_cal.loc[(report_time_start_cal['工单单号'].str.contains('669-')) & (
                report_time_start_cal['项目号整理'].str.contains('LEW') == False), '工单排序'] = 4
        report_time_start_cal.loc[(report_time_start_cal['工单单号'].str.contains('609-')) & (
                report_time_start_cal['项目号整理'].str.contains('LEW') == False), '工单排序'] = 5

        report_time_start_cal.loc[(report_time_start_cal['工单单号'].str.contains('607-')) & (
            report_time_start_cal['项目号整理'].str.contains('LEW')), '工单排序'] = 1
        report_time_start_cal.loc[(report_time_start_cal['工单单号'].str.contains('601-')) & (
            report_time_start_cal['项目号整理'].str.contains('LEW')), '工单排序'] = 2
        report_time_start_cal.loc[(report_time_start_cal['工单单号'].str.contains('603-')) & (
            report_time_start_cal['项目号整理'].str.contains('LEW')), '工单排序'] = 3
        report_time_start_cal.loc[(report_time_start_cal['工单单号'].str.contains('602-')) & (
            report_time_start_cal['项目号整理'].str.contains('LEW')), '工单排序'] = 4
        # report_time_start_cal.loc[(report_time_start_cal['工单单号'].str.contains('602-')) & (report_time_start_cal['项目号整理'].str.contains('LEW')), '工单排序'] = 5
        # report_time_start_cal.loc[(report_time_start_cal['工单单号'].str.contains('609-')) & (report_time_start_cal['项目号整理'].str.contains('LEW') == False), '项目工单排序'] = 5
        report_time_start_cal['项目工单排序'] = report_time_start_cal['项目排序'].astype(str) + report_time_start_cal[
            '工单排序'].astype(
            str)
        report_time_start_cal['项目工单排序'] = report_time_start_cal['项目工单排序'].astype(int)
        del report_time_start_cal['项目排序']
        del report_time_start_cal['工单排序']
        del report_time_start_cal['项目整1']
        del report_time_start_cal['项目整']
        report_time_start_cal = report_time_start_cal.sort_values(by=['项目号整理', '项目工单排序'],
                                                                  ascending=[True, True]).reset_index(drop=True)

        print("3-2-3：正在分离工单表601工单&603工单..")
        screm_total.insert(INSERT, '\n3-2-3：正在分离工单表601工单&603工单..', '\n')
        window_total.update()

        ##一般工单601
        report_time_start_cal2 = report_time_start_cal[(report_time_start_cal['关联的一般工单'] == '') & (
                report_time_start_cal['工单单号'].str.contains('603-') == False)].reset_index(drop=True)
        ##已关联603
        report_time_start_cal1 = report_time_start_cal[(report_time_start_cal['关联的一般工单'] != '') & (
        (report_time_start_cal['工单单号'].str.contains('603-')))].reset_index(drop=True)
        # 未关联603
        report_time_start_cal3 = report_time_start_cal[
            (report_time_start_cal['关联的一般工单'] == '') & (
                report_time_start_cal['工单单号'].str.contains('603-'))].reset_index(
            drop=True)
        ###############已关联的603里删除（603一对多关联601的）
        report_time_start_cal1_1 = report_time_start_cal1[
            report_time_start_cal1['关联的一般工单'].duplicated(keep=False)].reset_index(drop=True)
        report_time_start_cal1 = report_time_start_cal1[
            ~report_time_start_cal1['关联的一般工单'].isin(report_time_start_cal1_1['关联的一般工单'])].reset_index(drop=True)[
            ['工单单号', '关联的一般工单', '生产数量']]
        ##拆行
        print("3-2-4：正在将601工单、603工单（系统已关联601）分别拆行再做关联..")
        screm_total.insert(INSERT, '\n3-2-4：正在将601工单、603工单（系统已关联601）分别拆行再做关联..', '\n')
        window_total.update()
        report_time_start_cal2['需拆行数'] = report_time_start_cal2['生产数量'] - 1
        list_out = list(
            report_time_start_cal2[(report_time_start_cal2['需拆行数'] > 0)]['需拆行数'].drop_duplicates().reset_index(
                drop=True))
        for i in list_out:
            add_row = report_time_start_cal2[report_time_start_cal2['需拆行数'] == i].reset_index(drop=True)
            #    add_row['是否拆行'] = '是'
            for j in range(int(i)):
                report_time_start_cal2 = pd.concat([report_time_start_cal2, add_row]).reset_index(drop=True)
        report_time_start_cal2['原生产数量'] = report_time_start_cal2['生产数量']
        report_time_start_cal2['生产数量'] = 1
        del report_time_start_cal2['需拆行数']

        report_time_start_cal1['需拆行数'] = report_time_start_cal1['生产数量'] - 1
        list_out = list(
            report_time_start_cal1[(report_time_start_cal1['需拆行数'] > 0)]['需拆行数'].drop_duplicates().reset_index(
                drop=True))
        for i in list_out:
            add_row = report_time_start_cal1[report_time_start_cal1['需拆行数'] == i].reset_index(drop=True)
            #    add_row['是否拆行'] = '是'
            for j in range(int(i)):
                report_time_start_cal1 = pd.concat([report_time_start_cal1, add_row]).reset_index(drop=True)
        report_time_start_cal1['生产数量'] = 1
        del report_time_start_cal1['需拆行数']

        ######分别生成拉取字段，唯一值
        report_time_start_cal1 = report_time_start_cal1.sort_values(by=['关联的一般工单']).reset_index(drop=True)
        report_time_start_cal1.loc[0, '排序'] = str(1)
        d = 1
        for i in range(1, len(report_time_start_cal1)):
            if str(report_time_start_cal1.loc[i, '关联的一般工单']) == str(report_time_start_cal1.loc[i - 1, '关联的一般工单']):
                d = d + 1
                report_time_start_cal1.loc[i, '排序'] = str(d)
            if str(report_time_start_cal1.loc[i, '关联的一般工单']) != str(report_time_start_cal1.loc[i - 1, '关联的一般工单']):
                d = 1
                report_time_start_cal1.loc[i, '排序'] = str(d)
        report_time_start_cal1['拉取'] = report_time_start_cal1['关联的一般工单'] + str('-') + report_time_start_cal1['排序']
        del report_time_start_cal1['排序']

        report_time_start_cal2 = report_time_start_cal2.sort_values(by=['工单单号']).reset_index(drop=True)
        report_time_start_cal2.loc[0, '排序'] = str(1)
        d = 1
        for i in range(1, len(report_time_start_cal2)):
            if str(report_time_start_cal2.loc[i, '工单单号']) == str(report_time_start_cal2.loc[i - 1, '工单单号']):
                d = d + 1
                report_time_start_cal2.loc[i, '排序'] = str(d)
            if str(report_time_start_cal2.loc[i, '工单单号']) != str(report_time_start_cal2.loc[i - 1, '工单单号']):
                d = 1
                report_time_start_cal2.loc[i, '排序'] = str(d)
        report_time_start_cal2['拉取'] = report_time_start_cal2['工单单号'] + str('-') + report_time_start_cal2['排序']
        del report_time_start_cal2['排序']
        ########601关联603
        report_time_start_cal2['关联的一般工单'] = \
        pd.merge(report_time_start_cal2, report_time_start_cal1, on='拉取', how='left')['工单单号_y']
        report_time_start_cal2['关联的一般工单'] = report_time_start_cal2['关联的一般工单'].fillna('')
        print("3-2-5：正在处理系统未关联的603的工单..")
        screm_total.insert(INSERT, '\n3-2-5：正在处理系统未关联的603的工单..', '\n')
        window_total.update()
        ####给无家可归的603找妈妈
        ##先把已关联过的601去重并筛选里面未关联的并按数量倒序排序---也就是没有儿子的601
        report_time_start_cal2_1 = report_time_start_cal2.drop_duplicates(subset=['工单单号'], keep='first').reset_index(
            drop=True)
        report_time_start_cal2_1 = report_time_start_cal2_1[
            report_time_start_cal2_1['工单单号'].str.contains('608-|601-|607-')].reset_index(drop=True)
        report_time_start_cal2_1['序'] = 3
        report_time_start_cal2_1.loc[report_time_start_cal2_1['工单单号'].str.contains('601-'), '序'] = 1
        report_time_start_cal2_1.loc[report_time_start_cal2_1['工单单号'].str.contains('608-'), '序'] = 2
        report_time_start_cal2_1 = report_time_start_cal2_1.sort_values(by=['序', '原生产数量'],
                                                                        ascending=[True, False]).reset_index(drop=True)
        del report_time_start_cal2_1['序']
        report_time_start_cal2_1['关联的一般工单'] = report_time_start_cal2_1['关联的一般工单'].fillna('')
        report_time_start_cal2_1 = report_time_start_cal2_1[report_time_start_cal2_1['关联的一般工单'] == ''].reset_index(
            drop=True)
        ######找不到妈妈的603按数量倒序
        report_time_start_cal3 = report_time_start_cal3.sort_values(by=['生产数量'], ascending=False).reset_index(drop=True)
        for i in range(len(report_time_start_cal3)):
            for j in range(len(report_time_start_cal2_1)):
                if report_time_start_cal3.loc[i, "大项目号"] == report_time_start_cal2_1.loc[j, "大项目号"] and \
                        report_time_start_cal3.loc[i, "项目号整理"] == report_time_start_cal2_1.loc[j, "项目号整理"] and \
                        report_time_start_cal3.loc[i, "生产数量"] <= report_time_start_cal2_1.loc[j, "原生产数量"] and \
                        report_time_start_cal2_1.loc[j, "关联的一般工单"] == "":
                    report_time_start_cal2_1.loc[j, "关联的一般工单"] = report_time_start_cal3.loc[i, "工单单号"]
                    break
        report_time_start_cal2_1["关联的一般工单1"] = ''
        for i in range(len(report_time_start_cal3)):
            for j in range(len(report_time_start_cal2_1)):
                if report_time_start_cal3.loc[i, "项目号整理"] == report_time_start_cal2_1.loc[j, "项目号整理"] and \
                        report_time_start_cal3.loc[i, "生产数量"] <= report_time_start_cal2_1.loc[j, "原生产数量"] and \
                        report_time_start_cal2_1.loc[j, "关联的一般工单1"] == "":
                    report_time_start_cal2_1.loc[j, "关联的一般工单1"] = report_time_start_cal3.loc[i, "工单单号"]
                    break
        report_time_start_cal2_1.loc[report_time_start_cal2_1['关联的一般工单'] == '', '关联的一般工单'] = report_time_start_cal2_1[
            '关联的一般工单1']
        ##整理生成新匹配好601-603的关系表,并将关联关系给到找不到妈妈的603
        report_time_start_cal2_1 = \
            report_time_start_cal2_1[report_time_start_cal2_1['关联的一般工单'] != ''].reset_index(drop=True)[
                ['工单单号', '关联的一般工单']]
        ###找不到妈妈的603分配关联一般工单号
        report_time_start_cal3['关联的一般工单'] = \
            pd.merge(report_time_start_cal3, report_time_start_cal2_1, left_on='工单单号', right_on='关联的一般工单', how='left')[
                '工单单号_y']
        report_time_start_cal3['关联的一般工单'] = report_time_start_cal3['关联的一般工单'].fillna('')
        report_time_start_cal3 = report_time_start_cal3[report_time_start_cal3['关联的一般工单'] != ''].reset_index(drop=True)

        print("3-2-6：正在将601工单、603工单（系统未关联601）分别拆行再做关联..")
        screm_total.insert(INSERT, '\n3-2-6：正在将601工单、603工单（系统未关联601）分别拆行再做关联..', '\n')
        window_total.update()
        ##给找到妈妈的603拆并生成拉取字段
        report_time_start_cal3['需拆行数'] = report_time_start_cal3['生产数量'] - 1
        list_out = list(
            report_time_start_cal3[(report_time_start_cal3['需拆行数'] > 0)]['需拆行数'].drop_duplicates().reset_index(
                drop=True))
        for i in list_out:
            add_row = report_time_start_cal3[report_time_start_cal3['需拆行数'] == i].reset_index(drop=True)
            #    add_row['是否拆行'] = '是'
            for j in range(int(i)):
                report_time_start_cal3 = pd.concat([report_time_start_cal3, add_row]).reset_index(drop=True)
        report_time_start_cal3['生产数量'] = 1
        del report_time_start_cal3['需拆行数']

        report_time_start_cal3 = report_time_start_cal3.sort_values(by=['关联的一般工单']).reset_index(drop=True)
        report_time_start_cal3.loc[0, '排序'] = str(1)
        d = 1
        for i in range(1, len(report_time_start_cal3)):
            if str(report_time_start_cal3.loc[i, '关联的一般工单']) == str(report_time_start_cal3.loc[i - 1, '关联的一般工单']):
                d = d + 1
                report_time_start_cal3.loc[i, '排序'] = str(d)
            if str(report_time_start_cal3.loc[i, '关联的一般工单']) != str(report_time_start_cal3.loc[i - 1, '关联的一般工单']):
                d = 1
                report_time_start_cal3.loc[i, '排序'] = str(d)
        report_time_start_cal3['拉取'] = report_time_start_cal3['关联的一般工单'] + str('-') + report_time_start_cal3['排序']
        del report_time_start_cal3['排序']

        ########601拉取新增关联603
        report_time_start_cal2['关联的一般工单1'] = \
        pd.merge(report_time_start_cal2, report_time_start_cal3, on='拉取', how='left')[
            '工单单号_y']
        report_time_start_cal2['关联的一般工单1'] = report_time_start_cal2['关联的一般工单1'].fillna('')
        report_time_start_cal2.loc[report_time_start_cal2['关联的一般工单'] == '', '关联的一般工单'] = report_time_start_cal2[
            '关联的一般工单1']
        del report_time_start_cal2['关联的一般工单1']
        default_date = pd.Timestamp(1990, 1, 1)
        report_time_start_cal2['工单开立时间'] = report_time_start_cal2['工单开立时间'].fillna(default_date)
        report_time_start_cal2['工单开立时间'] = pd.to_datetime(report_time_start_cal2['工单开立时间'], errors='coerce')
        report_time_start_cal2 = report_time_start_cal2.sort_values(by=['项目号整理', '项目工单排序', '工单开立时间'],
                                                                    ascending=[True, True, True]).reset_index(drop=True)

        print("3-3:工单完工表..")
        print("3-3-1：正在给工单完工表做项目号整理..")
        screm_total.insert(INSERT, '\n3-3:工单完工表..', '\n')
        screm_total.insert(INSERT, '\n3-3-1：正在给工单完工表做项目号整理..', '\n')
        window_total.update()
        # 前缀
        if len(report_time_end[report_time_end['项目编号'].str.contains('-')]) > 0:
            report_time_end['项目号整理'] = ''
            report_time_end['项目整'] = report_time_end['项目编号'].str.split('-', expand=True)[0]
            report_time_end['项目整1'] = report_time_end['项目编号'].str.split('-', expand=True)[1]
            report_time_end['项目整1'] = report_time_end['项目整1'].fillna('空值')
            report_time_end['项目号整理'] = report_time_end['项目整']
            report_time_end.loc[
                (report_time_end['项目整1'].str.isdigit()) | (report_time_end['项目整1'].str.contains('SH')), '项目号整理'] = \
                report_time_end['项目号整理'] + '-' + report_time_end['项目整1']
        if len(report_time_end[report_time_end['项目编号'].str.contains('-')]) == 0:
            report_time_end['项目号整理'] = report_time_end['项目编号']
        ##后缀
        report_time_end.loc[(report_time_end['项目号整理'].str[0] == 'F') & (
            report_time_end['项目号整理'].str[:3].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX')), '项目号整理'] = \
            report_time_end['项目号整理'].str[3:]
        report_time_end.loc[
            (report_time_end['项目号整理'].str[:2].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX',
                                                           na=False)), '项目号整理'] = \
            report_time_end['项目号整理'].str[2:]
        ####加快速度，提取目标项目
        report_time_end = report_time_end[report_time_end['项目号整理'].isin(report_item_cal_out['项目号整理'])].reset_index(
            drop=True)
        print("3-3-2：正在给工单完工表做数量拆行..")
        screm_total.insert(INSERT, '\n3-3-2：正在给工单完工表做数量拆行..', '\n')
        window_total.update()
        report_time_end['需拆行数'] = report_time_end['数量'] - 1
        list_out = list(report_time_end[(report_time_end['需拆行数'] > 0)]['需拆行数'].drop_duplicates().reset_index(drop=True))
        for i in list_out:
            add_row = report_time_end[report_time_end['需拆行数'] == i].reset_index(drop=True)
            add_row['是否拆行'] = '是'
            for j in range(int(i)):
                report_time_end = pd.concat([report_time_end, add_row]).reset_index(drop=True)

        print("3-4:系统出货表..")
        print("3-4-1：正在给系统出货表做项目号整理..")
        screm_total.insert(INSERT, '\n3-4:系统出货表..', '\n')
        screm_total.insert(INSERT, '\n3-4-1：正在给系统出货表做项目号整理..', '\n')
        window_total.update()
        ###########项目号整理
        ##后缀
        if len(report_outtime[report_outtime['项目编号'].str.contains('-')]) > 0:
            report_outtime['项目号整理'] = ''
            report_outtime['项目整'] = report_outtime['项目编号'].str.split('-', expand=True)[0]
            report_outtime['项目整1'] = report_outtime['项目编号'].str.split('-', expand=True)[1]
            report_outtime['项目整1'] = report_outtime['项目整1'].fillna('空值')
            report_outtime['项目号整理'] = report_outtime['项目整']
            report_outtime.loc[
                (report_outtime['项目整1'].str.isdigit()) | (report_outtime['项目整1'].str.contains('SH')), '项目号整理'] = \
                report_outtime['项目号整理'] + '-' + report_outtime['项目整1']
        if len(report_outtime[report_outtime['项目编号'].str.contains('-')]) == 0:
            report_outtime['项目号整理'] = report_outtime['项目编号']
        ##前缀
        report_outtime.loc[(report_outtime['项目号整理'].str[0] == 'F') & (
            report_outtime['项目号整理'].str[:3].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX')), '项目号整理'] = \
            report_outtime['项目号整理'].str[3:]
        report_outtime.loc[(report_outtime['项目号整理'].str[:2].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX',
                                                                         na=False)), '项目号整理'] = \
            report_outtime['项目号整理'].str[2:]

        print("3-4-2：正在给系统出货表做数量拆行..")
        screm_total.insert(INSERT, '\n3-4-2：正在给系统出货表做数量拆行..', '\n')
        window_total.update()
        report_outtime = report_outtime[['大项目号', '项目号整理', '系统出货时间', '出货数量']]
        report_outtime = report_outtime[report_outtime['出货数量'] >= 0].reset_index(drop=True)
        report_outtime = report_outtime[
            report_outtime['项目号整理'].isin(report_item_cal_out['项目号整理'])].reset_index(drop=True)
        report_outtime['需拆行数'] = report_outtime['出货数量'] - 1
        list_out = list(report_outtime[(report_outtime['需拆行数'] > 0)]['需拆行数'].drop_duplicates().reset_index(drop=True))
        for i in list_out:
            add_row = report_outtime[report_outtime['需拆行数'] == i].reset_index(drop=True)
            add_row['是否拆行'] = '是'
            for j in range(int(i)):
                report_outtime = pd.concat([report_outtime, add_row]).reset_index(drop=True)
        report_outtime['出货数量'] = 1

        print("3-5:系统验收表..")
        print("3-5-1：正在给系统验收表做项目号整理..")
        screm_total.insert(INSERT, '\n3-5:系统验收表..', '\n')
        screm_total.insert(INSERT, '\n3-5-1：正在给系统验收表做项目号整理..', '\n')
        window_total.update()
        #####验收做项目号整理
        ##前缀
        if len(report_recetime[report_recetime['项目编号'].str.contains('-')]) > 0:
            report_recetime['项目号整理'] = ''
            report_recetime['项目整'] = report_recetime['项目编号'].str.split('-', expand=True)[0]
            report_recetime['项目整1'] = report_recetime['项目编号'].str.split('-', expand=True)[1]
            report_recetime['项目整1'] = report_recetime['项目整1'].fillna('空值')
            report_recetime['项目号整理'] = report_recetime['项目整']
            report_recetime.loc[
                (report_recetime['项目整1'].str.isdigit()) | (report_recetime['项目整1'].str.contains('SH')), '项目号整理'] = \
                report_recetime['项目号整理'] + '-' + report_recetime['项目整1']
        if len(report_recetime[report_recetime['项目编号'].str.contains('-')]) == 0:
            report_recetime['项目号整理'] = report_recetime['项目编号']
        ##后缀
        report_recetime.loc[(report_recetime['项目号整理'].str[0] == 'F') & (
            report_recetime['项目号整理'].str[:3].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX')), '项目号整理'] = \
            report_recetime['项目号整理'].str[3:]
        report_recetime.loc[
            (report_recetime['项目号整理'].str[:2].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX',
                                                           na=False)), '项目号整理'] = \
            report_recetime['项目号整理'].str[2:]

        report_recetime = report_recetime[['大项目号', '项目号整理', '系统验收时间', '出货数量']]
        report_recetime = report_recetime[report_recetime['出货数量'] >= 0].reset_index(drop=True)
        report_recetime = report_recetime[report_recetime['项目号整理'].isin(report_item_cal_out['项目号整理'])].reset_index(
            drop=True)
        report_recetime['需拆行数'] = report_recetime['出货数量'] - 1
        list_rece = list(
            report_recetime[(report_recetime['需拆行数'] > 0)]['需拆行数'].drop_duplicates().reset_index(drop=True))
        print("3-5-2：正在给系统验收表按数量拆行..")
        screm_total.insert(INSERT, '\n3-5-2：正在给系统验收表按数量拆行..', '\n')
        window_total.update()
        for i in list_rece:
            add_row = report_recetime[report_recetime['需拆行数'] == i].reset_index(drop=True)
            add_row['是否拆行'] = '是'
            for j in range(int(i)):
                report_recetime = pd.concat([report_recetime, add_row]).reset_index(drop=True)
        report_recetime['出货数量'] = 1

        print("3-6：台账..")
        print("3-6-1：正在给台账出货\验收做项目号整理..")
        screm_total.insert(INSERT, '\n3-6：台账..', '\n')
        screm_total.insert(INSERT, '\n3-6-1：正在给台账出货\验收做项目号整理...', '\n')
        window_total.update()
        ##前缀
        if len(report_bill_out[report_bill_out['项目号'].str.contains('-')]) > 0:
            report_bill_out['项目号整理'] = ''
            report_bill_out['项目整'] = report_bill_out['项目号'].str.split('-', expand=True)[0]
            report_bill_out['项目整1'] = report_bill_out['项目号'].str.split('-', expand=True)[1]
            report_bill_out['项目整1'] = report_bill_out['项目整1'].fillna('空值')
            report_bill_out['项目号整理'] = report_bill_out['项目整']
            report_bill_out.loc[
                (report_bill_out['项目整1'].str.isdigit()) | (report_bill_out['项目整1'].str.contains('SH')), '项目号整理'] = \
                report_bill_out['项目号整理'] + '-' + report_bill_out['项目整1']
        if len(report_bill_out[report_bill_out['项目号'].str.contains('-')]) == 0:
            report_bill_out['项目号整理'] = report_bill_out['项目号']
        ##后缀
        report_bill_out.loc[(report_bill_out['项目号整理'].str[0] == 'F') & (
            report_bill_out['项目号整理'].str[:3].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX')), '项目号整理'] = \
            report_bill_out['项目号整理'].str[3:]
        report_bill_out.loc[
            (report_bill_out['项目号整理'].str[:2].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX',
                                                           na=False)), '项目号整理'] = report_bill_out['项目号整理'].str[2:]
        report_bill_out = report_bill_out[
            (report_bill_out['实际出货时间'] != pd.Timestamp(2090, 1, 1)) & (report_bill_out['项目号整理'] != '')].reset_index(
            drop=True)
        #####
        report_bill_out = report_bill_out.sort_values(by=['实际出货时间'], ascending=False).reset_index(drop=True)
        ##前缀
        if len(report_bill_rece[report_bill_rece['项目号'].str.contains('-')]) > 0:
            report_bill_rece['项目号整理'] = ''
            report_bill_rece['项目整'] = report_bill_rece['项目号'].str.split('-', expand=True)[0]
            report_bill_rece['项目整1'] = report_bill_rece['项目号'].str.split('-', expand=True)[1]
            report_bill_rece['项目整1'] = report_bill_rece['项目整1'].fillna('空值')
            report_bill_rece['项目号整理'] = report_bill_rece['项目整']
            report_bill_rece.loc[
                (report_bill_rece['项目整1'].str.isdigit()) | (report_bill_rece['项目整1'].str.contains('SH')), '项目号整理'] = \
                report_bill_rece['项目号整理'] + '-' + report_bill_rece['项目整1']
        if len(report_bill_rece[report_bill_rece['项目号'].str.contains('-')]) == 0:
            report_bill_rece['项目号整理'] = report_bill_rece['项目号']
        ##后缀
        report_bill_rece.loc[(report_bill_rece['项目号整理'].str[0] == 'F') & (
            report_bill_rece['项目号整理'].str[:3].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX')), '项目号整理'] = \
            report_bill_rece['项目号整理'].str[3:]
        report_bill_rece.loc[
            (report_bill_rece['项目号整理'].str[:2].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX',
                                                            na=False)), '项目号整理'] = \
            report_bill_rece['项目号整理'].str[2:]

        ###
        report_bill_rece = report_bill_rece[
            (report_bill_rece['实际验收时间'] != pd.Timestamp(2090, 1, 1)) & (report_bill_rece['项目号整理'] != '')].reset_index(
            drop=True)
        report_bill_rece = report_bill_rece.sort_values(by=['实际验收时间'], ascending=False).reset_index(drop=True)

        print("3-6-2：正在给台账出货\验收按数量做拆行..")
        screm_total.insert(INSERT, '\n3-6-2：正在给台账出货\验收按数量做拆行..', '\n')
        window_total.update()
        report_bill_out = report_bill_out[report_bill_out['项目号整理'].isin(report_item_cal_out['项目号整理'])].reset_index(
            drop=True)
        report_bill_out_return = report_bill_out[report_bill_out['实际数量'] < 0].reset_index(drop=True)
        report_bill_out = report_bill_out[report_bill_out['实际数量'] >= 0].reset_index(drop=True)
        default_date1 = pd.Timestamp(2090, 1, 1)
        report_bill_out['实际出货时间'] = report_bill_out['实际出货时间'].fillna(default_date1)
        report_bill_out['实际出货时间'] = pd.to_datetime(report_bill_out['实际出货时间'], errors='coerce')
        report_bill_out['需拆行数'] = report_bill_out['实际数量'] - 1
        list_out = list(report_bill_out[(report_bill_out['需拆行数'] > 0)]['需拆行数'].drop_duplicates().reset_index(drop=True))
        for i in list_out:
            add_row = report_bill_out[report_bill_out['需拆行数'] == i].reset_index(drop=True)
            add_row['是否拆行'] = '是'
            for j in range(int(i)):
                report_bill_out = pd.concat([report_bill_out, add_row]).reset_index(drop=True)
        report_bill_out['实际数量'] = 1

        report_bill_rece = report_bill_rece[report_bill_rece['项目号整理'].isin(report_item_cal_out['项目号整理'])].reset_index(
            drop=True)
        report_bill_rece_return = report_bill_rece[report_bill_rece['数量'] < 0].reset_index(drop=True)
        report_bill_rece = report_bill_rece[report_bill_rece['数量'] >= 0].reset_index(drop=True)
        default_date1 = pd.Timestamp(2090, 1, 1)
        report_bill_rece['实际验收时间'] = report_bill_rece['实际验收时间'].fillna(default_date1)
        report_bill_rece['实际验收时间'] = pd.to_datetime(report_bill_rece['实际验收时间'], errors='coerce')
        report_bill_rece['需拆行数'] = report_bill_rece['数量'] - 1
        list_rece = list(
            report_bill_rece[(report_bill_rece['需拆行数'] > 0)]['需拆行数'].drop_duplicates().reset_index(drop=True))
        for i in list_rece:
            add_row = report_bill_rece[report_bill_rece['需拆行数'] == i].reset_index(drop=True)
            add_row['是否拆行'] = '是'
            for j in range(int(i)):
                report_bill_rece = pd.concat([report_bill_rece, add_row]).reset_index(drop=True)
        report_bill_rece['数量'] = 1
        time3_end = time.time()
        print('三阶段执行时长:%d秒' % (time3_end - time2_start))
        screm_total.insert(INSERT, '\n三阶段执行时长:%d秒' % (time3_end - time2_start), '\n')
        window_total.update()

        ###############################################################################################################################################四
        print("四、数据汇总..(以对照表为底表拉取数据)")
        screm_total.insert(INSERT, '\n四、数据汇总..(以对照表为底表拉取数据)' , '\n')
        window_total.update()
        time4_start = time.time()
        print("4-1:汇总表拉取工单开立数据..")
        print("4-1-1:汇总表排序(大小项目正序)&生成拉取字段..")  ##
        screm_total.insert(INSERT, '\n4-1:汇总表拉取工单开立数据..', '\n')
        screm_total.insert(INSERT, '\n4-1-1:汇总表排序(大小项目正序)&生成拉取字段..', '\n')
        window_total.update()
        report_item_cal_out = report_item_cal_out.sort_values(by=['大项目号', '项目号整理'], ascending=[True, True]).reset_index(drop=True)
        report_item_cal_out.loc[0, '排序1'] = str(1)
        d = 1
        for i in range(1, len(report_item_cal_out)):
            if str(report_item_cal_out.loc[i, '项目号整理']) == str(report_item_cal_out.loc[i - 1, '项目号整理']) and str(
                    report_item_cal_out.loc[i, '大项目号']) == str(report_item_cal_out.loc[i - 1, '大项目号']):
                d = d + 1
                report_item_cal_out.loc[i, '排序1'] = str(d)
            if str(report_item_cal_out.loc[i, '项目号整理']) != str(report_item_cal_out.loc[i - 1, '项目号整理']) or str(
                    report_item_cal_out.loc[i, '大项目号']) != str(report_item_cal_out.loc[i - 1, '大项目号']):
                d = 1
                report_item_cal_out.loc[i, '排序1'] = str(d)
        report_item_cal_out['拉取1'] = report_item_cal_out['大项目号'].astype(str) + report_item_cal_out['项目号整理'] + str('-') + \
                                     report_item_cal_out['排序1']
        del report_item_cal_out['排序1']

        ###工单开立表
        # 排序
        ##第一层：大项目号+项目号
        print("4-1-2:工单开立表排序(大小项目正序)&生成拉取字段..")
        screm_total.insert(INSERT, '\n4-1-2:工单开立表排序(大小项目正序)&生成拉取字段..', '\n')
        window_total.update()
        report_time_start_cal2 = report_time_start_cal2[
            ['工单单号', '工单开立时间', '关联的一般工单', '大项目号', '项目号整理', '项目工单排序']].reset_index(drop=True)
        report_time_start_cal2 = report_time_start_cal2.sort_values(by=['大项目号', '项目号整理', '项目工单排序', '工单开立时间'],
                                                                    ascending=[True, True, True, True]).reset_index(
            drop=True)
        report_time_start_cal2['排序1'] = 0
        # report_item_cal_out_drop=report_item_cal_out_new.drop_duplicates(subset=['核算项目号'])[['核算项目号']].reset_index(drop=True)
        report_time_start_cal2.loc[0, '排序1'] = str(1)
        d1 = 1
        for i in range(1, len(report_time_start_cal2)):
            if str(report_time_start_cal2.loc[i, '项目号整理']) == str(report_time_start_cal2.loc[i - 1, '项目号整理']) and str(
                    report_time_start_cal2.loc[i, '大项目号']) == str(report_time_start_cal2.loc[i - 1, '大项目号']):
                d1 = d1 + 1
                report_time_start_cal2.loc[i, '排序1'] = str(d1)
            if str(report_time_start_cal2.loc[i, '项目号整理']) != str(report_time_start_cal2.loc[i - 1, '项目号整理']) or str(
                    report_time_start_cal2.loc[i, '大项目号']) != str(report_time_start_cal2.loc[i - 1, '大项目号']):
                d1 = 1
                report_time_start_cal2.loc[i, '排序1'] = str(d1)
        report_time_start_cal2['拉取1'] = report_time_start_cal2['大项目号'].astype(str) + report_time_start_cal2[
            '项目号整理'] + str(
            '-') + report_time_start_cal2['排序1']
        del report_time_start_cal2['排序1']

        ##########################################拉到大小项目为条件的第一层数据
        print("4-1-3：汇总表拉取第一层工单数据(大小项目)..")
        screm_total.insert(INSERT, '\n4-1-3：汇总表拉取第一层工单数据(大小项目)..', '\n')
        window_total.update()
        report_item_cal_out[['一般工单号601/608_1', '工单开立时间_1', '返工工单号603_1']] = \
            pd.merge(report_item_cal_out, report_time_start_cal2, on='拉取1', how='left')[['工单单号', '工单开立时间', '关联的一般工单']]
        report_item_cal_out['一般工单号601/608_1'] = report_item_cal_out['一般工单号601/608_1'].fillna('')

        ####汇总表二次排序
        print("4-1-4：汇总表排序(项目号整理)&生成拉取字段..")
        screm_total.insert(INSERT, '\n4-1-4：汇总表排序(项目号整理)&生成拉取字段..', '\n')
        window_total.update()
        report_item_cal_out = report_item_cal_out.sort_values(by=['项目号整理', '一般工单号601/608_1'], ascending=[True, True],
                                                              na_position='first').reset_index(drop=True)
        # report_item_cal_out_new=report_item_cal_out[['核算项目号']]
        # report_item_cal_out['排序']=0

        # report_item_cal_out_drop=report_item_cal_out_new.drop_duplicates(subset=['核算项目号'])[['核算项目号']].reset_index(drop=True)
        report_item_cal_out.loc[0, '排序'] = str(1)
        d = 1
        for i in range(1, len(report_item_cal_out)):
            if str(report_item_cal_out.loc[i, '项目号整理']) == str(report_item_cal_out.loc[i - 1, '项目号整理']):
                d = d + 1
                report_item_cal_out.loc[i, '排序'] = str(d)
            if str(report_item_cal_out.loc[i, '项目号整理']) != str(report_item_cal_out.loc[i - 1, '项目号整理']):
                d = 1
                report_item_cal_out.loc[i, '排序'] = str(d)
        report_item_cal_out['拉取'] = report_item_cal_out['项目号整理'] + str('-') + report_item_cal_out['排序']
        del report_item_cal_out['排序']

        print("4-1-5：工单开立排序(项目号整理)&生成拉取字段..")
        screm_total.insert(INSERT, '\n4-1-5：工单开立排序(项目号整理)&生成拉取字段..', '\n')
        window_total.update()
        report_time_start_cal2 = report_time_start_cal2.sort_values(by=['项目号整理', '项目工单排序', '工单开立时间'],
                                                                    ascending=[True, True, False]).reset_index(
            drop=True)
        report_time_start_cal2_1 = report_time_start_cal2[
            ~report_time_start_cal2['工单单号'].isin(report_item_cal_out['一般工单号601/608_1'])].reset_index(drop=True)
        # report_item_cal_out_new=report_item_cal_out[['核算项目号']]
        report_time_start_cal2_1['排序'] = 0
        # report_item_cal_out_drop=report_item_cal_out_new.drop_duplicates(subset=['核算项目号'])[['核算项目号']].reset_index(drop=True)
        report_time_start_cal2_1.loc[0, '排序'] = str(1)
        d1 = 1
        for i in range(1, len(report_time_start_cal2_1)):
            if str(report_time_start_cal2_1.loc[i, '项目号整理']) == str(report_time_start_cal2_1.loc[i - 1, '项目号整理']):
                d1 = d1 + 1
                report_time_start_cal2_1.loc[i, '排序'] = str(d1)
            if str(report_time_start_cal2_1.loc[i, '项目号整理']) != str(report_time_start_cal2_1.loc[i - 1, '项目号整理']):
                d1 = 1
                report_time_start_cal2_1.loc[i, '排序'] = str(d1)
        report_time_start_cal2_1['拉取'] = report_time_start_cal2_1['项目号整理'] + str('-') + report_time_start_cal2_1['排序']

        print("4-1-6：汇总表拉取第二层工单数据(项目号整理)..")
        screm_total.insert(INSERT, '\n4-1-6：汇总表拉取第二层工单数据(项目号整理)..', '\n')
        window_total.update()
        # 第二层 :项目号整理
        report_item_cal_out[['一般工单号601/608', '工单开立时间', '返工工单号603']] = \
            pd.merge(report_item_cal_out, report_time_start_cal2_1, on='拉取', how='left')[['工单单号', '工单开立时间', '关联的一般工单']]
        report_item_cal_out['返工工单号603_1'] = report_item_cal_out['返工工单号603_1'].fillna('')

        print("4-1-7：汇总表按优先级选择开立工单数据(以大小项目为主)..")
        screm_total.insert(INSERT, '\n4-1-7：汇总表按优先级选择开立工单数据(以大小项目为主)..', '\n')
        window_total.update()

        report_item_cal_out.loc[report_item_cal_out['一般工单号601/608_1'] != '', '一般工单号601/608'] = report_item_cal_out[
            '一般工单号601/608_1']
        report_item_cal_out.loc[report_item_cal_out['一般工单号601/608_1'] != '', '工单开立时间'] = report_item_cal_out['工单开立时间_1']
        report_item_cal_out.loc[report_item_cal_out['一般工单号601/608_1'] != '', '返工工单号603'] = report_item_cal_out[
            '返工工单号603_1']

        # del report_item_cal_out['工单完工时间_1']
        del report_item_cal_out['工单开立时间_1']
        del report_item_cal_out['一般工单号601/608_1']
        del report_item_cal_out['返工工单号603_1']
        default_date1 = pd.Timestamp(2090, 1, 1)
        report_item_cal_out['工单开立时间'] = report_item_cal_out['工单开立时间'].fillna(default_date1)
        report_item_cal_out['工单开立时间'] = pd.to_datetime(report_item_cal_out['工单开立时间'], errors='coerce')
        report_item_cal_out['一般工单号601/608'] = report_item_cal_out['一般工单号601/608'].fillna('')
        report_item_cal_out['返工工单号603'] = report_item_cal_out['返工工单号603'].fillna('')
        report_item_cal_out['核算项目号'] = report_item_cal_out['核算项目号'].fillna('')

        print("4-2:汇总表拉取工单完工数据....")
        print("4-2-1：正在给工单完工表做按数量做拆行..")
        screm_total.insert(INSERT, '\n4-2:汇总表拉取工单完工数据....', '\n')
        screm_total.insert(INSERT, '\n4-2-1：正在给工单完工表做按数量做拆行..', '\n')
        window_total.update()
        report_time_end = report_time_end[report_time_end['工单单号'].isin(report_item_cal_out['一般工单号601/608'])].reset_index(drop=True)
        report_time_end = report_time_end.sort_values(by=['工单单号', '工单完工时间'], ascending=[True, True],na_position='last').reset_index(drop=True)

        print("4-2-2：正在给工单完工表拉取相关字段..")
        screm_total.insert(INSERT, '\n4-2-2：正在给工单完工表拉取相关字段..', '\n')
        window_total.update()
        report_time_end.loc[0, '排序'] = str(1)
        d = 1
        for i in range(1, len(report_time_end)):
            if str(report_time_end.loc[i, '工单单号']) == str(report_time_end.loc[i - 1, '工单单号']):
                d = d + 1
                report_time_end.loc[i, '排序'] = str(d)
            if str(report_time_end.loc[i, '工单单号']) != str(report_time_end.loc[i - 1, '工单单号']):
                d = 1
                report_time_end.loc[i, '排序'] = str(d)
        report_time_end['拉取'] = report_time_end['工单单号'] + str('-') + report_time_end['排序']
        del report_time_end['排序']

        print("4-2-3：正在给汇总表做拉拉取字段..")
        screm_total.insert(INSERT, '\n4-2-3：正在给汇总表做拉拉取字段..', '\n')
        window_total.update()
        report_item_cal_out.loc[0, '排序'] = str(1)
        d = 1
        for i in range(1, len(report_item_cal_out)):
            if str(report_item_cal_out.loc[i, '一般工单号601/608']) == str(report_item_cal_out.loc[i - 1, '一般工单号601/608']):
                d = d + 1
                report_item_cal_out.loc[i, '排序'] = str(d)
            if str(report_item_cal_out.loc[i, '一般工单号601/608']) != str(report_item_cal_out.loc[i - 1, '一般工单号601/608']):
                d = 1
                report_item_cal_out.loc[i, '排序'] = str(d)
        report_item_cal_out['拉取3'] = report_item_cal_out['一般工单号601/608'] + str('-') + report_item_cal_out['排序']
        del report_item_cal_out['排序']

        print("4-2-4：汇总表正在拉取工单完工数据..")
        screm_total.insert(INSERT, '\n4-2-4：汇总表正在拉取工单完工数据..', '\n')
        window_total.update()
        report_item_cal_out['工单完工时间'] = \
            pd.merge(report_item_cal_out, report_time_end, left_on='拉取3', right_on='拉取', how='left')['工单完工时间']
        report_item_cal_out['工单完工时间'] = report_item_cal_out['工单完工时间'].fillna(default_date1)
        report_item_cal_out['工单完工时间'] = pd.to_datetime(report_item_cal_out['工单完工时间'], errors='coerce')

        screm_total.insert(INSERT, '\n4-2-5：工单数据拉取完成之后，根据工单重置底表排序，拉取四个时间字段', '\n')
        report_item_cal_out = report_item_cal_out.sort_values(by=['大项目号', '项目号整理','工单完工时间','工单开立时间'], ascending=[True, True,True, True]).reset_index(
            drop=True)
        report_item_cal_out.loc[0, '排序1'] = str(1)
        d = 1
        for i in range(1, len(report_item_cal_out)):
            if str(report_item_cal_out.loc[i, '项目号整理']) == str(report_item_cal_out.loc[i - 1, '项目号整理']) and str(
                    report_item_cal_out.loc[i, '大项目号']) == str(report_item_cal_out.loc[i - 1, '大项目号']):
                d = d + 1
                report_item_cal_out.loc[i, '排序1'] = str(d)
            if str(report_item_cal_out.loc[i, '项目号整理']) != str(report_item_cal_out.loc[i - 1, '项目号整理']) or str(
                    report_item_cal_out.loc[i, '大项目号']) != str(report_item_cal_out.loc[i - 1, '大项目号']):
                d = 1
                report_item_cal_out.loc[i, '排序1'] = str(d)
        report_item_cal_out['拉取1'] = report_item_cal_out['大项目号'].astype(str) + report_item_cal_out['项目号整理'] + str('-') + \
                                     report_item_cal_out['排序1']
        del report_item_cal_out['排序1']

        print("4-3:汇总表拉取系统出货数据....")
        print("4-3-1：正在给系统出货表做第一层排序(大小项目正序)&生成拉取字段....")
        screm_total.insert(INSERT, '\n4-3:汇总表拉取系统出货数据....', '\n')
        screm_total.insert(INSERT, '\n4-3-1：正在给系统出货表做第一层排序(大小项目正序)&生成拉取字段....', '\n')
        window_total.update()
        ####出货表排序第一层：大项目号+项目号整理
        report_outtime = report_outtime.sort_values(by=['大项目号', '项目号整理', '系统出货时间'], ascending=[True, True, True],
                                                    na_position='last').reset_index(drop=True)
        report_outtime.loc[0, '排序1'] = str(1)
        d1 = 1
        for i in range(1, len(report_outtime)):
            if str(report_outtime.loc[i, '项目号整理']) == str(report_outtime.loc[i - 1, '项目号整理']) and str(
                    report_outtime.loc[i, '大项目号']) == str(report_outtime.loc[i - 1, '大项目号']):
                d1 = d1 + 1
                report_outtime.loc[i, '排序1'] = str(d1)
            if str(report_outtime.loc[i, '项目号整理']) != str(report_outtime.loc[i - 1, '项目号整理']) or str(
                    report_outtime.loc[i, '大项目号']) != str(report_outtime.loc[i - 1, '大项目号']):
                d1 = 1
                report_outtime.loc[i, '排序1'] = str(d1)
        report_outtime['拉取1'] = report_outtime['大项目号'] + report_outtime['项目号整理'] + str('-') + report_outtime['排序1']
        del report_outtime['排序1']

        ###获得优先出货时间
        print("4-3-2：汇总表拉取第一层系统出货数据(大小项目)....")
        screm_total.insert(INSERT, '\n4-3-2：汇总表拉取第一层系统出货数据(大小项目)....', '\n')
        window_total.update()
        report_item_cal_out['系统出货时间1'] = pd.merge(report_item_cal_out, report_outtime, on='拉取1', how='left')['系统出货时间']
        default_date1 = pd.Timestamp(2090, 1, 1)
        report_item_cal_out['系统出货时间1'] = report_item_cal_out['系统出货时间1'].fillna(default_date1)
        report_item_cal_out['系统出货时间1'] = pd.to_datetime(report_item_cal_out['系统出货时间1'], errors='coerce')

        print("4-3-3：正在给系统出货表做第二层排序(项目号整理)&生成拉取字段....")
        screm_total.insert(INSERT, '\n4-3-3：正在给系统出货表做第二层排序(项目号整理)&生成拉取字段....', '\n')
        window_total.update()
        report_outtime['排序'] = 0
        report_outtime_1 = report_outtime[~report_outtime['拉取1'].isin(report_item_cal_out['拉取1'])].reset_index(
            drop=True)
        report_outtime_1 = report_outtime_1.sort_values(by=['项目号整理', '系统出货时间'], ascending=[True, True],
                                                        na_position='last').reset_index(drop=True)

        # report_item_cal_out_drop=report_item_cal_out_new.drop_duplicates(subset=['核算项目号'])[['核算项目号']].reset_index(drop=True)
        report_outtime_1.loc[0, '排序'] = str(1)
        d1 = 1
        for i in range(1, len(report_outtime_1)):
            if str(report_outtime_1.loc[i, '项目号整理']) == str(report_outtime_1.loc[i - 1, '项目号整理']):
                d1 = d1 + 1
                report_outtime_1.loc[i, '排序'] = str(d1)
            if str(report_outtime_1.loc[i, '项目号整理']) != str(report_outtime_1.loc[i - 1, '项目号整理']):
                d1 = 1
                report_outtime_1.loc[i, '排序'] = str(d1)
        report_outtime_1['拉取'] = report_outtime_1['项目号整理'] + str('-') + report_outtime_1['排序']
        del report_outtime_1['排序']

        ###先将汇总表按项目号，系统出货时间1排序
        print("4-3-4：正在给汇总表做第二层排序(项目号整理)&生成拉取字段....")
        screm_total.insert(INSERT, '\n4-3-4：正在给汇总表做第二层排序(项目号整理)&生成拉取字段....', '\n')
        window_total.update()
        report_item_cal_out = report_item_cal_out.sort_values(by=['项目号整理', '系统出货时间1','工单完工时间','工单开立时间'], ascending=[True, False,True,True],
                                                              na_position='last').reset_index(drop=True)
        ###########再将汇总表排序
        report_item_cal_out.loc[0, '排序'] = str(1)
        d = 1
        for i in range(1, len(report_item_cal_out)):
            if str(report_item_cal_out.loc[i, '项目号整理']) == str(report_item_cal_out.loc[i - 1, '项目号整理']):
                d = d + 1
                report_item_cal_out.loc[i, '排序'] = str(d)
            if str(report_item_cal_out.loc[i, '项目号整理']) != str(report_item_cal_out.loc[i - 1, '项目号整理']):
                d = 1
                report_item_cal_out.loc[i, '排序'] = str(d)
        report_item_cal_out['拉取'] = report_item_cal_out['项目号整理'] + str('-') + report_item_cal_out['排序']
        del report_item_cal_out['排序']

        print("4-3-5：汇总表拉取第二层系统出货数据(项目号整理)....")
        screm_total.insert(INSERT, '\n4-3-5：汇总表拉取第二层系统出货数据(项目号整理)....', '\n')
        window_total.update()
        report_item_cal_out['系统出货时间'] = pd.merge(report_item_cal_out, report_outtime_1, on='拉取', how='left')['系统出货时间']

        print("4-3-6：汇总表按优先级选择系统出货数据(以大小项目为主)..")
        screm_total.insert(INSERT, '\n4-3-6：汇总表按优先级选择系统出货数据(以大小项目为主)..', '\n')
        window_total.update()
        default_date1 = pd.Timestamp(2090, 1, 1)
        report_item_cal_out['系统出货时间'] = report_item_cal_out['系统出货时间'].fillna(default_date1)
        report_item_cal_out['系统出货时间'] = pd.to_datetime(report_item_cal_out['系统出货时间'], errors='coerce')
        report_item_cal_out['系统出货时间1'] = report_item_cal_out['系统出货时间1'].fillna(default_date1)
        report_item_cal_out['系统出货时间1'] = pd.to_datetime(report_item_cal_out['系统出货时间1'], errors='coerce')
        report_item_cal_out.loc[report_item_cal_out['系统出货时间1'] != pd.Timestamp(2090, 1, 1), '系统出货时间'] = \
        report_item_cal_out['系统出货时间1']

        print("4-4:汇总表拉取系统验收数据....")
        print("4-4-1：正在给系统验收表做第一层排序(大小项目正序)&生成拉取字段....")
        screm_total.insert(INSERT, '\n4-4:汇总表拉取系统验收数据....', '\n')
        screm_total.insert(INSERT, '\n4-4-1：正在给系统验收表做第一层排序(大小项目正序)&生成拉取字段....', '\n')
        window_total.update()
        report_recetime = report_recetime.sort_values(by=['大项目号', '项目号整理', '系统验收时间'],
                                                      ascending=[True, True, True],
                                                      na_position='last').reset_index(drop=True)
        report_recetime.loc[0, '排序1'] = str(1)
        d1 = 1
        for i in range(1, len(report_recetime)):
            if str(report_recetime.loc[i, '项目号整理']) == str(report_recetime.loc[i - 1, '项目号整理']) and str(
                    report_recetime.loc[i, '大项目号']) == str(report_recetime.loc[i - 1, '大项目号']):
                d1 = d1 + 1
                report_recetime.loc[i, '排序1'] = str(d1)
            if str(report_recetime.loc[i, '项目号整理']) != str(report_recetime.loc[i - 1, '项目号整理']) or str(
                    report_recetime.loc[i, '大项目号']) != str(report_recetime.loc[i - 1, '大项目号']):
                d1 = 1
                report_recetime.loc[i, '排序1'] = str(d1)
        report_recetime['拉取1'] = report_recetime['大项目号'] + report_recetime['项目号整理'] + str('-') + report_recetime['排序1']
        del report_recetime['排序1']

        print("4-4-2：汇总表拉取第一层系统验收数据(大小项目)....")
        screm_total.insert(INSERT, '\n4-4-2：汇总表拉取第一层系统验收数据(大小项目)....', '\n')
        window_total.update()
        report_item_cal_out['系统验收时间1'] = pd.merge(report_item_cal_out, report_recetime, on='拉取1', how='left')['系统验收时间']
        default_date1 = pd.Timestamp(2090, 1, 1)
        report_item_cal_out['系统验收时间1'] = report_item_cal_out['系统验收时间1'].fillna(default_date1)
        report_item_cal_out['系统验收时间1'] = pd.to_datetime(report_item_cal_out['系统验收时间1'], errors='coerce')

        print("4-4-3：正在给系统验收表做第二层排序(项目号整理)&生成拉取字段....")
        screm_total.insert(INSERT, '\n4-4-3：正在给系统验收表做第二层排序(项目号整理)&生成拉取字段....', '\n')
        window_total.update()
        report_recetime_1 = report_recetime[~report_recetime['拉取1'].isin(report_item_cal_out['拉取1'])].reset_index(
            drop=True)
        report_recetime_1 = report_recetime_1.sort_values(by=['项目号整理', '系统验收时间'], ascending=[True, True],
                                                          na_position='last').reset_index(drop=True)
        report_recetime_1.loc[0, '排序'] = str(1)
        d1 = 1
        for i in range(1, len(report_recetime_1)):
            if str(report_recetime_1.loc[i, '项目号整理']) == str(report_recetime_1.loc[i - 1, '项目号整理']):
                d1 = d1 + 1
                report_recetime_1.loc[i, '排序'] = str(d1)
            if str(report_recetime_1.loc[i, '项目号整理']) != str(report_recetime_1.loc[i - 1, '项目号整理']):
                d1 = 1
                report_recetime_1.loc[i, '排序'] = str(d1)
        report_recetime_1['拉取'] = report_recetime_1['项目号整理'] + str('-') + report_recetime_1['排序']
        del report_recetime_1['排序']

        print("4-4-4：正在给汇总表做第二层排序(项目号整理)&生成拉取字段....")
        screm_total.insert(INSERT, '\n4-4-4：正在给汇总表做第二层排序(项目号整理)&生成拉取字段....', '\n')
        window_total.update()
        ###先将汇总表按项目号，大项目号，系统验收时间1排序
        report_item_cal_out = report_item_cal_out.sort_values(by=['项目号整理', '系统验收时间1','工单完工时间','工单开立时间'], ascending=[True, False,True,True],
                                                              na_position='last').reset_index(drop=True)
        ###########再将汇总表排序
        report_item_cal_out.loc[0, '排序'] = str(1)
        d = 1
        for i in range(1, len(report_item_cal_out)):
            if str(report_item_cal_out.loc[i, '项目号整理']) == str(report_item_cal_out.loc[i - 1, '项目号整理']):
                d = d + 1
                report_item_cal_out.loc[i, '排序'] = str(d)
            if str(report_item_cal_out.loc[i, '项目号整理']) != str(report_item_cal_out.loc[i - 1, '项目号整理']):
                d = 1
                report_item_cal_out.loc[i, '排序'] = str(d)
        report_item_cal_out['拉取'] = report_item_cal_out['项目号整理'] + str('-') + report_item_cal_out['排序']
        del report_item_cal_out['排序']

        print("4-4-5：汇总表按优先级选择系统出货数据(以大小项目为主)..")
        screm_total.insert(INSERT, '\n4-4-5：汇总表按优先级选择系统出货数据(以大小项目为主)..', '\n')
        window_total.update()
        report_item_cal_out['系统验收时间'] = pd.merge(report_item_cal_out, report_recetime_1, on='拉取', how='left')['系统验收时间']
        default_date1 = pd.Timestamp(2090, 1, 1)
        report_item_cal_out['系统验收时间'] = report_item_cal_out['系统验收时间'].fillna(default_date1)
        report_item_cal_out['系统验收时间'] = pd.to_datetime(report_item_cal_out['系统验收时间'], errors='coerce')
        report_item_cal_out['系统验收时间1'] = report_item_cal_out['系统验收时间1'].fillna(default_date1)
        report_item_cal_out['系统验收时间1'] = pd.to_datetime(report_item_cal_out['系统验收时间1'], errors='coerce')
        report_item_cal_out.loc[report_item_cal_out['系统验收时间1'] != pd.Timestamp(2090, 1, 1), '系统验收时间'] = report_item_cal_out['系统验收时间1']

        print("4-5:汇总表拉取实际出货数据....")
        print("4-5-1：正在给实际出货表做第一层排序(大小项目正序)&生成拉取字段....")
        screm_total.insert(INSERT, '\n4-5:汇总表拉取实际出货数据....', '\n')
        screm_total.insert(INSERT, '\n4-5-1：正在给实际出货表做第一层排序(大小项目正序)&生成拉取字段....', '\n')
        window_total.update()
        report_bill_out = report_bill_out.sort_values(by=['大项目号', '项目号整理', '实际出货时间'], ascending=[True, True, True],na_position='last').reset_index(drop=True)
        report_bill_out.loc[0, '排序1'] = str(1)
        d1 = 1
        for i in range(1, len(report_bill_out)):
            if str(report_bill_out.loc[i, '项目号整理']) == str(report_bill_out.loc[i - 1, '项目号整理']) and str(
                    report_bill_out.loc[i, '大项目号']) == str(report_bill_out.loc[i - 1, '大项目号']):
                d1 = d1 + 1
                report_bill_out.loc[i, '排序1'] = str(d1)
            if str(report_bill_out.loc[i, '项目号整理']) != str(report_bill_out.loc[i - 1, '项目号整理']) or str(
                    report_bill_out.loc[i, '大项目号']) != str(report_bill_out.loc[i - 1, '大项目号']):
                d1 = 1
                report_bill_out.loc[i, '排序1'] = str(d1)
        report_bill_out['拉取1'] = report_bill_out['大项目号'] + report_bill_out['项目号整理'] + str('-') + report_bill_out['排序1']
        del report_bill_out['排序1']

        print("4-5-2：汇总表拉取第一层实际出货数据(大小项目)....")
        screm_total.insert(INSERT, '\n4-5-2：汇总表拉取第一层实际出货数据(大小项目)....', '\n')
        window_total.update()
        report_item_cal_out['实际出货时间1'] = pd.merge(report_item_cal_out, report_bill_out, on='拉取1', how='left')['实际出货时间']
        default_date1 = pd.Timestamp(2090, 1, 1)
        report_item_cal_out['实际出货时间1'] = report_item_cal_out['实际出货时间1'].fillna(default_date1)
        report_item_cal_out['实际出货时间1'] = pd.to_datetime(report_item_cal_out['实际出货时间1'], errors='coerce')

        print("4-5-3：正在给实际出货表做第二层排序(项目号整理)&生成拉取字段....")
        screm_total.insert(INSERT, '\n4-5-3：正在给实际出货表做第二层排序(项目号整理)&生成拉取字段....', '\n')
        window_total.update()
        report_bill_out_1 = report_bill_out[~report_bill_out['拉取1'].isin(report_item_cal_out['拉取1'])].reset_index(
            drop=True)
        report_bill_out_1 = report_bill_out_1.sort_values(by=['项目号整理', '实际出货时间'], ascending=[True, True],
                                                          na_position='last').reset_index(drop=True)
        report_bill_out_1.loc[0, '排序'] = str(1)
        d1 = 1
        for i in range(1, len(report_bill_out_1)):
            if str(report_bill_out_1.loc[i, '项目号整理']) == str(report_bill_out_1.loc[i - 1, '项目号整理']):
                d1 = d1 + 1
                report_bill_out_1.loc[i, '排序'] = str(d1)
            if str(report_bill_out_1.loc[i, '项目号整理']) != str(report_bill_out_1.loc[i - 1, '项目号整理']):
                d1 = 1
                report_bill_out_1.loc[i, '排序'] = str(d1)
        report_bill_out_1['拉取'] = report_bill_out_1['项目号整理'] + str('-') + report_bill_out_1['排序']
        del report_bill_out_1['排序']

        print("4-5-4：正在给汇总表做第二层排序(项目号整理)&生成拉取字段....")
        screm_total.insert(INSERT, '\n4-5-4：正在给汇总表做第二层排序(项目号整理)&生成拉取字段....', '\n')
        window_total.update()
        ###先将汇总表按项目号，大项目号，实际出货时间1排序
        report_item_cal_out = report_item_cal_out.sort_values(by=['项目号整理', '实际出货时间1','工单完工时间','工单开立时间'], ascending=[True, False,True,True],
                                                              na_position='last').reset_index(drop=True)
        ###########再将汇总表排序
        report_item_cal_out.loc[0, '排序'] = str(1)
        d = 1
        for i in range(1, len(report_item_cal_out)):
            if str(report_item_cal_out.loc[i, '项目号整理']) == str(report_item_cal_out.loc[i - 1, '项目号整理']):
                d = d + 1
                report_item_cal_out.loc[i, '排序'] = str(d)
            if str(report_item_cal_out.loc[i, '项目号整理']) != str(report_item_cal_out.loc[i - 1, '项目号整理']):
                d = 1
                report_item_cal_out.loc[i, '排序'] = str(d)
        report_item_cal_out['拉取'] = report_item_cal_out['项目号整理'] + str('-') + report_item_cal_out['排序']
        del report_item_cal_out['排序']

        print("4-5-5：汇总表按优先级选择实际出货数据(以大小项目为主)..")
        screm_total.insert(INSERT, '\n4-5-5：汇总表按优先级选择实际出货数据(以大小项目为主)..', '\n')
        window_total.update()
        report_item_cal_out['实际出货时间'] = pd.merge(report_item_cal_out, report_bill_out_1, on='拉取', how='left')['实际出货时间']
        default_date1 = pd.Timestamp(2090, 1, 1)
        report_item_cal_out['实际出货时间1'] = report_item_cal_out['实际出货时间1'].fillna(default_date1)
        report_item_cal_out['实际出货时间1'] = pd.to_datetime(report_item_cal_out['实际出货时间1'], errors='coerce')
        report_item_cal_out['实际出货时间'] = report_item_cal_out['实际出货时间'].fillna(default_date1)
        report_item_cal_out['实际出货时间'] = pd.to_datetime(report_item_cal_out['实际出货时间'], errors='coerce')
        report_item_cal_out.loc[report_item_cal_out['实际出货时间1'] != pd.Timestamp(2090, 1, 1), '实际出货时间'] = \
        report_item_cal_out[
            '实际出货时间1']

        print("4-6:汇总表拉取实际验收数据....")
        print("4-6-1：正在给实际验收表做第一层排序(大小项目正序)&生成拉取字段....")
        screm_total.insert(INSERT, '\n4-6:汇总表拉取实际验收数据..', '\n')
        screm_total.insert(INSERT, '\n4-6-1：正在给实际验收表做第一层排序(大小项目正序)&生成拉取字段..', '\n')
        window_total.update()
        report_bill_rece = report_bill_rece.sort_values(by=['大项目号', '项目号整理', '实际验收时间'], ascending=[True, True, True],
                                                        na_position='last').reset_index(drop=True)
        report_bill_rece.loc[0, '排序1'] = str(1)
        d1 = 1
        for i in range(1, len(report_bill_rece)):
            if str(report_bill_rece.loc[i, '项目号整理']) == str(report_bill_rece.loc[i - 1, '项目号整理']) and str(
                    report_bill_rece.loc[i, '大项目号']) == str(report_bill_rece.loc[i - 1, '大项目号']):
                d1 = d1 + 1
                report_bill_rece.loc[i, '排序1'] = str(d1)
            if str(report_bill_rece.loc[i, '项目号整理']) != str(report_bill_rece.loc[i - 1, '项目号整理']) or str(
                    report_bill_rece.loc[i, '大项目号']) != str(report_bill_rece.loc[i - 1, '大项目号']):
                d1 = 1
                report_bill_rece.loc[i, '排序1'] = str(d1)
        report_bill_rece['拉取1'] = report_bill_rece['大项目号'] + report_bill_rece['项目号整理'] + str('-') + report_bill_rece[
            '排序1']
        del report_bill_rece['排序1']

        print("4-6-2：汇总表拉取第一层实际验收数据(大小项目)....")
        screm_total.insert(INSERT, '\n4-6-2：汇总表拉取第一层实际验收数据(大小项目)....', '\n')
        window_total.update()
        report_item_cal_out[['实际验收时间1', '是否YY1', '是否YY未终验1']] = \
        pd.merge(report_item_cal_out, report_bill_rece, on='拉取1', how='left')[['实际验收时间', '是否预验收', '是否预验未终验']]
        default_date1 = pd.Timestamp(2090, 1, 1)
        report_item_cal_out['实际验收时间1'] = report_item_cal_out['实际验收时间1'].fillna(default_date1)
        report_item_cal_out['实际验收时间1'] = pd.to_datetime(report_item_cal_out['实际验收时间1'], errors='coerce')

        print("4-6-3：正在给实际验收表做第二层排序(项目号整理)&生成拉取字段....")
        screm_total.insert(INSERT, '\n4-6-3：正在给实际验收表做第二层排序(项目号整理)&生成拉取字段..', '\n')
        window_total.update()
        report_bill_rece_1 = report_bill_rece[~report_bill_rece['拉取1'].isin(report_item_cal_out['拉取1'])].reset_index(
            drop=True)
        report_bill_rece_1 = report_bill_rece_1.sort_values(by=['项目号整理', '实际验收时间'], ascending=[True, True],
                                                            na_position='last').reset_index(drop=True)
        report_bill_rece_1.loc[0, '排序'] = str(1)
        d1 = 1
        for i in range(1, len(report_bill_rece_1)):
            if str(report_bill_rece_1.loc[i, '项目号整理']) == str(report_bill_rece_1.loc[i - 1, '项目号整理']):
                d1 = d1 + 1
                report_bill_rece_1.loc[i, '排序'] = str(d1)
            if str(report_bill_rece_1.loc[i, '项目号整理']) != str(report_bill_rece_1.loc[i - 1, '项目号整理']):
                d1 = 1
                report_bill_rece_1.loc[i, '排序'] = str(d1)
        report_bill_rece_1['拉取'] = report_bill_rece_1['项目号整理'] + str('-') + report_bill_rece_1['排序']
        del report_bill_rece_1['排序']

        print("4-6-4：正在给汇总表做第二层排序(项目号整理)&生成拉取字段....")
        screm_total.insert(INSERT, '\n4-6-4：正在给汇总表做第二层排序(项目号整理)&生成拉取字段..', '\n')
        window_total.update()
        ###先将汇总表按项目号，大项目号，实际验收时间1排序
        report_item_cal_out = report_item_cal_out.sort_values(by=['项目号整理', '实际验收时间1','工单完工时间','工单开立时间'], ascending=[True, False,True,True],
                                                              na_position='last').reset_index(drop=True)
        ###########再将汇总表排序
        report_item_cal_out.loc[0, '排序'] = str(1)
        d = 1
        for i in range(1, len(report_item_cal_out)):
            if str(report_item_cal_out.loc[i, '项目号整理']) == str(report_item_cal_out.loc[i - 1, '项目号整理']):
                d = d + 1
                report_item_cal_out.loc[i, '排序'] = str(d)
            if str(report_item_cal_out.loc[i, '项目号整理']) != str(report_item_cal_out.loc[i - 1, '项目号整理']):
                d = 1
                report_item_cal_out.loc[i, '排序'] = str(d)
        report_item_cal_out['拉取'] = report_item_cal_out['项目号整理'] + str('-') + report_item_cal_out['排序']
        del report_item_cal_out['排序']

        print("4-6-5：汇总表按优先级选择实际验收数据(以大小项目为主)..")
        screm_total.insert(INSERT, '\n4-6-5：汇总表按优先级选择实际验收数据(以大小项目为主)..', '\n')
        window_total.update()
        report_item_cal_out[['实际验收时间', '是否YY', '是否YY未终验']] = \
        pd.merge(report_item_cal_out, report_bill_rece_1, on='拉取', how='left')[['实际验收时间', '是否预验收', '是否预验未终验']]
        default_date1 = pd.Timestamp(2090, 1, 1)
        report_item_cal_out['实际验收时间1'] = report_item_cal_out['实际验收时间1'].fillna(default_date1)
        report_item_cal_out['实际验收时间1'] = pd.to_datetime(report_item_cal_out['实际验收时间1'], errors='coerce')
        report_item_cal_out['实际验收时间'] = report_item_cal_out['实际验收时间'].fillna(default_date1)
        report_item_cal_out['实际验收时间'] = pd.to_datetime(report_item_cal_out['实际验收时间'], errors='coerce')
        report_item_cal_out.loc[report_item_cal_out['实际验收时间1'] != pd.Timestamp(2090, 1, 1), '实际验收时间'] = \
        report_item_cal_out[
            '实际验收时间1']
        report_item_cal_out.loc[report_item_cal_out['实际验收时间1'] != pd.Timestamp(2090, 1, 1), '是否YY'] = \
        report_item_cal_out['是否YY1']
        report_item_cal_out.loc[report_item_cal_out['实际验收时间1'] != pd.Timestamp(2090, 1, 1), '是否YY未终验'] = \
        report_item_cal_out['是否YY未终验1']

        report_item_cal_out = report_item_cal_out.fillna('')
        time4_end = time.time()
        print('四阶段执行时长:%d秒' % (time4_end - time4_start))
        screm_total.insert(INSERT, '\n四阶段执行时长:%d秒' % (time4_end - time4_start), '\n')
        window_total.update()
        ###########################################################################################################################五
        print("五、数据补充..")
        screm_total.insert(INSERT, '\n五、数据补充..', '\n')
        window_total.update()
        time5_start = time.time()
        print('5.1：判断是否出货、验收销退..')
        screm_total.insert(INSERT, '\n5.1：判断是否出货、验收销退..', '\n')
        report_bill_out_return = report_bill_out_return.drop_duplicates(subset=['项目号整理']).reset_index(drop=True)
        report_bill_rece_return = report_bill_rece_return.drop_duplicates(subset=['项目号整理']).reset_index(drop=True)
        report_item_cal_out['是否出货销退'] = ''
        report_item_cal_out['是否验收销退'] = ''
        report_item_cal_out.loc[report_item_cal_out['项目号整理'].isin(report_bill_out_return['项目号整理']), '是否出货销退'] = '是'
        report_item_cal_out.loc[report_item_cal_out['项目号整理'].isin(report_bill_rece_return['项目号整理']), '是否验收销退'] = '是'

        ###汇总表时间
        print('5.2：清理汇总表时间格式..')
        screm_total.insert(INSERT, '\n5.2：清理汇总表时间格式..', '\n')
        window_total.update()
        report_item_cal_out['工单开立时间'] = pd.to_datetime(report_item_cal_out['工单开立时间'], errors='coerce').dt.strftime(
            '%Y-%m-%d').astype(str)
        report_item_cal_out['工单开立时间'] = ['' if i == '2090-01-01' else i for i in report_item_cal_out['工单开立时间']]
        report_item_cal_out['工单完工时间'] = pd.to_datetime(report_item_cal_out['工单完工时间'], errors='coerce').dt.strftime(
            '%Y-%m-%d').astype(str)
        report_item_cal_out['工单完工时间'] = ['' if i == '2090-01-01' else i for i in report_item_cal_out['工单完工时间']]

        report_item_cal_out['系统出货时间'] = pd.to_datetime(report_item_cal_out['系统出货时间'], errors='coerce').dt.strftime(
            '%Y-%m-%d').astype(str)
        report_item_cal_out['系统出货时间'] = ['' if i == '2090-01-01' else i for i in report_item_cal_out['系统出货时间']]
        # report_item_cal_out['工单完工时间'] = ['' if i == '2090-01-01' else i for i in report_item_cal_out['工单完工时间']]

        report_item_cal_out['系统验收时间'] = pd.to_datetime(report_item_cal_out['系统验收时间'], errors='coerce').dt.strftime(
            '%Y-%m-%d').astype(str)
        report_item_cal_out['系统验收时间'] = ['' if i == '2090-01-01' else i for i in report_item_cal_out['系统验收时间']]

        report_item_cal_out['实际出货时间'] = pd.to_datetime(report_item_cal_out['实际出货时间'], errors='coerce').dt.strftime(
            '%Y-%m-%d').astype(str)
        report_item_cal_out['实际出货时间'] = ['' if i == '2090-01-01' else i for i in report_item_cal_out['实际出货时间']]
        report_item_cal_out['实际验收时间'] = pd.to_datetime(report_item_cal_out['实际验收时间'], errors='coerce').dt.strftime(
            '%Y-%m-%d').astype(str)
        report_item_cal_out['实际验收时间'] = ['' if i == '2090-01-01' else i for i in report_item_cal_out['实际验收时间']]

        ###判断出货验收新增
        print('5.3：拉取需人工固定出货、验收时间..')
        screm_total.insert(INSERT, '\n5.3：拉取需人工固定出货、验收时间..', '\n')
        window_total.update()
        report_item_cal_out['固定'] = ''
        report_old_use = report_old[report_old['固定'].str.contains('是')].reset_index(drop=True)
        for i in range(len(report_old_use)):
            report_item_cal_out.loc[report_item_cal_out['序列号'] == report_old_use.loc[i, '序列号'], '系统出货时间'] = \
            report_old_use.loc[i, '系统出货时间']
            report_item_cal_out.loc[report_item_cal_out['序列号'] == report_old_use.loc[i, '序列号'], '实际出货时间'] = \
            report_old_use.loc[i, '实际出货时间']
            report_item_cal_out.loc[report_item_cal_out['序列号'] == report_old_use.loc[i, '序列号'], '系统验收时间'] = \
            report_old_use.loc[i, '系统验收时间']
            report_item_cal_out.loc[report_item_cal_out['序列号'] == report_old_use.loc[i, '序列号'], '实际验收时间'] = \
            report_old_use.loc[i, '实际验收时间']
            report_item_cal_out.loc[report_item_cal_out['序列号'] == report_old_use.loc[i, '序列号'], '固定'] = '是'

        ####补齐简单字段
        print('5.4：判断设备是否出货、验收..')
        screm_total.insert(INSERT, '\n5.4：判断设备是否出货、验收..', '\n')
        window_total.update()
        report_item_cal_out['项目数量'] = 1
        report_item_cal_out['已出货数量'] = 0
        report_item_cal_out.loc[report_item_cal_out['实际出货时间'] != '', '已出货数量'] = 1
        report_item_cal_out['在产数量'] = report_item_cal_out['项目数量'] - report_item_cal_out['已出货数量']
        report_item_cal_out.loc[report_item_cal_out['来源'].str.contains('子项目'), '已出货数量'] = 0
        report_item_cal_out.loc[report_item_cal_out['来源'].str.contains('子项目'), '在产数量'] = 0
        report_item_cal_out.loc[report_item_cal_out['来源'].str.contains('子项目'), '项目数量'] = 0
        report_item_cal_out['生产状态'] = '在产'
        report_item_cal_out.loc[report_item_cal_out['实际出货时间'] != '', '生产状态'] = '已出货'
        report_item_cal_out.loc[report_item_cal_out['实际验收时间'] != '', '生产状态'] = '已验收'
        report_item_cal_out.loc[report_item_cal_out['来源'].str.contains('子项目'), '生产状态'] = '子项目'

        ######判断子项目状态
        received = report_item_cal_out[report_item_cal_out['生产状态'].str.contains('已验收') & (
                    report_item_cal_out['是否YY未终验'].str.contains('是') == False)].reset_index(drop=True)
        received = received.drop_duplicates(subset=['核算项目号']).reset_index(drop=True)
        item_son1 = item_son1[item_son1['核算项目号'].isin(received['核算项目号'])].reset_index(drop=True)
        report_item_cal_out['子项目状态'] = ''
        report_item_cal_out.loc[report_item_cal_out['核算项目号'].isin(item_son1['子项目号']), '子项目状态'] = '已验收'

        ######判断新增出货
        print('5.5：判断设备是否新增出货、验收..')
        screm_total.insert(INSERT, '\n5.5：判断设备是否新增出货、验收..', '\n')
        window_total.update()
        report_item_cal_out['是否新增出货'] = ''
        report_item_cal_out['历史出货'] = pd.merge(report_item_cal_out, report_old, on='序列号', how='left')['实际出货时间_y']
        report_item_cal_out['历史出货'] = report_item_cal_out['历史出货'].fillna('')
        report_item_cal_out.loc[
            (report_item_cal_out['历史出货'] == '') & (report_item_cal_out['实际出货时间'] != ''), '是否新增出货'] = '是'
        ######判断新增验收
        report_item_cal_out['是否新增验收'] = ''
        report_item_cal_out['历史验收'] = pd.merge(report_item_cal_out, report_old, on='序列号', how='left')['实际验收时间_y']
        report_item_cal_out['历史验收'] = report_item_cal_out['历史验收'].fillna('')
        report_item_cal_out.loc[
            (report_item_cal_out['历史验收'] == '') & (report_item_cal_out['实际验收时间'] != ''), '是否新增验收'] = '是'

        time5_end = time.time()
        print('五阶段执行时长:%d秒' % (time5_end - time5_start))
        screm_total.insert(INSERT, '\n五阶段执行时长:%d秒' % (time5_end - time5_start), '\n')
        window_total.update()
        report_item_cal_out['项目财经'] = ''
        report_item_cal_out['项目财经再分类'] = ''
        report_item_cal_out = report_item_cal_out.reindex(
            columns=['序列号', '区域', '行业中心', '设备类型', "客户简称", "大项目名称", "大项目号", "产品线名称", '产品线编码', "核算项目号", '设备名称', '项目数量',
                     '已出货数量',
                     '在产数量', '生产状态', '集团收入', '软件收入', '硬件收入'
                , '一般工单号601/608', '工单开立时间', '工单完工时间', '系统出货时间', '实际出货时间'
                , '返工工单号603', '系统验收时间', '实际验收时间'
                , '项目号整理', '成品料号', '是否YY', '全面预算有无', 'OA状态', '是否出货销退', '是否验收销退', '固定', '是否新增出货', '是否新增验收', '自制/外包',
                     "项目财经", "项目财经再分类", '是否YY未终验', '是否转移变更', '子项目状态'])
        # 设置表格格式1
        print('六、表格输出...')
        screm_total.insert(INSERT, '\n六、表格输出...', '\n')
        window_total.update()
        time6_start = time.time()

        ######################################################################################################################################六
        def writer_contents(sheet, array, start_row, start_col, format=None, percent_format=None, percentlist=[]):
            # start_col = 0
            for col in array:
                if percentlist and (start_col in percentlist):
                    sheet.write_column(start_row, start_col, col, percent_format)
                else:
                    sheet.write_column(start_row, start_col, col, format)
                start_col += 1

        def write_color(book, sheet, data, fmt, col_num='I'):
            start = 3
            format_red = book.add_format({'font_name': 'Arial',
                                          'font_size': 10,
                                          'bg_color': '#F86470'})
            format_red.set_align('center')
            format_red.set_align('vcenter')

            for item in data:
                if '找不到' in str(item) in str(item):
                    sheet.write(col_num + str(start), item, format_red)
                else:
                    sheet.write(col_num + str(start), item, fmt)
                start += 1

        # 写入表格
        print('正在设置表格格式...')
        screm_total.insert(INSERT, '\n正在设置表格格式...', '\n')
        window_total.update()
        now_time = time.strftime("%Y-%m-%d-%H", time.localtime(time.time()))
        book_name = '底表-数据\核算-进度底表' + now_time
        workbook = xlsxwriter.Workbook(book_name + '.xlsx', {'nan_inf_to_errors': True})
        worksheet1 = workbook.add_worksheet('底表')
        title_format = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 10,
                                            'font_color': 'white',
                                            'bg_color': '#1F4E78',
                                            'bold': True,
                                            'bold': True,
                                            'align': 'center',
                                            'valign': 'vcenter',
                                            'border': 1,
                                            'border_color': 'white'
                                            })
        title_format.set_align('vcenter')
        col_format = workbook.add_format({'font_name': 'Arial',
                                          'font_size': 8,
                                          'font_color': 'white',
                                          'bg_color': '#595959',
                                          'text_wrap': True,
                                          'border': 1,
                                          'border_color': 'white',
                                          'align': 'center',
                                          'valign': 'vcenter'
                                          })

        data_format = workbook.add_format({'font_name': 'Arial',
                                           'font_size': 10,
                                           'align': 'left',
                                           'valign': 'vcenter'
                                           })
        data_format1 = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 10,
                                            'align': 'center',
                                            'valign': 'vcenter'
                                            })
        data_format2 = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 10,
                                            'align': 'center',
                                            'valign': 'vcenter'
                                            })
        data_format2.set_num_format('0.00')
        data_format3 = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 10,
                                            'align': 'center',
                                            'valign': 'vcenter'
                                            })
        data_format3.set_num_format('0.00%')
        num_percent_data_format = workbook.add_format({'font_name': 'Arial',
                                                       'font_size': 10,
                                                       'align': 'center',
                                                       'valign': 'vcenter',
                                                       'num_format': '0.00%'
                                                       })
        statis_format2 = workbook.add_format({'font_name': 'Arial',  # 系列总计
                                              'font_size': 9,
                                              'align': 'center',
                                              'valign': 'vcenter',
                                              'bg_color': '#92CDDC'
                                              })

        print('正在写入EXCEL表格...')
        screm_total.insert(INSERT, '\n正在写入EXCEL表格...', '\n')
        window_total.update()
        worksheet1.write_row("A1", report_item_cal_out.columns, title_format)
        writer_contents(sheet=worksheet1, array=report_item_cal_out.T.values, start_row=1, start_col=0)

        # end = len(report_work1) + 1
        worksheet1.set_row(0, 25)
        worksheet1.set_column('A:A', 16, data_format)
        worksheet1.set_column('F:H', 13, data_format)
        worksheet1.set_column('L:N', 8, data_format1)
        worksheet1.set_column('P:R', 8, data_format1)
        worksheet1.set_column('S:AB', 11, data_format)
        worksheet1.set_column('AC:AJ', 9, data_format)
        print('明细表已写入。。。')
        screm_total.insert(INSERT, '\n明细表已写入。。。', '\n')
        window_total.update()
        workbook.close()
        time6_end = time.time()
        print('六阶段执行时长:%d秒' % (time6_end - time6_start))
        screm_total.insert(INSERT, '\n六阶段执行时长:%d秒' % (time6_end - time6_start), '\n')
        window_total.update()

    except Exception as f:
        # print('异常信息为:', e)  # 异常信息为: division by zero
        print('——#@*&程序报错，异常信息为:' + traceback.format_exc())
        screm_total.insert(INSERT, '\n——#@*&程序报错，异常信息为:' + traceback.format_exc(), '\n')
        window_total.update()

    time_end = time.time()
    print('执行完成！！！！！')
    print('执行总时长:%d秒' % (time_end - time_start))

    screm_total.insert(INSERT, '\n执行完成！！！！' , '\n')
    screm_total.insert(INSERT, '\n执行总时长:%d秒' % (time_end - time_start), '\n')
    window_total.update()
