#coding:utf-8
import zipfile
####################################################抬头
import threading
import pandas as pd
import numpy as np
import time
import os
import xlsxwriter
from pathlib import Path
import warnings
import tkinter as tk
import tkinter.messagebox #弹出框
from tkinter import *
from PIL import Image, ImageTk, ImageSequence
import xlsxwriter
import traceback
import datetime
from tkinter import scrolledtext
from datetime import datetime
from picture import load_image
from wholeuse import*

def total_out():
    '''
    frame_total.create_image(740, 60, image=load_image(r'0、软件附带文件\框子.png'), anchor="n")
    '''
    screm_total = scrolledtext.ScrolledText(frame_total, bg='powderblue',  # 标签背景颜色
                                            highlightthickness=0,
                                            font=('微软雅黑', 12),  # 字体和字体大小
                                            width=72, height=14  # 标签长宽(以字符长度计算)
                                            )
    screm_total_total=frame_total.create_window(740, 246, window=screm_total)
    button_texts.append(screm_total_total)
    time_start = time.time()
    try:
        ###############################################################################################################################################一
        time_start = time.time()
        print("一、数据读取..")

        screm_total.insert(INSERT, '\n一、数据读取..', '\n')
        window_total.update()
        #########################读取核算进度底表
        screm_total.insert(INSERT, '\n1.1：正在读取核算进度底表..', '\n')
        filePath_base = r'7、核算&进度&汇总表底表'
        file_name_base = os.listdir(filePath_base)
        for i in range(len(file_name_base)):
            if str(file_name_base[i]).count('~$') == 0:
                # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
                base = pd.read_excel(filePath_base + '/' + str(file_name_base[i]))

        print("1.2：正在读取概预算表..")
        screm_total.insert(INSERT, '\n1.2：正在读取概预算表..', '\n')
        window_total.update()
        #########################读取概预算表
        path_line = r'4、概预核决汇总-数据\02：概预算'
        index = 0
        line = []
        line_file = os.listdir(path_line)
        for i in line_file:
            if '~$' in i:
                line_file.remove(i)
        for name in line_file:
            if 'xlsx' in name and '~$' not in name:
                budget_estimate = pd.read_excel(path_line + '\\' + name, sheet_name='概预算', header=1)
                year_budget = pd.read_excel(path_line + '\\' + name, sheet_name='年初预算表', header=2)

        print("1.3：正在读取核算汇总表..")
        screm_total.insert(INSERT, '\n1.3：正在读取核算汇总表..', '\n')
        window_total.update()
        #########################读取核算汇总表
        path_calcu = r'4、概预核决汇总-数据\03：核算汇总表'
        calcu_file = os.listdir(path_calcu)
        for i in calcu_file:
            if '~$' in i:
                calcu_file.remove(i)
        calcu_1=[]
        for name in calcu_file:
            if 'xlsx' in name and '~$' not in name:
                cal = pd.read_excel(path_calcu + '\\' + name, header=2)
                calcu_1.append(cal)
        calcu=pd.concat(calcu_1).reset_index(drop=True)

        print("1.4：正在读取海外项目信息管理表..")
        screm_total.insert(INSERT, '\n1.4：正在读取海外项目信息管理表..', '\n')
        window_total.update()
        #########################读取核算汇总表
        path_outproj = r'9、海外-项目信息管理表'
        outproj_file = os.listdir(path_outproj )
        for i in outproj_file:
            if '~$' in i:
                outproj_file.remove(i)
        for name in outproj_file:
            if 'xlsx' in name and '~$' not in name:
                outproj = pd.read_excel(path_outproj  + '\\' + name)

        print("1.5：正在读取BOM明细表..")
        screm_total.insert(INSERT, '\n1.5：正在读取BOM明细表..', '\n')
        window_total.update()
        #########################读取核算汇总表
        path_bom= r'4、概预核决汇总-数据\08：海外-Bom明细表'
        bom_file = os.listdir(path_bom)
        for i in bom_file:
            if '~$' in i:
                bom_file.remove(i)
        bom1=[]
        for name in bom_file:
            if 'xlsx' in name and '~$' not in name:
                bom2 = pd.read_excel(path_bom + '\\' + name)
                bom1.append(bom2)
        bom=pd.concat(bom1).reset_index(drop=True)

        print("1.6：正在读取海外项目看板..")
        screm_total.insert(INSERT, '\n1.6：正在读取海外项目看板..', '\n')
        window_total.update()
        dir_workdeal = r'4、概预核决汇总-数据\09：海外-项目看板'

        filename_workdeal = os.listdir(dir_workdeal)
        for i in filename_workdeal:
            if '~$' in i:
                filename_workdeal.remove(i)
        if os.listdir(dir_workdeal):
            for name in filename_workdeal:
                if 'xlsx' in name and '~$' not in name:
                    workdeal1 = pd.read_excel(dir_workdeal + '\\' + name,sheet_name='Project general plan（Equipment）')[['项目号(Project number)', '计划/实际出货\n(Planned/Actual delivery time)', '计划/实际验收\n(Planned/Actual Acceptance)']]
                    workdeal2 = pd.read_excel(dir_workdeal + '\\' + name, sheet_name='Project general plan（parts）')[
                        ['项目号(Project number)', '计划/实际出货\n(Planned/Actual delivery time)','计划/实际验收\n(Planned/Actual Acceptance)']]
                    workdeal=pd.concat([workdeal1,workdeal2]).reset_index(drop=True) #'计划/实际发货时间', '计划/实际验收时间'
                    workdeal=workdeal.rename(columns={'项目号(Project number)': '项目号','计划/实际出货\n(Planned/Actual delivery time)': '计划/实际发货时间','计划/实际验收\n(Planned/Actual Acceptance)': '计划/实际验收时间'})
        else:
            workdeal = pd.DataFrame(columns=['项目号','计划/实际发货时间','计划/实际验收时间'])

        time_data_read = time.time()
        print('第一阶段【数据读取】执行时长:%d秒' % (time_data_read - time_start))
        screm_total.insert(INSERT, '\n第一阶段【数据读取】执行时长:%d秒' % (time_data_read - time_start), '\n')
        window_total.update()

        ######################################################################################################################################二
        print("二、数据格式处理..")
        screm_total.insert(INSERT, '\n二、数据格式处理..', '\n')
        window_total.update()
        base = base[['序列号', '区域', '行业中心', '设备类型', '客户简称', '大项目名称', '大项目号', '产品线名称', '产品线编码','核算项目号','项目经理', '设备名称', '项目数量'
            , '已出货数量', '在产数量', '生产状态', '集团收入', '软件收入',
                     '硬件收入', '一般工单号601/608', '工单开立时间', '工单完工时间', '系统出货时间', '实际出货时间','返工工单号603',
                     '系统验收时间', '实际验收时间', '项目号整理', '成品料号', '是否YY', '全面预算有无', 'OA状态', '自制/外包', '项目财经','子项目状态']]
        print("2.1：核算进度底表..")
        screm_total.insert(INSERT, '\n2.1：核算进度底表..', '\n')
        window_total.update()
        base_str = ['序列号', '区域', '行业中心', '设备类型', '客户简称', '大项目名称', '大项目号', '产品线名称', '产品线编码',
                    '核算项目号', '设备名称', '生产状态', '一般工单号601/608', '返工工单号603', '项目号整理', '成品料号', '是否YY', '全面预算有无',
                    'OA状态', '自制/外包', '项目财经', '子项目状态']
        base[base_str] = base[base_str].fillna('')
        base['大项目名称'] = base['大项目名称'].str.strip()
        base['大项目名称'] = base['大项目名称'].replace(' ', '', regex=True).astype(str)


        base_num = ['项目数量', '已出货数量', '在产数量', '集团收入', '软件收入', '硬件收入']
        base[base_num] = base[base_num].fillna(0)

        base_date = ['工单开立时间', '工单完工时间', '系统出货时间', '实际出货时间', '系统验收时间', '实际验收时间']
        default_date = pd.Timestamp(1990, 1, 1)

        for dat in base_date:
            base[dat] = base[dat].fillna(default_date)
            base[dat] = pd.to_datetime(base[dat], errors='coerce').dt.strftime('%Y-%m-%d').astype(str)
            base[dat] = ['' if i == '1990-01-01' else i for i in base[dat]]

        print("2.2：概预算表..")
        screm_total.insert(INSERT, '\n2.2：概预算表..', '\n')
        window_total.update()
        budget_estimate_str = ['大项目号', '项目号', '设备名称', '生产料号', '类型']
        budget_estimate[budget_estimate_str] = budget_estimate[budget_estimate_str].fillna('')
        budget_estimate['大项目号'] = budget_estimate['大项目号'].str.strip()
        budget_estimate['大项目号'] = budget_estimate['大项目号'].replace(' ', '', regex=True).astype(str)
        budget_estimate['项目号'] = budget_estimate['项目号'].str.strip()
        budget_estimate['项目号'] = budget_estimate['项目号'].replace(' ', '', regex=True).astype(str)

        budget_estimate_num = ['设备数量', '成本金额', '料', '生产工', '交付工', '设计工', '项目工', '其他', '制费']
        budget_estimate[budget_estimate_num] = budget_estimate[budget_estimate_num].fillna(0)
        estimate = budget_estimate[budget_estimate['类型'].str.contains('概算')].reset_index(drop=True)
        budget = budget_estimate[budget_estimate['类型'].str.contains('预算')].reset_index(drop=True)

        year_budget_str = ['项目号整理', '归属', '客户', '线体', '大项目', '产品线编码', '产品线', '核算项目号', '设备名称-整理',
                           '产能', '自制/外包', '生产主体', '销售主体', '业务', '项目经理', '产品经理', '产品\n类型', '全面预算\n有无']
        year_budget[year_budget_str] = year_budget[year_budget_str].fillna('')
        year_budget['线体'] = year_budget['线体'].str.strip()
        year_budget['线体'] = year_budget['线体'].replace(' ', '', regex=True).astype(str)
        year_budget['核算项目号'] = year_budget['核算项目号'].str.strip()
        year_budget['核算项目号'] = year_budget['核算项目号'].replace(' ', '', regex=True).astype(str)
        year_budget_num = ['数量', '成本合计', '料', '工', '生产工', '交付工', '费', '设计工', '其他费']
        year_budget[year_budget_num] = year_budget[year_budget_num].fillna(0)

        print("2.3：核算汇总表..")
        screm_total.insert(INSERT, '\n2.3：核算汇总表..', '\n')
        window_total.update()
        calcu = calcu[['序列号', '区域', '行业中心', '设备类型', '客户简称', '大项目名称', '大项目号', '产品线名称', '核算项目号',
                       '设备名称', '项目财经', '项目数量', '已出货数量', '在产数量', '生产状态', '集团收入', '软件收入', '硬件收入',
                       '成本', '毛利', '毛利率', '料', '工单料', '设变料', '采购PO', '工', '生产工', '交付工', '费', '设计工', '其他费',
                       '一般工单号601/608', '工单开立时间', '工单完工时间', '系统出货时间', '实际出货时间', '返工工单号603'
            , '系统验收时间', '实际验收时间', '项目号整理', '成品料号', '是否YY', '全面预算有无', 'OA状态','项目阶段','工时(生产交付)','生产工时','交付工时','设计工时']]

        calcu_str = ['序列号', '区域', '行业中心', '设备类型', '客户简称', '大项目名称', '大项目号', '产品线名称'
            , '核算项目号', '设备名称', '项目财经', '生产状态', '一般工单号601/608', '返工工单号603', '项目号整理'
            , '成品料号', '是否YY', '全面预算有无', 'OA状态','项目阶段']
        calcu[calcu_str] = calcu[calcu_str].fillna('')

        calcu_num = ['项目数量', '已出货数量', '在产数量', '集团收入', '软件收入', '硬件收入',
                     '成本', '毛利', '毛利率', '料', '工单料', '设变料', '采购PO', '工', '生产工', '交付工', '费', '设计工', '其他费','工时(生产交付)','生产工时','交付工时','设计工时']
        calcu[calcu_num] = calcu[calcu_num].fillna(0)

        calcu_date = ['工单开立时间', '工单完工时间', '系统出货时间', '实际出货时间', '系统验收时间', '实际验收时间']
        default_date = pd.Timestamp(1990, 1, 1)
        for dat in calcu_date:
            calcu[dat] = calcu[dat].fillna(default_date)
            calcu[dat] = pd.to_datetime(calcu[dat], errors='coerce').dt.strftime('%Y-%m-%d').astype(str)
            calcu[dat] = ['' if i == '1990-01-01' else i for i in calcu[dat]]

        print("2.4：海外信息管理表..")
        screm_total.insert(INSERT, '\n2.4：海外信息管理表..', '\n')
        window_total.update()
        if 'PO号' not in outproj.columns:
            outproj['PO号']=''
        if '概算名称' not in outproj.columns:
            outproj['概算名称']=''
        outproj=outproj[["项目号整理","客户","大项目","线体","产品线","产品线负责人","核算项目号","设备名称-整理","产能","工艺","生产主体","销售主体","业务","项目经理","产品经理","数量","项目财经","大项目名称整理","备注说明","母件料号",'PO号','概算名称']]
        outproj=outproj.fillna('')
        outproj_use=outproj[["项目号整理","产能","生产主体","销售主体","业务","项目经理","产品经理",'PO号','概算名称']]
        outproj_use=outproj_use.fillna('')
        outproj_use=outproj_use.drop_duplicates(subset=['项目号整理']).reset_index(drop=True)

        print("2.5：BOM明细表..")
        screm_total.insert(INSERT, '\n2.5：BOM明细表..', '\n')
        window_total.update()
        bom[['模组用量','最近本币未税采购单价']]=bom[['模组用量','最近本币未税采购单价']].fillna(0)
        bom= bom.fillna('')

        bom['项目号整理'] = ''
        if len(bom[bom['项目号'].str.contains('-')]) > 0:
            bom['项目整'] = bom['项目号'].str.split('-', expand=True)[0]
            bom['项目整1'] = bom['项目号'].str.split('-', expand=True)[1]
            bom['项目整1'] = bom['项目整1'].fillna('空值')
            bom['项目号整理'] = bom['项目整']
            bom.loc[
                (bom['项目整1'].str.isdigit()) | (bom['项目整1'].str.contains('SH')), '项目号整理'] = \
                bom['项目号整理'] + '-' + bom['项目整1']
            del bom['项目整']
            del bom['项目整1']
        else:
            bom['项目号整理'] = bom['项目号']
        bom.loc[(bom['项目号整理'].str[0] == 'F') & (
            bom['项目号整理'].str[:3].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX')), '项目号整理'] = \
            bom['项目号整理'].str[3:]
        bom.loc[(bom['项目号整理'].str[:2].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX',na=False)), '项目号整理'] = bom['项目号整理'].str[2:]
        bom['金额']=bom['模组用量']*bom['最近本币未税采购单价']

        bom1 = bom[["项目号整理", '金额']]
        bom_use =pd.DataFrame(bom1.groupby(['项目号整理'])['金额'].sum()).add_suffix('').reset_index()

        print("2.6：海外项目看板..")
        screm_total.insert(INSERT, '\n2.6：海外项目看板..', '\n')
        window_total.update()
        workdeal=workdeal.dropna()
        workdeal['项目号'] = workdeal['项目号'].fillna('')
        workdeal = workdeal.drop_duplicates(subset=['项目号']).reset_index(drop=True)
        workdeal['项目号'] = workdeal['项目号'].fillna('')
        if len(workdeal[workdeal['项目号'].astype(str).str.contains('-')]) > 0:
            workdeal['项目号整理'] = ''
            workdeal['项目整'] = workdeal['项目号'].str.split('-', expand=True)[0]
            workdeal['项目整1'] = workdeal['项目号'].str.split('-', expand=True)[1]
            workdeal['项目整1'] = workdeal['项目整1'].fillna('空值')
            workdeal['项目号整理'] = workdeal['项目整']
            workdeal.loc[(workdeal['项目整1'].str.isdigit()) | (workdeal['项目整1'].str.contains('SH')), '项目号整理'] =workdeal['项目号整理'] + '-' + workdeal['项目整1']
        if len(workdeal[workdeal['项目号'].str.contains('-')]) == 0:
            workdeal['项目号整理'] = workdeal['项目号']

        workdeal.loc[(workdeal['项目号整理'].str[0] == 'F') & (
            workdeal['项目号整理'].str[:3].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX')), '项目号整理'] =workdeal['项目号整理'].str[3:]
        workdeal.loc[(workdeal['项目号整理'].str[:2].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX',na=False)), '项目号整理'] = workdeal['项目号整理'].str[2:]
        for dat in ['计划/实际发货时间', '计划/实际验收时间']:
            workdeal[dat] = workdeal[dat].fillna(default_date)
            workdeal[dat] = pd.to_datetime(workdeal[dat], errors='coerce')

        time_data_format = time.time()
        print('第二阶段【数据格式处理】执行时长:%d秒' % (time_data_format - time_data_read))
        screm_total.insert(INSERT, '\n第二阶段【数据格式处理】执行时长:%d秒' % (time_data_format - time_data_read), '\n')
        window_total.update()

        ######################################################################################################################################三
        print("三、生成概预核决汇总表..")

        print("3.1：生成概预核决汇总表基础数据(根据底表)..")
        screm_total.insert(INSERT, '\n三、生成概预核决汇总表..', '\n')
        screm_total.insert(INSERT, '\n3.1：生成概预核决汇总表基础数据(根据底表)..', '\n')
        window_total.update()
        total_out = base.copy()
        total_out = total_out[['序列号', '区域', '行业中心', '客户简称', '大项目名称', '大项目号', '产品线名称'
            , '核算项目号', '设备名称','自制/外包', '项目数量', '已出货数量', '在产数量', '生产状态', '全面预算有无', '设备类型', '集团收入'
            , '软件收入', '硬件收入', '成品料号', '一般工单号601/608', '返工工单号603', '实际出货时间'
            , '实际验收时间', '系统验收时间', '项目号整理', '是否YY',  '子项目状态','OA状态','项目财经']]

        print("3.2：拉取概算数据..")
        screm_total.insert(INSERT, '\n3.2：拉取概算数据..', '\n')
        window_total.update()
        total_out['大小项目'] = total_out['大项目号'] + total_out['核算项目号']
        total_out_group = total_out.groupby(['大小项目']).agg({'核算项目号': "count"}).add_suffix('数量').reset_index()
        total_out['大小项目数量'] = pd.merge(total_out, total_out_group, on='大小项目', how='left')['核算项目号数量']

        estimate['大小项目'] = estimate['大项目号'] + estimate['项目号']
        estimate_group = estimate.groupby(['大小项目']).agg(
            {'成本金额': "sum", '料': "sum", '生产工': "sum", '交付工': "sum", '设计工': "sum", '制费': "sum"}).add_suffix(
            '').reset_index()
        total_out[['成本合计-概算', '料-概算', '生产工-概算', '交付工-概算', '设计工-概算', '制费-概算']] = \
            pd.merge(total_out, estimate_group, on='大小项目', how='left')[['成本金额', '料', '生产工', '交付工', '设计工', '制费']]

        estimate_num = ['成本合计-概算', '料-概算', '生产工-概算', '交付工-概算', '设计工-概算', '制费-概算']
        for num in estimate_num:
            total_out[num] = total_out[num] / total_out['大小项目数量']

        print("3.3：拉取预算数据..")
        screm_total.insert(INSERT, '\n3.3：拉取预算数据..', '\n')
        window_total.update()
        budget['大小项目'] = budget['大项目号'] + budget['项目号']
        budget_group = budget.groupby(['大小项目']).agg(
            {'成本金额': "sum", '料': "sum", '生产工': "sum", '交付工': "sum", '设计工': "sum", '制费': "sum"}).add_suffix('').reset_index()

        total_out[['成本合计-预算', '料-预算', '生产工-预算', '交付工-预算', '设计工-预算', '制费-预算']] = \
            pd.merge(total_out, budget_group, on='大小项目', how='left')[['成本金额', '料', '生产工', '交付工', '设计工', '制费']]

        budget_num = ['成本合计-预算', '料-预算', '生产工-预算', '交付工-预算', '设计工-预算', '制费-预算']
        for num in budget_num:
            total_out[num] = total_out[num] / total_out['大小项目数量']


        print("3.4：拉取核算汇总表..")
        screm_total.insert(INSERT, '\n3.5：拉取核算汇总表..', '\n')
        window_total.update()
        calcu_use = calcu[['序列号', '采购PO', '成本', '料', '工单料', '设变料', '工', '生产工', '交付工', '费', '设计工', '其他费', '毛利', '毛利率','工时(生产交付)','生产工时','交付工时','设计工时','项目阶段']]
        calcu_use=calcu_use.drop_duplicates(subset=['序列号']).reset_index(drop=True)
        total_out[
            ['采购PO', '成本合计-核算', '料-核算', '工单料', '设变料', '工-核算', '生产工-核算', '交付工-核算', '费-核算', '设计工-核算', '其他费-核算',
             '毛利-核算','毛利率-核算','工时(生产交付)','生产工时','交付工时','设计工时','项目阶段']] = pd.merge(total_out, calcu_use, on='序列号', how='left')[
            ['采购PO', '成本', '料', '工单料', '设变料', '工', '生产工', '交付工', '费', '设计工', '其他费', '毛利', '毛利率','工时(生产交付)','生产工时','交付工时','设计工时','项目阶段']].fillna('')

        print("3.5：拉取年初预算表..")
        screm_total.insert(INSERT, '\n3.5：拉取年初预算表..', '\n')
        window_total.update()
        year_budget['大小项目'] = year_budget['线体'] + year_budget['核算项目号']
        year_budget_group = year_budget.groupby(['大小项目']).agg(
            {'成本合计': "sum", '料': "sum", '工': "sum", '生产工': "sum", '交付工': "sum", '费': "sum", '设计工': "sum",
             '其他费': "sum"}).add_suffix('').reset_index()
        total_out[['成本合计-年初', '料-年初', '工-年初', '生产工-年初', '交付工-年初', '费-年初', '设计工-年初', '其他费-年初']] = \
            pd.merge(total_out, year_budget_group, on='大小项目', how='left')[
                ['成本合计', '料', '工', '生产工', '交付工', '费', '设计工', '其他费']]
        year_budget_num = ['成本合计-年初', '料-年初', '工-年初', '生产工-年初', '交付工-年初', '费-年初', '设计工-年初', '其他费-年初']
        for num in year_budget_num:
            total_out[num] = total_out[num] / total_out['大小项目数量']

        print("3.6：拉取海外项目信息表..")
        screm_total.insert(INSERT, '\n3.6：拉取海外项目信息表..', '\n')
        window_total.update()

        total_out[["生产主体","销售主体","业务","项目经理","产品经理","产能",'PO号','概算名称']]=pd.merge(total_out, outproj_use, on='项目号整理', how='left')[
                ["生产主体","销售主体","业务","项目经理","产品经理","产能",'PO号','概算名称']]
        total_out[["生产主体", "销售主体", "业务", "项目经理", "产品经理", "产能",'PO号','概算名称']]=total_out[["生产主体","销售主体","业务","项目经理","产品经理","产能",'PO号','概算名称']].fillna('')

        print("3.7：拉取BOM金额..")
        screm_total.insert(INSERT, '\n3.7：拉取bom金额..', '\n')
        window_total.update()
        total_out['Bom金额']=pd.merge(total_out, bom_use, on='项目号整理', how='left')[["金额"]]
        total_out['Bom金额']=total_out['Bom金额'].fillna(0)

        print("3.8：拉取海外项目看板..")
        screm_total.insert(INSERT, '\n3.8：拉取海外项目计划..', '\n')
        window_total.update()
        total_out[['计划出货时间','计划验收时间']] = pd.merge(total_out, workdeal, on='项目号整理', how='left')[['计划/实际发货时间', '计划/实际验收时间']]
        for dat in ['计划出货时间','计划验收时间']:
            total_out[dat] = total_out[dat].fillna(default_date)
            total_out[dat] = pd.to_datetime(total_out[dat], errors='coerce').dt.strftime(
                '%Y-%m-%d').astype(str)
            total_out[dat]= ['' if i == '1990-01-01' else i for i in total_out[dat]]

        time_data_catch = time.time()
        print('第三阶段【数据拉取】执行时长:%d秒' % (time_data_catch - time_data_format))

        ######################################################################################################################################四
        print("四、汇总表字段加工..")
        screm_total.insert(INSERT, '\n四、汇总表字段加工..', '\n')
        window_total.update()

        summary=total_out.copy()
        summary[['成本合计-还需', '料-还需', '工-还需', '生产工-还需', '交付工-还需', '费-还需', '设计工-还需', '其他费-还需']]=''
        summary_num = ['成本合计-概算', '料-概算', '生产工-概算', '交付工-概算', '设计工-概算', '制费-概算'
            , '成本合计-预算', '料-预算', '生产工-预算', '交付工-预算', '设计工-预算', '制费-预算'
            , '采购PO', '成本合计-核算', '料-核算', '工单料', '设变料', '工-核算', '生产工-核算', '交付工-核算', '费-核算', '设计工-核算', '其他费-核算', '毛利-核算','毛利率-核算'
            , '成本合计-年初', '料-年初', '工-年初', '生产工-年初', '交付工-年初', '费-年初', '设计工-年初', '其他费-年初'
            , '成本合计-还需', '料-还需', '工-还需', '生产工-还需', '交付工-还需', '费-还需', '设计工-还需', '其他费-还需'
            ,'Bom金额','工时(生产交付)','生产工时','交付工时','设计工时']
        summary[summary_num] = summary[summary_num].fillna('')

        for col in summary_num:
            if '概算' in col  or '还需' in col or '预算' in col:
                summary.loc[summary['OA状态'].str.contains('不'),col]=0

        print("4.1：加工出货&验收年月..")
        screm_total.insert(INSERT, '\n4.1：加工出货&验收年月..', '\n')
        window_total.update()


        print("4.2：滚动预测..")  ##核算+还需
        screm_total.insert(INSERT, '\n4.2：滚动预测..', '\n')
        window_total.update()
        summary['成本合计-滚动'] = ''
        summary['料-滚动'] = ''
        summary['工-滚动'] = ''
        summary['生产工-滚动'] = ''
        summary['交付工-滚动'] = ''
        summary['费-滚动'] = ''
        summary['设计工-滚动'] = ''
        summary['其他费-滚动'] = ''


        print("4.3：毛利&毛利率..")
        screm_total.insert(INSERT, '\n4.3：毛利&毛利率..', '\n')
        window_total.update()
        summary['毛利-概算'] = ''
        summary['毛利率-概算'] = ''
        for i in range(len(summary)):
            if summary.loc[i, "集团收入"] != '' and summary.loc[i, "成本合计-概算"] != '':
                summary.loc[i, "毛利-概算"] = summary.loc[i, '集团收入'] - summary.loc[i, '成本合计-概算']
        for i in range(len(summary)):
            if summary.loc[i, "集团收入"] != '' and summary.loc[i, "集团收入"] != 0 and summary.loc[i, "毛利-概算"] != '':
                summary.loc[i, "毛利率-概算"] = summary.loc[i, '毛利-概算'] / summary.loc[i, '集团收入']

        summary['毛利-预算'] = ''
        summary['毛利率-预算'] = ''
        for i in range(len(summary)):
            if summary.loc[i, "集团收入"] != '' and summary.loc[i, "成本合计-预算"] != '':
                summary.loc[i, "毛利-预算"] = summary.loc[i, '集团收入'] - summary.loc[i, '成本合计-预算']
        for i in range(len(summary)):
            if summary.loc[i, "集团收入"] != '' and summary.loc[i, "集团收入"] != 0 and summary.loc[i, "毛利-预算"] != '':
                summary.loc[i, "毛利率-预算"] = summary.loc[i, '毛利-预算'] / summary.loc[i, '集团收入']

        summary['毛利-核算'] = ''
        summary['毛利率-核算'] = ''
        for i in range(len(summary)):
            if summary.loc[i, "集团收入"] != '' and summary.loc[i, "成本合计-核算"] != '':
                summary.loc[i, "毛利-核算"] = summary.loc[i, '集团收入'] - summary.loc[i, '成本合计-核算']
        for i in range(len(summary)):
            if summary.loc[i, "集团收入"] != '' and summary.loc[i, "集团收入"] != 0 and summary.loc[i, "毛利-核算"] != '':
                summary.loc[i, "毛利率-核算"] = summary.loc[i, '毛利-核算'] / summary.loc[i, '集团收入']

        summary['毛利-滚动'] = ''
        summary['毛利率-滚动'] = ''

        print("4.3：整理格式..")
        screm_total.insert(INSERT, '\n4.3：整理格式..', '\n')
        window_total.update()
        summary['事业部收入'] = ''
        summary['大项目整理'] = ''
        summary['是否在手订单'] = ''
        summary['工-概算']=summary['生产工-概算']+summary['交付工-概算']
        summary['费-概算'] = summary['设计工-概算'] + summary['制费-概算']
        summary['工-预算'] = summary['生产工-预算'] + summary['交付工-预算']
        summary['费-预算'] = summary['设计工-预算'] + summary['制费-预算']

        summary[['系统发货月份','系统验收月份','备注','收入差异','预算是否锁定','验收状态']]=''
        summary[['成本合计','料','工','生产工','交付工','费','设计工','其他费', '预算出货时间', '预算验收时间', '是否有风险','原因分类','进度情况','工单开立时间','工单完工时间','生产周期-天数','验收周期-按月','设备总数量','差异BOM-预算料','BOM备注']] = ''
        summary = summary.reindex(
            columns=['序列号', '客户简称', '大项目名称', '大项目号', '产品线名称', '核算项目号','设备名称', "生产主体", "销售主体", "业务", "项目经理", "产品经理", "产能", '自制/外包'
                , '设备类型', '全面预算有无', '项目数量' , '已出货数量', '在产数量', '生产状态', '项目阶段','系统发货月份','系统验收月份','备注'
                ,  '事业部收入', '集团收入','收入差异','预算是否锁定','验收状态', '是否YY'
                , '成本合计-概算', '料-概算', '工-概算','生产工-概算', '交付工-概算','费-概算', '设计工-概算', '制费-概算', '毛利-概算','毛利率-概算'
                , '成本合计-预算', '料-预算','工-预算', '生产工-预算', '交付工-预算','费-预算', '设计工-预算', '制费-预算', '毛利-预算','毛利率-预算'
                , '成本合计-核算', '料-核算' , '采购PO',  '工-核算', '生产工-核算', '交付工-核算','费-核算', '设计工-核算', '其他费-核算', '毛利-核算', '毛利率-核算'
                , '成本合计-还需', '料-还需', '工-还需', '生产工-还需', '交付工-还需', '费-还需', '设计工-还需','其他费-还需'
                , '成本合计-滚动', '料-滚动', '工-滚动', '生产工-滚动', '交付工-滚动', '费-滚动', '设计工-滚动', '其他费-滚动', '毛利-滚动', '毛利率-滚动'
                , '成本合计','料','工','生产工','交付工','费','设计工','其他费','概算名称','PO号', '项目号整理'
                , '预算出货时间','计划出货时间', '实际出货时间', '预算验收时间','计划验收时间', '实际验收时间', '系统验收时间'
                , '是否有风险','原因分类','进度情况', '成品料号', '一般工单号601/608', '返工工单号603','工单开立时间','工单完工时间'
                , '生产周期-天数', '验收周期-按月', '设备总数量', '项目财经', '大项目整理'
                , '成本合计-年初', '料-年初', '工-年初', '生产工-年初', '交付工-年初', '费-年初', '设计工-年初','其他费-年初'
                , 'Bom金额','差异BOM-预算料','BOM备注'
                , '区域', '行业中心', '软件收入', '硬件收入', '是否在手订单', 'OA状态', '工时(生产交付)', '生产工时', '交付工时', '设计工时'])
        '''
        summary = summary.reindex(columns=['序列号', '区域', '行业中心', '客户简称', '大项目名称', '大项目号', '产品线名称', '核算项目号', '设备名称'
            ,"生产主体","销售主体","业务","项目经理","产品经理","产能"
            , '自制/外包','项目数量', '已出货数量', '在产数量', '生产状态'
            , '设备类型','全面预算有无', '项目阶段', '事业部收入', '集团收入', '软件收入', '硬件收入'
            , '成本合计-概算', '料-概算', '生产工-概算', '交付工-概算', '设计工-概算', '制费-概算', '毛利-概算', '毛利率-概算'
            , '成本合计-预算', '料-预算', '生产工-预算', '交付工-预算', '设计工-预算', '制费-预算', '毛利-预算', '毛利率-预算'
            , '采购PO', '成本合计-核算', '料-核算', '工单料', '设变料', '工-核算', '生产工-核算', '交付工-核算', '费-核算', '设计工-核算', '其他费-核算', '毛利-核算', '毛利率-核算'
            , '成本合计-还需', '料-还需', '工-还需', '生产工-还需', '交付工-还需', '费-还需', '设计工-还需', '其他费-还需'
            , '成本合计-滚动', '料-滚动', '工-滚动', '生产工-滚动', '交付工-滚动', '费-滚动', '设计工-滚动', '其他费-滚动', '毛利-滚动', '毛利率-滚动'
            , '成本合计-年初', '料-年初', '工-年初', '生产工-年初', '交付工-年初', '费-年初', '设计工-年初', '其他费-年初', 'Bom金额'
            , '项目号整理',  '成品料号', '一般工单号601/608', '返工工单号603', '大项目整理','是否在手订单'
            , '计划出货时间','计划验收时间', '实际出货时间', '实际验收时间', '系统验收时间', '是否YY','OA状态','PO号','概算名称'
            ,'工时(生产交付)','生产工时','交付工时','设计工时','项目财经'])
        '''
        summary_date = ['实际出货时间', '实际验收时间', '系统验收时间']
        for dat in summary_date:
            summary[dat] = ['' if i == '1990-01-01' else i for i in summary[dat]]
        summary.loc[summary['生产状态'].str.contains('出货'),'计划出货时间']=summary['实际出货时间']
        summary.loc[summary['生产状态'].str.contains('验收'), '计划验收时间'] = summary['实际验收时间']

        time_data_refresh = time.time()
        print('第四阶段【字段加工】执行时长:%d秒' % (time_data_refresh - time_data_catch))
        screm_total.insert(INSERT, '\n第四阶段【字段加工】执行时长:%d秒' % (time_data_refresh - time_data_catch), '\n')
        window_total.update()

        ######################################################################################################################################五
        print('五、表格输出...')
        screm_total.insert(INSERT, '\n五、表格输出...', '\n')
        window_total.update()

        def writer_contents(sheet, array, start_row, start_col, format, percent_format=None, percentlist=[]):
            # start_col = 0
            for col in array:
                if percentlist and (start_col in percentlist):
                    sheet.write_column(start_row, start_col, col, percent_format)
                else:
                    sheet.write_column(start_row, start_col, col, format)
                start_col += 1

        def write_color(book, sheet, data, fmt, col_num='I'):
            start = 3
            format_red = book.add_format({'font_name': 'Arial',
                                          'font_size': 10,
                                          'bg_color': '#F86470'})
            format_red.set_align('center')
            format_red.set_align('vcenter')

            for item in data:
                if '找不到' in str(item) in str(item):
                    sheet.write(col_num + str(start), item, format_red)
                else:
                    sheet.write(col_num + str(start), item, fmt)
                start += 1

        # 写入表格
        print('5.1：正在设置表格格式...')
        screm_total.insert(INSERT, '\n5.1：正在设置表格格式...', '\n')
        window_total.update()
        now_time = time.strftime("%Y-%m-%d-%H", time.localtime(time.time()))
        book_name = '4、概预核决汇总-数据\概预核决汇总表' + now_time
        workbook = xlsxwriter.Workbook(book_name + '.xlsx', {'nan_inf_to_errors': True})
        worksheet0 = workbook.add_worksheet('汇总表')
        worksheet1 = workbook.add_worksheet('01-底表基础数据')  ##base
        worksheet2 = workbook.add_worksheet('02-概预算')  # budget_estimate
        worksheet3 = workbook.add_worksheet('03-核算表')  ##calcu
        worksheet4 = workbook.add_worksheet('04-年初预算表')  ##year_budget
        worksheet5 = workbook.add_worksheet('05-海外项目信息表')  ##year_budget
        worksheet6 = workbook.add_worksheet('06-Bom明细表')  ##year_budget
        ######主色调
        title_format = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 10,
                                            'font_color': 'white',
                                            'bg_color': '#1F4E78',
                                            'bold': True,
                                            'align': 'center',
                                            'valign': 'vcenter',
                                            'border': 1,
                                            'border_color': 'white'
                                            })
        title_format.set_align('vcenter')
        ######收入
        title_format1 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#9D4B73',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format1.set_align('vcenter')
        ######概算
        title_format2 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#248E87',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format2.set_align('vcenter')

        ######预算
        title_format3 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#339966',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format3.set_align('vcenter')
        ######核算
        title_format4 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#A79B01',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format4.set_align('vcenter')
        ######还需
        title_format5 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#666699',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format5.set_align('vcenter')
        ######滚动
        title_format6 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#44A3B6',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format6.set_align('vcenter')
        ######财务|年初预算
        title_format7 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#4A9C8C',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format7.set_align('vcenter')
        ######项目工时
        title_format8 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#987256',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format8.set_align('vcenter')
        ######
        title_format9 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#C4462E',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format9.set_align('vcenter')
        col_format = workbook.add_format({'font_name': 'Arial',
                                          'font_size': 8,
                                          'font_color': 'white',
                                          'bg_color': '#595959',
                                          'text_wrap': True,
                                          'border': 1,
                                          'border_color': 'white',
                                          'align': 'center',
                                          'valign': 'vcenter'
                                          })

        data_format = workbook.add_format({'font_name': 'Regular',
                                           'font_size': 9,
                                           'align': 'left',
                                           'valign': 'vcenter'
                                           })
        data_format1 = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 9,
                                            'align': 'center',
                                            'valign': 'vcenter'
                                            })
        data_format2 = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 9,
                                            'align': 'center',
                                            'valign': 'vcenter'
                                            })
        data_format2.set_num_format('0.00')
        data_format3 = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 9,
                                            'align': 'center',
                                            'valign': 'vcenter'
                                            })
        data_format3.set_num_format('0.00%')
        num_percent_data_format = workbook.add_format({'font_name': 'Arial',
                                                       'font_size': 9,
                                                       'align': 'center',
                                                       'valign': 'vcenter',
                                                       'num_format': '0.00%'})
        statis_format2 = workbook.add_format({'font_name': 'Arial',  # 系列总计
                                              'font_size': 9,
                                              'align': 'center',
                                              'valign': 'vcenter',
                                              'bg_color': '#92CDDC'
                                              })
        data_format_percent = workbook.add_format({'font_name': 'Arial',
                                                   'font_size': 9,
                                                   'align': 'center',
                                                   'valign': 'vcenter'
                                                   })
        data_format_percent.set_num_format('0.00%')

        print('5.2：正在写入EXCEL表格...')
        screm_total.insert(INSERT, '\n5.2：正在写入EXCEL表格...', '\n')
        window_total.update()
        worksheet0.write_row("A2", summary.columns, title_format)
        writer_contents(sheet=worksheet0, array=summary.T.values, start_row=2, start_col=0,format=data_format)
        # end = len(report_work1) + 1

        worksheet0.merge_range('A1:X1', '项目基础信息', title_format)
        worksheet0.merge_range('Y1:AA1', '收入数据', title_format1)
        worksheet0.merge_range('AB1:AD1', '', title_format)
        worksheet0.merge_range('AE1:AN1', '概算数据', title_format2)
        worksheet0.merge_range('AO1:AX1', '预算数据', title_format3)
        worksheet0.merge_range('AY1:BI1', '核算数据', title_format4)
        worksheet0.merge_range('BJ1:BQ1', '还需数据', title_format5)
        worksheet0.merge_range('BR1:CA1', '滚动预测数据', title_format6)
        worksheet0.merge_range('CB1:CI1', '差异', title_format7)
        worksheet0.merge_range("CJ1:DF1", '项目补充信息', title_format)
        worksheet0.merge_range("DG1:DN1", '年度预算成本', title_format8)
        worksheet0.merge_range("DO1:EA1", '', title_format)

        worksheet0.write_row("Y2:AA2", ['事业部收入', '集团收入','收入差异'], title_format1)
        worksheet0.write_row("AE2:AN2", ['成本合计', '料', '工','生产工', '交付工','费', '设计工', '制费', '毛利','毛利率'],title_format2)
        worksheet0.write_row("AO2:AX2", ['成本合计', '料', '工','生产工', '交付工','费', '设计工', '制费', '毛利','毛利率'],title_format3)
        worksheet0.write_row("AY2:BI2",['成本合计', '料', '采购PO','工','生产工', '交付工','费', '设计工', '其他', '毛利','毛利率'], title_format4)
        worksheet0.write_row("BJ2:BQ2", ['成本合计', '料', '工', '生产工', '交付工', '费', '设计工', '其他费'],title_format5)
        worksheet0.write_row("BR2:CA2", ['成本合计', '料', '工', '生产工', '交付工', '费', '设计工', '其他费','毛利', '毛利率'],title_format6)
        worksheet0.write_row("CB2:CI2", ['成本合计','料','工','生产工','交付工','费','设计工', '其他费'],title_format7)
        worksheet0.write_row("DG2:DN2", ['成本合计','料','工','生产工','交付工','费','设计工', '其他费'], title_format8)
        '''
        columns=['序列号', '客户简称', '大项目名称', '大项目号', '产品线名称', '核算项目号','设备名称', "生产主体", "销售主体", "业务", "项目经理", "产品经理", "产能", '自制/外包'
                , '设备类型', '全面预算有无', '项目数量' , '已出货数量', '在产数量', '生产状态', '项目阶段','系统发货月份','系统验收月份','备注'
                ,  ,'预算是否锁定','验收状态', '是否YY'
                , '成本合计-概算', '料-概算', '工-概算','生产工-概算', '交付工-概算','费-概算', '设计工-概算', '制费-概算', '毛利-概算','毛利率-概算'
                , '成本合计-预算', '料-预算','工-预算', '生产工-预算', '交付工-预算','费-预算', '设计工-预算', '制费-预算', '毛利-预算','毛利率-预算'
                , '成本合计-核算', '料-核算' , '采购PO',  '工-核算', '生产工-核算', '交付工-核算','费-核算', '设计工-核算', '其他费-核算', '毛利-核算', '毛利率-核算'
                , '成本合计-还需', '料-还需', '工-还需', '生产工-还需', '交付工-还需', '费-还需', '设计工-还需','其他费-还需'
                , '成本合计-滚动', '料-滚动', '工-滚动', '生产工-滚动', '交付工-滚动', '费-滚动', '设计工-滚动', '其他费-滚动', '毛利-滚动', '毛利率-滚动'
                , '成本合计','料','工','生产工','交付工','费','设计工','其他费','概算名称','PO号', '项目号整理'
                , '预算出货时间','计划出货时间', '实际出货时间', '预算验收时间','计划验收时间', '实际验收时间', '系统验收时间'
                , '是否有风险','原因分类','进度情况', '成品料号', '一般工单号601/608', '返工工单号603','工单开立时间','工单完工时间'
                , '生产周期-天数', '验收周期-按月', '设备总数量', '项目财经', '大项目整理'
                , '成本合计-年初', '料-年初', '工-年初', '生产工-年初', '交付工-年初', '费-年初', '设计工-年初','其他费-年初'
                , 'Bom金额','差异BOM-预算料','BOM备注'
                , '区域', '行业中心', '软件收入', '硬件收入', '是否在手订单', 'OA状态', '工时(生产交付)', '生产工时', '交付工时', '设计工时'])
        '''

        worksheet0.set_row(0, 25)
        worksheet0.set_row(1, 22)
        worksheet0.set_column('AI:AI', 8, data_format_percent)
        worksheet0.set_column('AQ:AQ', 8, data_format_percent)
        worksheet0.set_column('BD:BD', 8, data_format_percent)
        worksheet0.set_column('BV:BV', 8, data_format_percent)

        worksheet0.set_column('X:AH', 8, data_format2)
        worksheet0.set_column('AJ:AP', 8, data_format2)
        worksheet0.set_column('AR:BC', 8, data_format2)
        worksheet0.set_column('BE:BU', 8, data_format2)
        worksheet0.set_column('BW:CE', 8, data_format2)
        worksheet0.set_column('CU:CX', 8, data_format2)

        ##########写入公式

        for i in range(len(summary)):
            n=i+3
            worksheet0.write_formula('AM' + str(n),'=IF(Z%d<>"",IF(AE%d<>"",Z%d-AE%d,""),"")' % (n, n, n, n))  # '=B2+C2+D2'
            worksheet0.write_formula('AN' + str(n),'=IF(Z%d<>"",IF(Z%d<>0,IF(AM%d<>"",AM%d/Z%d,""),""),"")' % (n, n, n, n,n))
            worksheet0.write_formula('AW' + str(n),'=IF(Z%d<>"",IF(AO%d<>"",Z%d-AO%d,""),"")' % (n, n, n, n))  # '=B2+C2+D2'
            worksheet0.write_formula('AX' + str(n),'=IF(Z%d<>"",IF(Z%d<>0,IF(AW%d<>"",AW%d/Z%d,""),""),"")' % (n, n, n, n, n))
            worksheet0.write_formula('BH' + str(n),'=IF(Z%d<>"",IF(AY%d<>"",Z%d-AY%d,""),"")' % (n, n, n, n))  # '=B2+C2+D2'
            worksheet0.write_formula('BI' + str(n),'=IF(Z%d<>"",IF(Z%d<>0,IF(BH%d<>"",BH%d/Z%d,""),""),"")' % (n, n, n, n, n))

            worksheet0.write_formula('BZ' + str(n),'=IF(Z%d<>"",IF(BR%d<>"",Z%d-BR%d,""),"")' % (n, n, n, n))  # '=B2+C2+D2'
            worksheet0.write_formula('CA' + str(n),'=IF(Z%d<>"",IF(Z%d<>0,IF(BZ%d<>"",BZ%d/Z%d,""),""),"")' % (n, n, n, n, n))

            worksheet0.write_formula('BJ' + str(n), '=IF(AO%d<>"",IF(AY%d<>"",AO%d-AY%d,""),"")' % (n, n, n, n))
            worksheet0.write_formula('BK' + str(n), '=IF(AP%d<>"",IF(AZ%d<>"",AP%d-AZ%d,""),"")' % (n, n, n, n))
            worksheet0.write_formula('BL' + str(n), '=IF(BM%d<>"",IF(BN%d<>"",BM%d+BN%d,""),"")' % (n, n, n, n))
            worksheet0.write_formula('BM' + str(n), '=IF(AR%d<>"",IF(BC%d<>"",AR%d-BC%d,""),"")' % (n, n, n, n))
            worksheet0.write_formula('BN' + str(n), '=IF(AS%d<>"",IF(BD%d<>"",AS%d-BD%d,""),"")' % (n, n, n, n))
            worksheet0.write_formula('BO' + str(n), '=IF(BP%d<>"",IF(BQ%d<>"",BP%d+BQ%d,""),"")' % (n, n, n, n))
            worksheet0.write_formula('BP' + str(n), '=IF(AU%d<>"",IF(BF%d<>"",AU%d-BF%d,""),"")' % (n, n, n, n))
            worksheet0.write_formula('BQ' + str(n), '=IF(AV%d<>"",IF(BG%d<>"",AV%d-BG%d,""),"")' % (n, n, n, n))

            worksheet0.write_formula('BR' + str(n), '=IF(AY%d<>"",IF(BJ%d<>"",AY%d+BJ%d,""),"")'%(n,n,n,n))
            worksheet0.write_formula('BS' + str(n), '=IF(AZ%d<>"",IF(BK%d<>"",AZ%d+BK%d,""),"")'%(n,n,n,n))
            worksheet0.write_formula('BT' + str(n), '=IF(BB%d<>"",IF(BL%d<>"",BB%d+BL%d,""),"")'%(n,n,n,n))
            worksheet0.write_formula('BU' + str(n), '=IF(BC%d<>"",IF(BM%d<>"",BC%d+BM%d,""),"")'%(n,n,n,n))
            worksheet0.write_formula('BV' + str(n), '=IF(BD%d<>"",IF(BN%d<>"",BD%d+BN%d,""),"")'%(n,n,n,n))
            worksheet0.write_formula('BW' + str(n), '=IF(BE%d<>"",IF(BO%d<>"",BE%d+BO%d,""),"")'%(n,n,n,n))
            worksheet0.write_formula('BX' + str(n), '=IF(BF%d<>"",IF(BP%d<>"",BF%d+BP%d,""),"")'%(n,n,n,n))
            worksheet0.write_formula('BY' + str(n), '=IF(BG%d<>"",IF(BQ%d<>"",BG%d+BQ%d,""),"")'%(n,n,n,n))

        worksheet1.write_row("A1", base.columns, title_format)
        writer_contents(sheet=worksheet1, array=base.T.values, start_row=1, start_col=0,format=data_format)
        worksheet1.set_row(0, 25)

        worksheet2.write_row("A1", budget_estimate.columns, title_format)
        writer_contents(sheet=worksheet2, array=budget_estimate.T.values, start_row=1, start_col=0,format=data_format)
        worksheet2.set_row(0, 25)

        worksheet3.write_row("A1", calcu.columns, title_format)
        writer_contents(sheet=worksheet3, array=calcu.T.values, start_row=1, start_col=0,format=data_format)
        worksheet3.set_row(0, 25)

        worksheet4.write_row("A1", year_budget.columns, title_format)
        writer_contents(sheet=worksheet4, array=year_budget.T.values, start_row=1, start_col=0,format=data_format)
        worksheet4.set_row(0, 25)

        worksheet5.write_row("A1", outproj.columns, title_format)
        writer_contents(sheet=worksheet5, array=outproj.T.values, start_row=1, start_col=0,format=data_format)
        worksheet5.set_row(0, 25)
        worksheet6.write_row("A1", bom.columns, title_format)
        writer_contents(sheet=worksheet6, array=bom.T.values, start_row=1, start_col=0,format=data_format)
        worksheet6.set_row(0, 25)
        print('明细表已写入。。。')
        workbook.close()
        time_excel = time.time()
        print('第五阶段【输出表格】执行时长:%d秒' % (time_excel - time_data_refresh))

    except Exception as f:
        # print('异常信息为:', e)  # 异常信息为: division by zero
        print('——#@*&程序报错，异常信息为:' + traceback.format_exc())
        screm_total.insert(INSERT, '\n——#@*&程序报错，异常信息为:' + traceback.format_exc(), '\n')
        window_total.update()

    time_end = time.time()
    print('执行完成！！！！！')
    print('执行总时长:%d秒' % (time_end - time_start))
    screm_total.insert(INSERT, '\n明细表已写入。。。', '\n')
    screm_total.insert(INSERT, '\n执行总时长:%d秒' % (time_end - time_start), '\n')
    window_total.update()
