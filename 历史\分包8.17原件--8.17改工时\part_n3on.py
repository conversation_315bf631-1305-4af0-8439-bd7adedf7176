#coding:utf-8
import zipfile
####################################################抬头
import threading
import pandas as pd
import numpy as np
import time
import os
import xlsxwriter
from pathlib import Path
import warnings
import tkinter as tk
import tkinter.messagebox #弹出框
from tkinter import *

from PIL import Image, ImageTk, ImageSequence
import xlsxwriter
import traceback
import datetime
from tkinter import scrolledtext
from datetime import datetime
from picture import load_image
from wholeuse import*
def n3on():
    frame_total.create_image(740, 60, image=load_image(r'软件附带文件\框子.png'), anchor="n")
    screm_total = scrolledtext.ScrolledText(frame_total, bg='powderblue',  # 标签背景颜色
                                            highlightthickness=0,
                                            font=('微软雅黑', 12),  # 字体和字体大小
                                            width=72, height=14  # 标签长宽(以字符长度计算)
                                            )
    frame_total.create_window(750, 240, window=screm_total)
    try:
        time_start = time.time()
        #################################################################################################第一阶段
        print("第一阶段：读取数据..")
        screm_total.insert(INSERT, '第一阶段：读取数据..', '\n')
        window_total.update()
        ###历史进度表
        print("1.1：正在读取历史进度表..")
        screm_total.insert(INSERT, '\n1.1：正在读取历史进度表..', '\n')
        window_total.update()
        filePathold = r'周进度表-数据\数据源\历史进度表'
        file_nameold = os.listdir(filePathold)
        for i in range(len(file_nameold)):
            if str(file_nameold[i]).count('~$') == 0:
                # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
                advance_old = pd.read_excel(filePathold + '/' + str(file_nameold[i]), header=2)

        print("1.2：正在读取规则整理表..")
        screm_total.insert(INSERT, '\n1.2：正在读取规则整理表..', '\n')
        window_total.update()
        Path_rule = r'周进度表-数据\数据源\产品线-客户整理规则'
        file_rule = os.listdir(Path_rule)
        for i in range(len(file_rule)):
            if str(file_rule[i]).count('~$') == 0:
                # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
                line_rule = pd.read_excel(Path_rule + '/' + str(file_rule[i]), sheet_name='产品线')
                customer_rule = pd.read_excel(Path_rule + '/' + str(file_rule[i]), sheet_name='客户')

        print("1.3：正在读取生产风险汇总表..")
        screm_total.insert(INSERT, '\n1.3：正在读取生产风险汇总表..', '\n')
        window_total.update()
        Path_danger = r'周进度表-数据\数据源\生产风险汇总'
        file_danger = os.listdir(Path_danger)
        for i in range(len(file_danger)):
            if str(file_danger[i]).count('~$') == 0:
                report_danger = pd.read_excel(Path_danger + '/' + str(file_danger[i]), sheet_name='在手订单明细')[
                    ['大项目号', '项目号', '原因大类', '原因小类', 'PC备注']]

        print("1.4：正在读取制片一览表..")
        screm_total.insert(INSERT, '\n1.4：正在读取制片一览表..', '\n')
        window_total.update()
        Path_item1 = r'周进度表-数据\数据源\制片-一览表'
        file_item1 = os.listdir(Path_item1)
        reportitem1 = []
        if os.listdir(Path_item1):
            for name in file_item1:
                if 'xlsx' in name and '~$' not in name:
                    writer = pd.ExcelFile(Path_item1 + '\\' + name)
                    sheet_len = len(writer.sheet_names)
                    for i in range(0, sheet_len):
                        if '出货）' in str(writer.sheet_names[i]):
                            df1 = pd.read_excel(writer, sheet_name=writer.sheet_names[i], header=1)[
                                ['下单项目号', '成品编码', '关键问题或风险点', '项目进度更新']]
                            reportitem1.append(df1)
            report_item1 = pd.concat(reportitem1).reset_index(drop=True)

        print("1.5：正在读取装配线一览表..")
        screm_total.insert(INSERT, '\n1.5：正在读取装配线一览表...', '\n')
        window_total.update()
        Path_item2 = r'周进度表-数据\数据源\装配线-一览表'
        file_item2 = os.listdir(Path_item2)
        reportitem2 = []
        if os.listdir(Path_item2):
            for name in file_item2:
                if 'xlsx' in name and '~$' not in name:
                    writer = pd.ExcelFile(Path_item2 + '\\' + name)
                    sheet_len = len(writer.sheet_names)
                    for i in range(0, sheet_len):
                        df2_1 = pd.read_excel(writer, sheet_name=writer.sheet_names[i])
                        if '下单项目号' in df2_1.columns and 'M6' not in writer.sheet_names[i]:
                            df2 = df2_1[['下单项目号', '成品编码', '关键问题或风险点', '项目进度更新']]
                            reportitem2.append(df2)
            report_item2 = pd.concat(reportitem2).reset_index(drop=True)

        print("1.6：正在读取项目部数据看板..")
        screm_total.insert(INSERT, '\n1.6：正在读取项目部数据看板..', '\n')
        window_total.update()
        Path_view = r'周进度表-数据\数据源\项目部数据看板'
        file_view = os.listdir(Path_view)
        for i in range(len(file_view)):
            if str(file_view[i]).count('~$') == 0:
                # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
                report_view = pd.read_excel(Path_view + '/' + str(file_view[i]), header=1, sheet_name='项目总体计划')[
                    ['大项目号', '项目号', '计划/实际出货', '实际\n验收', '设备数量', '出货年份']]

        print("1.7：正在读取底表..")
        screm_total.insert(INSERT, '\n1.7：正在读取底表..', '\n')
        window_total.update()
        Path_base = r'核算&进度&汇总表底表'
        file_base = os.listdir(Path_base)
        for name in file_base:
            if name.count('~$') == 0:
                report_base = pd.read_excel(Path_base + '/' + name)

        print("1.8：正在读取验收进度表..")
        screm_total.insert(INSERT, '\n1.8：正在读取验收进度表..', '\n')
        window_total.update()
        Path_rece = r'周进度表-数据\数据源\验收进度表'
        file_rece = os.listdir(Path_rece)
        for name in file_rece:
            if name.count('~$') == 0:
                report_rece = pd.read_excel(Path_rece + '/' + name, header=3)[
                    ['核算项目号', '一般工单号\n（601/608）', '最新刷新月份', "风险等级\n（高、中、正常推进）", "风险类别\n（商务问题、技术问题、项目问题）"]]
        report_rece = report_rece.rename(columns={'一般工单号\n（601/608）': "一般工单号601/608", "风险等级\n（高、中、正常推进）": "风险等级",
                                                  "风险类别\n（商务问题、技术问题、项目问题）": "风险分类"})
        time_read_data = time.time()
        print('第一阶段执行时长:%d秒' % (time_read_data - time_start))
        screm_total.insert(INSERT, '\n第一阶段执行时长:%d秒' % (time_read_data - time_start), '\n')
        window_total.update()

        #################################################################################################第二阶段
        print("第二阶段：数据格式处理..")
        screm_total.insert(INSERT, '\n第二阶段：数据格式处理..', '\n')
        window_total.update()
        ##文本数据
        print("2.1：正在处理文本数据..")
        screm_total.insert(INSERT, '\n2.1：正在处理文本数据..', '\n')
        window_total.update()
        old_str = ['序列号', "区域", "项目阶段", "姓名", "是否有风险", "原因分类", "生产实际进度"]
        advance_old[old_str] = advance_old[old_str].fillna('')
        advance_old = advance_old.drop_duplicates(subset=['序列号']).reset_index(drop=True)

        line_rule[['产品线', '产品线整理']] = line_rule[['产品线', '产品线整理']].fillna('')
        line_rule = line_rule.drop_duplicates(subset=['产品线']).reset_index(drop=True)

        customer_rule[['客户简称', '客户整理']] = customer_rule[['客户简称', '客户整理']].fillna('')
        customer_rule = customer_rule.drop_duplicates(subset=['客户简称']).reset_index(drop=True)

        report_danger[['大项目号', '项目号', '原因大类', '原因小类', 'PC备注']] = report_danger[
            ['大项目号', '项目号', '原因大类', '原因小类', 'PC备注']].fillna('')

        report_item1[['下单项目号', '成品编码', '关键问题或风险点', '项目进度更新']] = report_item1[
            ['下单项目号', '成品编码', '关键问题或风险点', '项目进度更新']].fillna('')
        report_item2[['下单项目号', '成品编码', '关键问题或风险点', '项目进度更新']] = report_item2[
            ['下单项目号', '成品编码', '关键问题或风险点', '项目进度更新']].fillna('')

        report_view[['大项目号', '项目号', '出货年份']] = report_view[['大项目号', '项目号', '出货年份']].fillna('/')
        report_view = report_view[report_view['出货年份'].str.contains('取消|暂停|待定') == False].reset_index(drop=True)

        report_rece[['核算项目号', '一般工单号601/608', "风险等级", "风险分类"]] = report_rece[
            ['核算项目号', '一般工单号601/608', "风险等级", "风险分类"]].fillna('')

        ##数值数据
        print("2.2：正在处理数值数据..")
        screm_total.insert(INSERT, '\n2.2：正在处理数值数据..', '\n')
        window_total.update()
        report_view['设备数量'] = report_view['设备数量'].fillna(0)
        report_view = report_view[report_view['设备数量'] > 0].reset_index(drop=True)
        report_rece[["最新刷新月份"]] = report_rece[["最新刷新月份"]].fillna(0)
        ##日期数据
        print("2.3：正在处理日期数据..")
        screm_total.insert(INSERT, '\n2.3：正在处理日期数据..', '\n')
        window_total.update()
        default_date1 = pd.Timestamp(2090, 1, 1)
        old_date = ["暂停时间", "重启时间", "23年预算出货时间", "23年预算验收时间"]
        advance_old[old_date] = advance_old[old_date].fillna(default_date1)
        advance_old['暂停时间'] = pd.to_datetime(advance_old['暂停时间'], errors='coerce')
        advance_old['重启时间'] = pd.to_datetime(advance_old['重启时间'], errors='coerce')
        advance_old['23年预算出货时间'] = pd.to_datetime(advance_old['23年预算出货时间'], errors='coerce')
        advance_old['23年预算验收时间'] = pd.to_datetime(advance_old['23年预算验收时间'], errors='coerce')

        report_view['计划/实际出货'] = report_view['计划/实际出货'].fillna(default_date1)
        report_view['计划/实际出货'] = pd.to_datetime(report_view['计划/实际出货'], errors='coerce')
        report_view['实际\n验收'] = report_view['实际\n验收'].fillna(default_date1)
        report_view['实际\n验收'] = pd.to_datetime(report_view['实际\n验收'], errors='coerce')

        report_base[["系统出货时间", "实际出货时间", "系统验收时间", "实际验收时间"]] = report_base[
            ["系统出货时间", "实际出货时间", "系统验收时间", "实际验收时间"]].fillna(default_date1)
        report_base['系统出货时间'] = pd.to_datetime(report_base['系统出货时间'], errors='coerce')
        report_base['实际出货时间'] = pd.to_datetime(report_base['实际出货时间'], errors='coerce')
        report_base['系统验收时间'] = pd.to_datetime(report_base['系统验收时间'], errors='coerce')
        report_base['实际验收时间'] = pd.to_datetime(report_base['实际验收时间'], errors='coerce')
        report_base = report_base.fillna('')
        report_base = report_base[report_base['是否转移变更'].str.contains('是') == False].reset_index(drop=True)
        if '区域' in report_base.columns:
            del report_base['区域']

        time_data_format = time.time()
        print('第二阶段执行时长:%d秒' % (time_data_format - time_read_data))
        screm_total.insert(INSERT, '\n第二阶段执行时长:%d秒' % (time_data_format - time_read_data), '\n')
        window_total.update()

        #################################################################################################第三阶段
        print("第三阶段：数据拉取..")
        print("3.1：底表拉取产品线、客户整理")
        screm_total.insert(INSERT, '\n第三阶段：数据拉取..', '\n')
        screm_total.insert(INSERT, '\n3.1：底表拉取产品线、客户整理..', '\n')
        window_total.update()
        report_base['产品线整理'] = pd.merge(report_base, line_rule, how='left', left_on='产品线名称', right_on='产品线')[
            '产品线整理']
        report_base['客户整理'] = pd.merge(report_base, customer_rule, how='left', left_on='客户简称', right_on='客户简称')[
            '客户整理']

        print("3.2：底表拉取一览表进度、关键问题或风险点")
        screm_total.insert(INSERT, '\n3.2：底表拉取一览表进度、关键问题或风险点..', '\n')
        window_total.update()
        report_item_total = pd.concat([report_item1, report_item2]).reset_index(drop=True)
        report_item_total = report_item_total.drop_duplicates(subset=['下单项目号']).reset_index(drop=True)
        report_base[['关键问题或风险点', '一览表进度']] = \
            pd.merge(report_base, report_item_total, how='left', left_on='核算项目号', right_on='下单项目号')[
                ['关键问题或风险点', '项目进度更新']]

        print("3.3：底表拉取看板出货、验收时间")
        screm_total.insert(INSERT, '\n3.3：底表拉取看板出货、验收时间..', '\n')
        window_total.update()
        ##############看板项目号整理
        print("3.3.1：看板做项目号整理")
        screm_total.insert(INSERT, '\n3.3.1：看板做项目号整理..', '\n')
        window_total.update()
        if len(report_view[report_view['项目号'].str.contains('-')]) > 0:
            report_view['项目号整理'] = ''
            report_view['项目整'] = report_view['项目号'].str.split('-', expand=True)[0]
            report_view['项目整1'] = report_view['项目号'].str.split('-', expand=True)[1]
            report_view['项目整1'] = report_view['项目整1'].fillna('空值')
            report_view['项目号整理'] = report_view['项目整']
            report_view.loc[
                (report_view['项目整1'].str.isdigit()) | (report_view['项目整1'].str.contains('SH')), '项目号整理'] = \
                report_view['项目号整理'] + '-' + report_view['项目整1']
        if len(report_view[report_view['项目号'].str.contains('-')]) == 0:
            report_view['项目号整理'] = report_view['项目号']
        report_view.loc[(report_view['项目号整理'].str[0] == 'F') & (
            report_view['项目号整理'].str[:3].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX')), '项目号整理'] = \
            report_view['项目号整理'].str[3:]
        report_view.loc[(report_view['项目号整理'].str[:2].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX',
                                                                   na=False)), '项目号整理'] = report_view['项目号整理'].str[
                                                                                          2:]

        ##############看板拆行
        print("3.3.2：看板做数据拆行")
        screm_total.insert(INSERT, '\n3.3.2：看板做数据拆行..', '\n')
        window_total.update()
        report_view['需拆行数'] = report_view['设备数量'] - 1
        list_out = list(report_view[(report_view['需拆行数'] > 0)]['需拆行数'].drop_duplicates().reset_index(drop=True))
        for i in list_out:
            add_row = report_view[report_view['需拆行数'] == i].reset_index(drop=True)
            add_row['是否拆行'] = '是'
            for j in range(int(i)):
                report_view = pd.concat([report_view, add_row]).reset_index(drop=True)
        #######################################第一层给值
        ###看板排序做唯一值
        print("3.3.3：看板第一层排序并做唯一字段")
        screm_total.insert(INSERT, '\n3.3.3：看板第一层排序并做唯一字段..', '\n')
        window_total.update()
        report_view = report_view.sort_values(by=['大项目号', '项目号整理', '计划/实际出货'],
                                              ascending=[True, True, True]).reset_index(drop=True)
        report_view.loc[0, '排序1'] = str(1)
        d = 1
        for i in range(1, len(report_view)):
            if str(report_view.loc[i, '项目号整理']) == str(report_view.loc[i - 1, '项目号整理']) and str(
                    report_view.loc[i, '大项目号']) == str(report_view.loc[i - 1, '大项目号']):
                d = d + 1
                report_view.loc[i, '排序1'] = str(d)
            if str(report_view.loc[i, '项目号整理']) != str(report_view.loc[i - 1, '项目号整理']) or str(
                    report_view.loc[i, '大项目号']) != str(report_view.loc[i - 1, '大项目号']):
                d = 1
                report_view.loc[i, '排序1'] = str(d)
        report_view['拉取1'] = report_view['大项目号'].astype(str) + report_view['项目号整理'] + str('-') + report_view['排序1']
        del report_view['排序1']

        print("3.3.4：底表第一层排序并做唯一字段")
        screm_total.insert(INSERT, '\n3.3.4：底表第一层排序并做唯一字段..', '\n')
        window_total.update()
        report_base = report_base.sort_values(by=['大项目号', '项目号整理', '系统出货时间'],
                                              ascending=[True, True, True]).reset_index(drop=True)
        report_base.loc[0, '排序1'] = str(1)
        d = 1
        for i in range(1, len(report_base)):
            if str(report_base.loc[i, '项目号整理']) == str(report_base.loc[i - 1, '项目号整理']) and str(
                    report_base.loc[i, '大项目号']) == str(report_base.loc[i - 1, '大项目号']):
                d = d + 1
                report_base.loc[i, '排序1'] = str(d)
            if str(report_base.loc[i, '项目号整理']) != str(report_base.loc[i - 1, '项目号整理']) or str(
                    report_base.loc[i, '大项目号']) != str(report_base.loc[i - 1, '大项目号']):
                d = 1
                report_base.loc[i, '排序1'] = str(d)
        report_base['拉取1'] = report_base['大项目号'].astype(str) + report_base['项目号整理'] + str('-') + report_base['排序1']
        del report_base['排序1']

        #########################拉取第一层
        print("3.3.5：底表按照第一层唯一字段拉取看板数据")
        screm_total.insert(INSERT, '\n3.3.5：底表按照第一层唯一字段拉取看板数据..', '\n')
        window_total.update()
        report_base[['计划出货时间_1', '看板实际验收时间_1']] = pd.merge(report_base, report_view, on='拉取1', how='left')[
            ['计划/实际出货', '实际\n验收']]

        #############第二层
        ####看板项目号整理排序
        print("3.3.6：看板按第二层排序并做唯一字段")
        screm_total.insert(INSERT, '\n3.3.6：看板按第二层排序并做唯一字段..', '\n')
        window_total.update()
        report_view = report_view.sort_values(by=['项目号整理', '计划/实际出货'], ascending=[True, True]).reset_index(
            drop=True)
        report_view = report_view[~report_view['拉取1'].isin(report_base['拉取1'])].reset_index(drop=True)
        d = 1
        if len(report_view) > 0:
            for i in range(1, len(report_view)):
                if str(report_view.loc[i, '项目号整理']) == str(report_view.loc[i - 1, '项目号整理']):
                    d = d + 1
                    report_view.loc[i, '排序'] = str(d)
                if str(report_view.loc[i, '项目号整理']) != str(report_view.loc[i - 1, '项目号整理']):
                    d = 1
                    report_view.loc[i, '排序'] = str(d)
            report_view['拉取'] = report_view['项目号整理'] + str('-') + report_view['排序']
            del report_view['排序']
        if len(report_view) == 0:
            report_view['拉取'] = '拉不到'

        ####底表项目号整理排序

        print("3.3.7：底表按第二层排序并做唯一字段")
        screm_total.insert(INSERT, '\n3.3.7：底表按第二层排序并做唯一字段..', '\n')
        window_total.update()
        report_base = report_base.sort_values(by=['项目号整理', '系统出货时间'], ascending=[True, True]).reset_index(drop=True)
        # report_base = report_base.sort_values(by=['项目号整理', '系统出货时间'], ascending=[True, True]).reset_index(drop=True)

        report_base['排序'] = 0
        report_base.loc[0, '排序'] = str(1)
        d1 = 1
        for i in range(1, len(report_base)):
            if str(report_base.loc[i, '项目号整理']) == str(report_base.loc[i - 1, '项目号整理']):
                d1 = d1 + 1
                report_base.loc[i, '排序'] = str(d1)
            if str(report_base.loc[i, '项目号整理']) != str(report_base.loc[i - 1, '项目号整理']):
                d1 = 1
                report_base.loc[i, '排序'] = str(d1)
        report_base['拉取'] = report_base['项目号整理'] + str('-') + report_base['排序']

        #########################拉取第一层
        print("3.3.8：底表按照第二层唯一字段拉取看板数据")
        screm_total.insert(INSERT, '\n3.3.8：底表按照第二层唯一字段拉取看板数据..', '\n')
        window_total.update()
        report_base[['计划出货时间', '看板实际验收时间']] = pd.merge(report_base, report_view, on='拉取', how='left')[
            ['计划/实际出货', '实际\n验收']]
        #############两层时间按优先级给值
        print("3.3.9：底表按照优先级选择两层看板数据")
        screm_total.insert(INSERT, '\n3.3.9：底表按照优先级选择两层看板数据..', '\n')
        window_total.update()
        report_base[['计划出货时间_1', '看板实际验收时间_1']] = report_base[['计划出货时间_1', '看板实际验收时间_1']].fillna(default_date1)
        report_base[['计划出货时间', '看板实际验收时间']] = report_base[['计划出货时间', '看板实际验收时间']].fillna(default_date1)
        report_base['计划出货时间_1'] = pd.to_datetime(report_base['计划出货时间_1'], errors='coerce')
        report_base['计划出货时间'] = pd.to_datetime(report_base['计划出货时间'], errors='coerce')
        report_base['看板实际验收时间_1'] = pd.to_datetime(report_base['看板实际验收时间_1'], errors='coerce')
        report_base['看板实际验收时间'] = pd.to_datetime(report_base['看板实际验收时间'], errors='coerce')
        report_base.loc[report_base['看板实际验收时间_1'] != default_date1, '看板实际验收时间'] = report_base['看板实际验收时间_1']
        report_base.loc[report_base['计划出货时间_1'] != default_date1, '计划出货时间'] = report_base['计划出货时间_1']

        ############拉取发货风险表
        print("3.4：底表拉取发货风险表")
        print("3.4.1：发货风险表做项目号整理")
        screm_total.insert(INSERT, '\n3.4：底表拉取发货风险表..', '\n')
        screm_total.insert(INSERT, '\n3.4.1：发货风险表做项目号整理..', '\n')
        window_total.update()
        if len(report_danger[report_danger['项目号'].str.contains('-')]) > 0:
            report_danger['项目号整理'] = ''
            report_danger['项目整'] = report_danger['项目号'].str.split('-', expand=True)[0]
            report_danger['项目整1'] = report_danger['项目号'].str.split('-', expand=True)[1]
            report_danger['项目整1'] = report_danger['项目整1'].fillna('空值')
            report_danger['项目号整理'] = report_danger['项目整']
            report_danger.loc[
                (report_danger['项目整1'].str.isdigit()) | (report_danger['项目整1'].str.contains('SH')), '项目号整理'] = \
                report_danger['项目号整理'] + '-' + report_danger['项目整1']
        if len(report_danger[report_danger['项目号'].str.contains('-')]) == 0:
            report_danger['项目号整理'] = report_danger['项目号']
        report_danger.loc[(report_danger['项目号整理'].str[0] == 'F') & (
            report_danger['项目号整理'].str[:3].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX')), '项目号整理'] = \
            report_danger['项目号整理'].str[3:]
        report_danger.loc[(report_danger['项目号整理'].str[:2].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX',
                                                                       na=False)), '项目号整理'] = report_danger[
                                                                                                  '项目号整理'].str[2:]
        report_danger = report_danger.drop_duplicates(subset=['项目号整理']).reset_index(drop=True)

        print("3.4.2：底表拉取发货风险表数据")
        screm_total.insert(INSERT, '\n3.4.2：底表拉取发货风险表数据..', '\n')
        window_total.update()
        report_base[['原因大类', '原因小类', 'PC备注']] = pd.merge(report_base, report_danger, on='项目号整理', how='left')[
            ['原因大类', '原因小类', 'PC备注']]

        ######拉取历史进度表数据
        print("3.5：底表拉取历史进度表数据")
        screm_total.insert(INSERT, '\n3.5：底表拉取历史进度表数据..', '\n')
        window_total.update()
        old_col = ["区域", "项目阶段", "暂停时间", "重启时间", "姓名", "23年预算出货时间", "23年预算验收时间", "是否有风险", "原因分类", "生产实际进度",
                   '验收实际进度']
        report_base[old_col] = pd.merge(report_base, advance_old, how='left', on='序列号')[old_col]
        report_base[old_str] = report_base[old_str].fillna('')
        report_base[old_date] = report_base[old_date].fillna(default_date1)

        ######拉取验收进度表数据
        print("3.6：底表拉取验收进度表数据")
        screm_total.insert(INSERT, '\n3.6：底表拉取验收进度表数据..', '\n')
        window_total.update()
        report_rece = report_rece[report_rece['核算项目号'] != ''].reset_index(drop=True)
        if len(report_rece[report_rece['核算项目号'].str.contains('-')]) > 0:
            report_rece['项目号整理'] = ''
            report_rece['项目整'] = report_rece['核算项目号'].str.split('-', expand=True)[0]
            report_rece['项目整1'] = report_rece['核算项目号'].str.split('-', expand=True)[1]
            report_rece['项目整1'] = report_rece['项目整1'].fillna('空值')
            report_rece['项目号整理'] = report_rece['项目整']
            report_rece.loc[
                (report_rece['项目整1'].str.isdigit()) | (report_rece['项目整1'].str.contains('SH')), '项目号整理'] = \
                report_rece['项目号整理'] + '-' + report_rece['项目整1']
        if len(report_rece[report_rece['核算项目号'].str.contains('-')]) == 0:
            report_rece['项目号整理'] = report_rece['核算项目号']
        report_rece.loc[(report_rece['项目号整理'].str[0] == 'F') & (
            report_rece['项目号整理'].str[:3].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX')), '项目号整理'] = \
            report_rece['项目号整理'].str[3:]
        report_rece.loc[(report_rece['项目号整理'].str[:2].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX',
                                                                   na=False)), '项目号整理'] = report_rece['项目号整理'].str[
                                                                                          2:]
        report_rece = report_rece.drop_duplicates(subset=['项目号整理']).reset_index(drop=True)

        report_rece['年份'] = str(int(time.strftime("%Y", time.localtime(time.time())))) + '年'
        # 最新刷新月份
        report_rece['最新刷新月份'] = report_rece['最新刷新月份'].astype(str)
        report_rece.loc[report_rece['最新刷新月份'].str.len() == 1, '最新刷新月份'] = str('0') + report_rece['最新刷新月份'].astype(
            str)
        report_rece['产品线计划验收时间'] = default_date1
        report_rece.loc[report_rece['最新刷新月份'] != '00', '产品线计划验收时间'] = pd.to_datetime(
            report_rece['年份'].astype(str).str.replace('年', '', regex=True) + '-' + report_rece[
                '最新刷新月份'] + '-' + str('01'), errors='coerce')
        report_rece.loc[report_rece['最新刷新月份'] != '00', '产品线计划验收时间'] = report_rece['产品线计划验收时间'] + pd.DateOffset(
            months=1) - pd.DateOffset(days=1)
        ##第一层工单号 '核算项目号','一般工单号601/608',"风险等级","风险分类"
        report_rece1 = report_rece[(report_rece['一般工单号601/608'] != '') & (report_rece['一般工单号601/608'] != '/') & (
            report_rece['一般工单号601/608'].isin(report_base['一般工单号601/608']))].reset_index(drop=True)
        report_rece1 = report_rece1.drop_duplicates(subset=['一般工单号601/608']).reset_index(drop=True)
        report_base[['产品线计划验收时间1', "风险等级1", "风险分类1"]] = \
            pd.merge(report_base, report_rece1, on='一般工单号601/608', how='left')[['产品线计划验收时间', "风险等级", "风险分类"]]
        report_rece2 = report_rece[(report_rece['一般工单号601/608'].isin(report_rece1['一般工单号601/608']))].reset_index(
            drop=True)
        report_rece2 = report_rece2.drop_duplicates(subset=['项目号整理']).reset_index(drop=True)
        report_base[['产品线计划验收时间', "风险等级", "风险分类"]] = pd.merge(report_base, report_rece2, on='项目号整理', how='left')[
            ['产品线计划验收时间', "风险等级", "风险分类"]]
        report_base[['产品线计划验收时间1', '产品线计划验收时间']] = report_base[['产品线计划验收时间1', '产品线计划验收时间']].fillna(default_date1)
        report_base[["风险等级1", "风险分类1", "风险等级", "风险分类"]] = report_base[["风险等级1", "风险分类1", "风险等级", "风险分类"]].fillna('')

        report_base.loc[report_base['一般工单号601/608'].isin(report_rece1['一般工单号601/608']), '产品线计划验收时间'] = report_base[
            '产品线计划验收时间1']
        report_base.loc[report_base['一般工单号601/608'].isin(report_rece1['一般工单号601/608']), '风险等级'] = report_base[
            '风险等级1']
        report_base.loc[report_base['一般工单号601/608'].isin(report_rece1['一般工单号601/608']), '风险分类'] = report_base[
            '风险分类1']

        report_base['暂停时间'] = pd.to_datetime(report_base['暂停时间'], errors='coerce')
        report_base['重启时间'] = pd.to_datetime(report_base['重启时间'], errors='coerce')
        report_base['23年预算出货时间'] = pd.to_datetime(report_base['23年预算出货时间'], errors='coerce')
        report_base['23年预算验收时间'] = pd.to_datetime(report_base['23年预算验收时间'], errors='coerce')
        report_base['产品线计划验收时间'] = pd.to_datetime(report_base['产品线计划验收时间'], errors='coerce')
        time_catch_data = time.time()
        print('第三阶段执行时长:%d秒' % (time_catch_data - time_data_format))
        #################################################################################################第四阶段
        print("第四阶段：数据计算..")
        report_base['新增出货收入'] = 0
        report_base['新增验收收入'] = 0
        report_base.loc[report_base['是否新增出货'].str.contains('是'), '新增出货收入'] = report_base['集团收入']
        report_base.loc[report_base['是否新增验收'].str.contains('是'), '新增验收收入'] = report_base['集团收入']
        report_base["实际-出货(年)"] = report_base['实际出货时间'].dt.strftime('%Y').astype(int)
        report_base.loc[(report_base['实际出货时间'].dt.strftime('%Y').astype(int) == 2090), "实际-出货(年)"] = ''
        ###实际出货时间
        report_base["实际-出货(季)"] = ''
        report_base.loc[(report_base['实际出货时间'].dt.strftime('%m').astype(int) < 4) & (
                report_base['实际出货时间'].dt.strftime('%m').astype(int) > 0), "实际-出货(季)"] = 'Q1'
        report_base.loc[(report_base['实际出货时间'].dt.strftime('%m').astype(int) < 7) & (
                report_base['实际出货时间'].dt.strftime('%m').astype(int) > 3), "实际-出货(季)"] = 'Q2'
        report_base.loc[(report_base['实际出货时间'].dt.strftime('%m').astype(int) < 10) & (
                report_base['实际出货时间'].dt.strftime('%m').astype(int) > 6), "实际-出货(季)"] = 'Q3'
        report_base.loc[(report_base['实际出货时间'].dt.strftime('%m').astype(int) > 9), "实际-出货(季)"] = 'Q4'
        report_base.loc[(report_base['实际出货时间'].dt.strftime('%Y').astype(int) == 2090), "实际-出货(季)"] = ''

        report_base["实际-出货(月)"] = report_base['实际出货时间'].dt.strftime('%m').astype(int)
        report_base.loc[(report_base['实际出货时间'].dt.strftime('%Y').astype(int) == 2090), "实际-出货(月)"] = ''
        ###预算出货时间
        report_base["预算-出货(季)"] = ''
        report_base.loc[(report_base['23年预算出货时间'].dt.strftime('%m').astype(int) < 4) & (
                report_base['23年预算出货时间'].dt.strftime('%m').astype(int) > 0), "预算-出货(季)"] = 'Q1'
        report_base.loc[(report_base['23年预算出货时间'].dt.strftime('%m').astype(int) < 7) & (
                report_base['23年预算出货时间'].dt.strftime('%m').astype(int) > 3), "预算-出货(季)"] = 'Q2'
        report_base.loc[(report_base['23年预算出货时间'].dt.strftime('%m').astype(int) < 10) & (
                report_base['23年预算出货时间'].dt.strftime('%m').astype(int) > 6), "预算-出货(季)"] = 'Q3'
        report_base.loc[(report_base['23年预算出货时间'].dt.strftime('%m').astype(int) > 9), "预算-出货(季)"] = 'Q4'
        report_base.loc[(report_base['23年预算出货时间'].dt.strftime('%Y').astype(int) == 2090), "预算-出货(季)"] = ''

        report_base["预算-出货(月)"] = report_base['23年预算出货时间'].dt.strftime('%m').astype(int)
        report_base.loc[(report_base['23年预算出货时间'].dt.strftime('%Y').astype(int) == 2090), "预算-出货(月)"] = ''
        ###计划出货时间
        report_base["预测-出货(季)"] = ''
        report_base.loc[(report_base['计划出货时间'].dt.strftime('%m').astype(int) < 4) & (
                report_base['计划出货时间'].dt.strftime('%m').astype(int) > 0), "预测-出货(季)"] = 'Q1'
        report_base.loc[(report_base['计划出货时间'].dt.strftime('%m').astype(int) < 7) & (
                report_base['计划出货时间'].dt.strftime('%m').astype(int) > 3), "预测-出货(季)"] = 'Q2'
        report_base.loc[(report_base['计划出货时间'].dt.strftime('%m').astype(int) < 10) & (
                report_base['计划出货时间'].dt.strftime('%m').astype(int) > 6), "预测-出货(季)"] = 'Q3'
        report_base.loc[(report_base['计划出货时间'].dt.strftime('%m').astype(int) > 9), "预测-出货(季)"] = 'Q4'
        report_base.loc[(report_base['计划出货时间'].dt.strftime('%Y').astype(int) == 2090), "预测-出货(季)"] = ''

        report_base["预测-出货(月)"] = report_base['计划出货时间'].dt.strftime('%m').astype(int)
        report_base.loc[(report_base['计划出货时间'].dt.strftime('%Y').astype(int) == 2090), "预测-出货(月)"] = ''

        report_base["实际-验收(年)"] = report_base['实际验收时间'].dt.strftime('%Y').astype(int)
        report_base.loc[(report_base['实际验收时间'].dt.strftime('%Y').astype(int) == 2090), "实际-验收(年)"] = ''

        ###看板实际验收时间
        report_base["实际-验收(季)"] = ''
        report_base.loc[(report_base['实际验收时间'].dt.strftime('%m').astype(int) < 4) & (
                report_base['实际验收时间'].dt.strftime('%m').astype(int) > 0), "实际-验收(季)"] = 'Q1'
        report_base.loc[(report_base['实际验收时间'].dt.strftime('%m').astype(int) < 7) & (
                report_base['实际验收时间'].dt.strftime('%m').astype(int) > 3), "实际-验收(季)"] = 'Q2'
        report_base.loc[(report_base['实际验收时间'].dt.strftime('%m').astype(int) < 10) & (
                report_base['实际验收时间'].dt.strftime('%m').astype(int) > 6), "实际-验收(季)"] = 'Q3'
        report_base.loc[(report_base['实际验收时间'].dt.strftime('%m').astype(int) > 9), "实际-验收(季)"] = 'Q4'
        report_base.loc[(report_base['实际验收时间'].dt.strftime('%Y').astype(int) == 2090), "实际-验收(季)"] = ''

        report_base["实际-验收(月)"] = report_base['实际验收时间'].dt.strftime('%m').astype(int)
        report_base.loc[(report_base['实际验收时间'].dt.strftime('%Y').astype(int) == 2090), "实际-验收(月)"] = ''

        ###预算验收时间
        report_base["预算-验收(季)"] = ''
        report_base.loc[(report_base['23年预算验收时间'].dt.strftime('%m').astype(int) < 4) & (
                report_base['23年预算验收时间'].dt.strftime('%m').astype(int) > 0), "预算-验收(季)"] = 'Q1'
        report_base.loc[(report_base['23年预算验收时间'].dt.strftime('%m').astype(int) < 7) & (
                report_base['23年预算验收时间'].dt.strftime('%m').astype(int) > 3), "预算-验收(季)"] = 'Q2'
        report_base.loc[(report_base['23年预算验收时间'].dt.strftime('%m').astype(int) < 10) & (
                report_base['23年预算验收时间'].dt.strftime('%m').astype(int) > 6), "预算-验收(季)"] = 'Q3'
        report_base.loc[(report_base['23年预算验收时间'].dt.strftime('%m').astype(int) > 9), "预算-验收(季)"] = 'Q4'
        report_base.loc[(report_base['23年预算验收时间'].dt.strftime('%Y').astype(int) == 2090), "预算-验收(季)"] = ''

        report_base["预算-验收(月)"] = report_base['23年预算验收时间'].dt.strftime('%m').astype(int)
        report_base.loc[(report_base['23年预算验收时间'].dt.strftime('%Y').astype(int) == 2090), "预算-验收(月)"] = ''

        ###产品线计划验收时间
        report_base["预测-验收(季)"] = ''
        report_base.loc[(report_base['产品线计划验收时间'].dt.strftime('%m').astype(int) < 4) & (
                report_base['产品线计划验收时间'].dt.strftime('%m').astype(int) > 0), "预测-验收(季)"] = 'Q1'
        report_base.loc[(report_base['产品线计划验收时间'].dt.strftime('%m').astype(int) < 7) & (
                report_base['产品线计划验收时间'].dt.strftime('%m').astype(int) > 3), "预测-验收(季)"] = 'Q2'
        report_base.loc[(report_base['产品线计划验收时间'].dt.strftime('%m').astype(int) < 10) & (
                report_base['产品线计划验收时间'].dt.strftime('%m').astype(int) > 6), "预测-验收(季)"] = 'Q3'
        report_base.loc[(report_base['产品线计划验收时间'].dt.strftime('%m').astype(int) > 9), "预测-验收(季)"] = 'Q4'
        report_base.loc[(report_base['产品线计划验收时间'].dt.strftime('%Y').astype(int) == 2090), "预测-验收(季)"] = ''

        report_base["预测-验收(月)"] = report_base['产品线计划验收时间'].dt.strftime('%m').astype(int)
        report_base.loc[(report_base['产品线计划验收时间'].dt.strftime('%Y').astype(int) == 2090), "预测-验收(月)"] = ''

        report_base['系统出货时间'] = pd.to_datetime(report_base['系统出货时间'], errors='coerce').dt.strftime(
            '%Y-%m-%d').astype(str)
        report_base['系统出货时间'] = ['' if i == '2090-01-01' else i for i in report_base['系统出货时间']]
        report_base['实际出货时间'] = pd.to_datetime(report_base['实际出货时间'], errors='coerce').dt.strftime(
            '%Y-%m-%d').astype(str)
        report_base['实际出货时间'] = ['' if i == '2090-01-01' else i for i in report_base['实际出货时间']]
        report_base['系统验收时间'] = pd.to_datetime(report_base['系统验收时间'], errors='coerce').dt.strftime(
            '%Y-%m-%d').astype(str)
        report_base['系统验收时间'] = ['' if i == '2090-01-01' else i for i in report_base['系统验收时间']]
        report_base['实际验收时间'] = pd.to_datetime(report_base['实际验收时间'], errors='coerce').dt.strftime(
            '%Y-%m-%d').astype(str)
        report_base['实际验收时间'] = ['' if i == '2090-01-01' else i for i in report_base['实际验收时间']]
        report_base['看板实际验收时间'] = pd.to_datetime(report_base['看板实际验收时间'], errors='coerce').dt.strftime(
            '%Y-%m-%d').astype(str)
        report_base['看板实际验收时间'] = ['' if i == '2090-01-01' else i for i in report_base['看板实际验收时间']]
        report_base['23年预算出货时间'] = pd.to_datetime(report_base['23年预算出货时间'], errors='coerce').dt.strftime(
            '%Y-%m-%d').astype(str)
        report_base['23年预算出货时间'] = ['' if i == '2090-01-01' else i for i in report_base['23年预算出货时间']]
        report_base['产品线计划验收时间'] = pd.to_datetime(report_base['产品线计划验收时间'], errors='coerce').dt.strftime(
            '%Y-%m-%d').astype(str)
        report_base['产品线计划验收时间'] = ['' if i == '2090-01-01' else i for i in report_base['产品线计划验收时间']]
        report_base['计划出货时间'] = pd.to_datetime(report_base['计划出货时间'], errors='coerce').dt.strftime(
            '%Y-%m-%d').astype(str)
        report_base['计划出货时间'] = ['' if i == '2090-01-01' else i for i in report_base['计划出货时间']]
        report_base['23年预算验收时间'] = pd.to_datetime(report_base['23年预算验收时间'], errors='coerce').dt.strftime(
            '%Y-%m-%d').astype(str)
        report_base['23年预算验收时间'] = ['' if i == '2090-01-01' else i for i in report_base['23年预算验收时间']]
        report_base['暂停时间'] = pd.to_datetime(report_base['暂停时间'], errors='coerce').dt.strftime('%Y-%m-%d').astype(
            str)
        report_base['暂停时间'] = ['' if i == '2090-01-01' else i for i in report_base['暂停时间']]
        report_base['重启时间'] = pd.to_datetime(report_base['重启时间'], errors='coerce').dt.strftime('%Y-%m-%d').astype(
            str)
        report_base['重启时间'] = ['' if i == '2090-01-01' else i for i in report_base['重启时间']]
        '''
        report_base.loc[report_base['实际出货时间']!='','计划出货时间']=report_base['实际出货时间']
        report_base.loc[report_base['实际验收时间']!='','计划验收时间']=report_base['实际验收时间']
        '''
        n3 = '是'
        if '是' in n3:
            report_base['集团收入'] = ''
            report_base['计划出货时间'] = ''

            path_n3 = r'周进度表-数据\数据源\N+3'
            file_n3 = os.listdir(path_n3)
            for name in file_n3:
                if name.count('~$') == 0:
                    report_n3 = pd.read_excel(path_n3 + '/' + name, header=2)[['序列号', '计划出货时间', '集团收入']]

            report_n3['序列号'] = report_n3['序列号'].fillna('')
            report_n3['计划出货时间'] = report_n3['计划出货时间'].fillna(default_date1)
            report_n3['计划出货时间'] = pd.to_datetime(report_n3['计划出货时间'], errors='coerce').dt.strftime(
                '%Y-%m-%d').astype(str)
            report_n3['集团收入'] = report_n3['集团收入'].fillna(0)
            report_n3['序列号'] = report_n3['序列号'].fillna('')

            report_base['计划出货时间'] = pd.merge(report_base, report_n3, on='序列号', how='left')['计划出货时间_y']
            report_base['集团收入'] = pd.merge(report_base, report_n3, on='序列号', how='left')['集团收入_y']

            report_base['计划出货时间'] = report_base['计划出货时间'].fillna(default_date1)
            report_base['计划出货时间'] = pd.to_datetime(report_base['计划出货时间'], errors='coerce').dt.strftime(
                '%Y-%m-%d').astype(str)
            report_base['计划出货时间'] = ['' if i == '2090-01-01' else i for i in report_base['计划出货时间']]

        report_base = report_base.fillna('')
        if '项目经理' not in report_base.columns:
            report_base['项目经理'] = ''
        report_base = report_base.reindex(columns=
                                          ["序列号", "设备类型", "客户简称", "大项目名称", "大项目号", "产品线名称", "核算项目号", "设备名称", "项目财经",
                                           "项目经理", "项目数量", "已出货数量"
                                              , "在产数量", "生产状态", "集团收入", "新增出货收入", "新增验收收入", "一般工单号601/608",
                                           "系统出货时间", "实际出货时间", "返工工单号603"
                                              , "系统验收时间", "实际验收时间", "看板实际验收时间", "项目号整理", "成品料号", "OA状态", "产品线整理",
                                           "客户整理", "区域", "项目阶段", "暂停时间"
                                              , "重启时间", "姓名", "23年预算出货时间", "计划出货时间", "23年预算验收时间", "产品线计划验收时间",
                                           "关键问题或风险点", "一览表进度", "是否有风险"
                                              , "原因分类", "原因大类", "原因小类", "PC备注", "生产实际进度", "风险等级", "风险分类", "验收实际进度",
                                           "实际-出货(年)", "预算-出货(季)", "预测-出货(季)"
                                              , "实际-出货(季)", "预算-出货(月)", "预测-出货(月)", "实际-出货(月)", "实际-验收(年)",
                                           "预算-验收(季)", "预测-验收(季)", "实际-验收(季)"
                                              , "预算-验收(月)", "预测-验收(月)", "实际-验收(月)"])
        ##########取用N+3

        #################################################################################################第五阶段
        print("第五阶段：数据输出..")
        screm_total.insert(INSERT, '\n第五阶段：数据输出..', '\n')
        window_total.update()

        def writer_contents(sheet, array, start_row, start_col, format=None, percent_format=None, percentlist=[]):
            # start_col = 0
            for col in array:
                if percentlist and (start_col in percentlist):
                    sheet.write_column(start_row, start_col, col, percent_format)
                else:
                    sheet.write_column(start_row, start_col, col, format)
                start_col += 1

        def write_color(book, sheet, data, fmt, col_num='I'):
            start = 3
            format_red = book.add_format({'font_name': 'Arial',
                                          'font_size': 10,
                                          'bg_color': '#F86470'})
            format_red.set_align('center')
            format_red.set_align('vcenter')
            for item in data:
                if '找不到' in str(item) in str(item):
                    sheet.write(col_num + str(start), item, format_red)
                else:
                    sheet.write(col_num + str(start), item, fmt)
                start += 1

        # 写入表格
        print('正在设置表格格式...')
        screm_total.insert(INSERT, '\n正在设置表格格式.....', '\n')
        window_total.update()
        now_time = time.strftime("%Y-%m-%d-%H", time.localtime(time.time()))
        book_name = '周进度表-数据\进度表' + now_time
        workbook = xlsxwriter.Workbook(book_name + '.xlsx', {'nan_inf_to_errors': True})
        worksheet1 = workbook.add_worksheet('明细表')
        title_format = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 10,
                                            'font_color': 'white',
                                            'bg_color': '#1F4E78',
                                            'bold': True,
                                            'bold': True,
                                            'align': 'center',
                                            'valign': 'vcenter',
                                            'border': 1,
                                            'border_color': 'white'
                                            })
        title_format.set_align('vcenter')
        title_format_hand = workbook.add_format({'font_name': 'Arial',
                                                 'font_size': 10,
                                                 'font_color': 'white',
                                                 'bg_color': '#963634',
                                                 'bold': True,
                                                 'bold': True,
                                                 'align': 'center',
                                                 'valign': 'vcenter',
                                                 'border': 1,
                                                 'border_color': 'white'
                                                 })
        title_format_hand.set_align('vcenter')
        col_format = workbook.add_format({'font_name': 'Arial',
                                          'font_size': 8,
                                          'font_color': 'white',
                                          'bg_color': '#595959',
                                          'text_wrap': True,
                                          'border': 1,
                                          'border_color': 'white',
                                          'align': 'center',
                                          'valign': 'vcenter'
                                          })

        data_format = workbook.add_format({'font_name': 'Arial',
                                           'font_size': 10,
                                           'align': 'left',
                                           'valign': 'vcenter'
                                           })
        data_format1 = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 10,
                                            'align': 'center',
                                            'valign': 'vcenter'
                                            })
        data_format2 = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 10,
                                            'align': 'center',
                                            'valign': 'vcenter'
                                            })
        data_format2.set_num_format('0.00')
        data_format3 = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 10,
                                            'align': 'center',
                                            'valign': 'vcenter'
                                            })
        data_format3.set_num_format('0.00%')
        num_percent_data_format = workbook.add_format({'font_name': 'Arial',
                                                       'font_size': 10,
                                                       'align': 'center',
                                                       'valign': 'vcenter',
                                                       'num_format': '0.00%'
                                                       })
        statis_format2 = workbook.add_format({'font_name': 'Arial',  # 系列总计
                                              'font_size': 9,
                                              'align': 'center',
                                              'valign': 'vcenter',
                                              'bg_color': '#92CDDC'
                                              })

        print('正在写入EXCEL表格...')
        worksheet1.merge_range('A1:BK1', '项目财经周计划汇总表', title_format)
        worksheet1.merge_range('A2:N2', '项目基本信息', title_format)
        worksheet1.merge_range('O2:Q2', '收入', title_format)
        worksheet1.merge_range('R2:T2', '出货', title_format)
        worksheet1.merge_range('U2:X2', '验收', title_format)
        worksheet1.merge_range('Y2:AH2', '验收', title_format)
        worksheet1.merge_range('AI2:AJ2', '计划出货', title_format)
        worksheet1.merge_range('AK2:AL2', '计划验收', title_format)
        worksheet1.merge_range('AM2:AN2', '项目一览表风险进度', title_format)
        worksheet1.merge_range('AO2:AT2', '生产进度', title_format)
        worksheet1.merge_range('AU2:AW2', '验收进度', title_format)

        worksheet1.write('AX2', '出货年份', title_format)
        worksheet1.merge_range('AY2:BA2', '出货季', title_format)
        worksheet1.merge_range('BB2:BD2', '出货月', title_format)
        worksheet1.write('BE2', '验收年份', title_format)
        worksheet1.merge_range('BF2:BH2', '验收季', title_format)
        worksheet1.merge_range('BI2:BK2', '验收月', title_format)
        worksheet1.write_row("A3", report_base.columns, title_format)
        writer_contents(sheet=worksheet1, array=report_base.T.values, start_row=3, start_col=0)
        worksheet1.set_row(0, 25)
        worksheet1.write_row('AD3:AI3', ['区域', '项目阶段', '暂停时间', '重启时间', '姓名', '23年预算出货时间'], title_format_hand)
        worksheet1.write('AK3', '23年预算验收时间', title_format_hand)
        worksheet1.write_row('AO3:AP3', ['是否有风险', '原因分类'], title_format_hand)
        worksheet1.write('AT3', '生产实际进度', title_format_hand)
        worksheet1.write('AW3', '验收实际进度', title_format_hand)

        print('明细表已写入。。。')
        workbook.close()
        print('执行完成无异常！！！！！')

        screm_total.insert(INSERT, '\n明细表已写入。。。', '\n')
        screm_total.insert(INSERT, '\n执行完成无异常！！！！！。', '\n')
        window_total.update()

    except Exception as f:
        # print('异常信息为:', e)  # 异常信息为: division by zero
        print('——#@*&程序报错，异常信息为:' + traceback.format_exc())
        screm_total.insert(INSERT, '\n——#@*&程序报错，异常信息为:' + traceback.format_exc(), '\n')
        window_total.update()

    time_end = time.time()
    print('执行总时长:%d秒' % (time_end - time_start))
    screm_total.insert(INSERT, '\n执行总时长:%d秒' % (time_end - time_start), '\n')


    screm_total.pack_forget()