#coding:utf-8
import zipfile
####################################################抬头
import threading
import pandas as pd
import numpy as np
import time
import os
import xlsxwriter
from pathlib import Path
import warnings
import tkinter as tk
import tkinter.messagebox #弹出框
from tkinter import *
from PIL import Image, ImageTk, ImageSequence
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import xlsxwriter
import traceback
import datetime
from tkinter import scrolledtext
from datetime import datetime
from picture import load_image
from wholeuse import*

def total():
    '''
    frame_total.create_image(740, 60, image=load_image(r'0、软件附带文件\框子.png'), anchor="n")
    '''
    screm_total = scrolledtext.ScrolledText(frame_total, bg='powderblue',  # 标签背景颜色
                                            highlightthickness=0,
                                            font=('微软雅黑', 12),  # 字体和字体大小
                                            width=72, height=14  # 标签长宽(以字符长度计算)
                                            )
    screm_total_total=frame_total.create_window(740, 246, window=screm_total)
    button_texts.append(screm_total_total)
    time_start = time.time()
    try:
        ###############################################################################################################################################一
        time_start = time.time()
        year1='2025'
        print("一、数据读取..")

        screm_total.insert(INSERT, '\n一、数据读取..', '\n')
        window_total.update()
        #########################读取核算进度底表
        screm_total.insert(INSERT, '\n1.1：正在读取核算进度底表..', '\n')
        filePath_base = r'7、核算&进度&汇总表底表'
        file_name_base = os.listdir(filePath_base)
        for i in range(len(file_name_base)):
            if str(file_name_base[i]).count('~$') == 0:
                # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
                base = pd.read_excel(filePath_base + '/' + str(file_name_base[i]))

        print("1.2：正在读取概预算表..")
        screm_total.insert(INSERT, '\n1.2：正在读取概预算表..', '\n')
        window_total.update()
        #########################读取概预算表
        path_line = r'4、概预核决汇总-数据\02：概预算'
        index = 0
        line = []
        line_file = os.listdir(path_line)
        for i in line_file:
            if '~$' in i:
                line_file.remove(i)
        for name in line_file:
            if 'xlsx' in name and '~$' not in name:
                budget_estimate = pd.read_excel(path_line + '\\' + name, sheet_name='概预算', header=1)
                #year_budget = pd.read_excel(path_line + '\\' + name, sheet_name='年初预算表', header=2)

        print("1.3：正在读取核算汇总表..")
        screm_total.insert(INSERT, '\n1.3：正在读取核算汇总表..', '\n')
        window_total.update()
        #########################读取核算汇总表
        path_calcu = r'4、概预核决汇总-数据\03：核算汇总表'
        calcu_file = os.listdir(path_calcu)
        for i in calcu_file:
            if '~$' in i:
                calcu_file.remove(i)
        for name in calcu_file:
            if 'xlsx' in name and '~$' not in name:
                calcu = pd.read_excel(path_calcu + '\\' + name, header=2)

     
        print("1.4：正在读取还需表..")
        screm_total.insert(INSERT, '\n1.4：正在读取还需表..', '\n')
        window_total.update()
        #########################读取周进度表
        path_need = r'4、概预核决汇总-数据\06：还需'
        need_file = os.listdir(path_need)
        for i in need_file:
            if '~$' in i:
                need_file.remove(i)
        for name in need_file:
            if 'xlsx' in name and '~$' not in name:
                need = pd.read_excel(path_need + '\\' + name)
                special_del = 0
            if '方装' in name:
                special_del=1

        print("1.5：正在读取Bom明细..")
        screm_total.insert(INSERT, '\n1.5：正在读取Bom明细..', '\n')
        window_total.update()
        #########################读取
        path_bom= r'4、概预核决汇总-数据\10：Bom明细'
        need_bom=['项目号','元件料号            ','模组用量','分类说明', '最近本币未税采购单价','最近成本单价        ']
        bom_file = os.listdir(path_bom)
        for i in bom_file:
            if '~$' in i:
                bom_file.remove(i)
        pd_bom=[]
        for name in bom_file:
            try:
                with open(os.path.join(path_bom, name), encoding='utf-8',errors='ignore') as f:
                    df_bom = pd.read_csv(f, sep=',', on_bad_lines='skip', low_memory=False, thousands=',')
            except pd.errors.EmptyDataError:
                df_bom=pd.DataFrame(columns=['项目号','元件料号            ','分类说明','模组用量', '最近本币未税采购单价','最近成本单价        '])
            if '项目号              ' in df_bom.columns:
                df_bom = df_bom.rename(columns={'项目号              ':'项目号'})
            if '模组用量            ' in df_bom.columns:
                df_bom = df_bom.rename(columns={'模组用量            ':'模组用量'})
            for col in need_bom:
                if col not in df_bom.columns:
                    df_bom[col]=''
            pd_bom.append(df_bom)
        if len(pd_bom)>0:
            bom=pd.concat(pd_bom).reset_index(drop=True)
        if len(pd_bom)==0:
            bom=pd.DataFrame(columns=['项目号','元件料号            ','分类说明','模组用量', '最近本币未税采购单价','最近成本单价        '])

        print("1.6：正在读取年初预算..")
        screm_total.insert(INSERT, '\n1.6：正在读取年初预算..', '\n')
        window_total.update()
        #########################读取
        path_year_budget = r'4、概预核决汇总-数据\04：年初预算'
        need_year_budget= ['序列号','收入-本年累计','成本合计','成本-料','成本-生产工','成本-交付工','成本-设计工','成本-费']
        year_budget_file = os.listdir(path_year_budget)
        for name in year_budget_file:
            if  '~$' not in name:
                year_budget= pd.read_excel( path_year_budget + '\\' + name,header=3)
        print("1.7：正在读取制费公摊期初数..")
        screm_total.insert(INSERT, '\n1.7：正在读取制费公摊期初数..', '\n')
        window_total.update()
        #########################读取
        path_average23 = r'4、概预核决汇总-数据\05：制费公摊期初数'
        need_average23 = ['序列号','23年制费公摊期初数']
        average23_file = os.listdir(path_average23)
        for name in average23_file:
            if '~$' not in name:
                average23 = pd.read_excel(path_average23 + '\\' + name)[need_average23].fillna('')
        average23=average23.drop_duplicates(subset=['序列号']).reset_index(drop=True)

        print("1.8：正在读取历史滚动表..")
        screm_total.insert(INSERT, '\n1.8：正在读取历史滚动表..', '\n')
        window_total.update()
        dir_scroll_old = r'4、概预核决汇总-数据\07：验收数据明细\1、历史滚动表'
        filename_scroll_old = os.listdir(dir_scroll_old)
        for i in filename_scroll_old:
            if '~$' in i:
                filename_scroll_old.remove(i)
        for name in filename_scroll_old:
            if 'xlsx' in name and '~$' not in name:
                scroll_old_all = pd.read_excel(dir_scroll_old + '\\' + name,  header=1)
                scroll_old_all = scroll_old_all.fillna('')
                old_rece = scroll_old_all[scroll_old_all['序列号'].str.contains('增值')].reset_index(drop=True)

                scroll_old = scroll_old_all[
                    ['序列号', '核算项目号', '验收分类', '验收年份（实际和最新预测）', '验收月份（实际和最新预测）','产品线整理-一级']].copy()
                scroll_2023_1 = scroll_old[(scroll_old['验收分类'].str.contains('2023')) & (scroll_old['序列号'] != "")& (scroll_old['序列号'].str.contains("增值")==False)].reset_index(drop=True)
                scroll_2023_1 = scroll_2023_1.drop_duplicates(subset=['序列号']).reset_index(drop=True)
                '''
                scroll_2023_2 = scroll_old[
                    (scroll_old['验收分类'].str.contains('2023')) & (scroll_old['序列号'] == "")].reset_index(drop=True)
                scroll_2023_2 = scroll_2023_2.drop_duplicates(subset=['核算项目号']).reset_index(drop=True)
                
                '''
                rece_2024_1 = scroll_old[(scroll_old['验收分类'].str.contains('2024')) & (scroll_old['序列号'] != "")& (scroll_old['序列号'].str.contains("增值")==False)].reset_index(drop=True)
                rece_2024_1 = rece_2024_1.drop_duplicates(subset=['序列号']).reset_index(drop=True)
            else:
                scroll_2023_1=pd.DataFrame(columns=['序列号', '核算项目号', '验收分类', '验收年份（实际和最新预测）', '验收月份（实际和最新预测）','产品线整理-一级'])
                scroll_2024_1 = pd.DataFrame(
                    columns=['序列号', '核算项目号', '验收分类', '验收年份（实际和最新预测）', '验收月份（实际和最新预测）',
                             '产品线整理-一级'])

        #############################################################################################################################读取历史滚动表
        #############################################################################################################################读取暂停清单
        print("1.9：正在读取暂停清单..")
        screm_total.insert(INSERT, '\n1.9：正在读取暂停清单..', '\n')
        window_total.update()
        dir_pause_list = r'4、概预核决汇总-数据\07：验收数据明细\3、暂停项目清单'
        filename_pause_list = os.listdir(dir_pause_list)
        for i in filename_pause_list:
            if '~$' in i:
                filename_pause_list.remove(i)
        for name in filename_pause_list:
            if 'xlsx' in name and '~$' not in name:
                writer = pd.ExcelFile(dir_pause_list + '\\' + name)
                for sheet_name in writer.sheet_names:
                    if '华东' in sheet_name:
                        pause_list_east =pd.read_excel(dir_pause_list + '\\' + name, sheet_name=sheet_name)[['大项目号', '核算项目号','一般工单号601/608']]
                        pause_list_east = pause_list_east.fillna('').astype(str)
                        pause_list_east['项目单号']=pause_list_east['核算项目号'].astype(str)+pause_list_east['一般工单号601/608'].astype(str)
                    if '华南' in sheet_name:
                        pause_list_north = pd.read_excel(dir_pause_list + '\\' + name, sheet_name=sheet_name)[['大项目号', '核算项目号','一般工单号601/608']]
                        pause_list_north = pause_list_north.fillna('').astype(str)
                        pause_list_north['项目单号'] = pause_list_north['核算项目号'].astype(str) + pause_list_north['一般工单号601/608'].astype(str)

        ###################################################################################################################################
        print("1.10：正在读取产品线对照表..")
        screm_total.insert(INSERT, '\n1.10：正在读取产品线对照表..', '\n')
        window_total.update()
        dir_line_list = r'4、概预核决汇总-数据\07：验收数据明细\2、产品线对照表'
        filename_line_list = os.listdir(dir_line_list)
        for i in filename_line_list:
            if '~$' in i:
                filename_line_list.remove(i)
        for name in filename_line_list:
            if 'xlsx' in name and '~$' not in name:
                line_list = pd.read_excel(dir_line_list + '\\' + name)
                line_list = line_list.fillna('')
                line_list = line_list.drop_duplicates(subset='产品线').reset_index(drop=True)

        #############################################################################################################################读取台账
        print("1.11：正在读取台账..")
        screm_total.insert(INSERT, '\n1.11：正在读取台账..', '\n')
        window_total.update()
        dir_bill = r'5、台账'
        filename_bill = os.listdir(dir_bill)
        for name in filename_bill:
            if '~$' not in name:
                writer = pd.ExcelFile(dir_bill + '\\' + name)
                for sheet_name in writer.sheet_names:
                    if '3.验收' in sheet_name:
                        bill_rece = pd.read_excel(dir_bill + '\\' + name, sheet_name=sheet_name)[['大项目名称', '大项目号', '项目号', '产品线', '料号', '系统录入日期', '本币未税金额']]
            else:
                bill_rece = pd.DataFrame(columns=['大项目名称', '大项目号', '项目号', '产品线', '料号', '系统录入日期', '本币未税金额'])

        ###################################################################################################################################在库
        # 原始路径保持不变
        dir_cybersecurity = r'4、概预核决汇总-数据\08、在库物料'
        # 检查目录是否存在，且目录中包含至少一个csv或xlsx文件
        if os.path.exists(dir_cybersecurity) and any(fname.lower().endswith(('.csv', '.xlsx')) for fname in os.listdir(dir_cybersecurity)):
            screm_total.insert(INSERT, '\n1.12：正在读取在库物料..', '\n')
            window_total.update()
            filename_cybersecurity= os.listdir(dir_cybersecurity)
            for i in filename_cybersecurity:
                if '~$' in i:
                    filename_cybersecurity.remove(i)
            for name in filename_cybersecurity:
                if 'xlsx' in name and '~$' not in name:
                    cybersecurity = pd.read_excel(dir_cybersecurity + '\\' + name)[['库存管理特征','库存数量','在拣量','平均单价']].fillna(0)
                    cybersecurity['库存管理特征'] = cybersecurity['库存管理特征'].str.strip()
                    cybersecurity['库存管理特征'] = cybersecurity['库存管理特征'].replace(' ', '', regex=True).astype(str)
                if 'csv' in name and '~$' not in name:
                    try:
                        cybersecurity = pd.read_csv(dir_cybersecurity + '\\' + name)[['库存管理特征','库存数量','在拣量','平均单价']].fillna(0)
                    except Exception as f:
                        cybersecurity = pd.read_csv(dir_cybersecurity + '\\' + name,encoding='gb18030')[
                            ['库存管理特征', '库存数量', '在拣量', '平均单价']].fillna(0)
                    cybersecurity['库存管理特征'] = cybersecurity['库存管理特征'].str.strip()
                    cybersecurity['库存管理特征'] = cybersecurity['库存管理特征'].replace(' ', '', regex=True).astype(str)
        else:
            cybersecurity = pd.DataFrame(columns=['库存管理特征','库存数量','在拣量','平均单价'])
        ###################################################################################################################################在库
        # 原始路径保持不变
        dir_in_transit  = r'4、概预核决汇总-数据\09、在途物料'
        # 检查目录是否存在，且目录中包含至少一个csv或xlsx文件
        if os.path.exists(dir_in_transit) and any(fname.lower().endswith(('.csv', '.xlsx')) for fname in os.listdir(dir_in_transit)):
            screm_total.insert(INSERT, '\n1.13：正在读取在途物料..', '\n')
            window_total.update()
            filename_in_transit= os.listdir(dir_in_transit)
            for i in filename_in_transit:
                if '~$' in i:
                    filename_in_transit.remove(i)
            for name in filename_in_transit:
                if 'xlsx' in name and '~$' not in name:
                    in_transit = pd.read_excel(dir_in_transit + '\\' + name)[['项目编号','采购单据状态','采购数量','行状态','未交量','税率','未税单价']].fillna(0)
                    for col in ['项目编号','状态码','行状态','采购单据状态','行状态','状态','采购单类型','品名','料件单号','采购供应商']:
                            in_transit[col] = in_transit[col].str.strip()
                            in_transit[col] = in_transit[col].replace(' ', '', regex=True).astype(str)
                if 'csv' in name and '~$' not in name:
                    try:
                        in_transit = pd.read_csv(dir_in_transit + '\\' + name)[['项目编号','采购单据状态','采购数量','行状态','采购单类型','品名','料件编号','采购供应商','未交量', '税前金额']].fillna(0)
                    except Exception as f:
                        in_transit = pd.read_csv(dir_in_transit + '\\' + name,encoding='gb18030')[
                            ['项目编号', '采购单据状态', '采购数量', '行状态',  '采购单类型', '品名', '料件编号',
                             '采购供应商', '未交量', '税前金额']].fillna(0)

                    for col in ['项目编号','行状态','采购单据状态','采购单类型','品名','料件编号','采购供应商']:
                            in_transit[col] = in_transit[col].str.strip()
                            in_transit[col] = in_transit[col].replace(' ', '', regex=True).astype(str)
        else:
            in_transit = pd.DataFrame(columns=['项目编号','采购单据状态','采购数量','行状态','采购单类型','品名','料件编号','采购供应商','未交量', '税前金额'])
        
        time_data_read = time.time()
        print('第一阶段【数据读取】执行时长:%d秒' % (time_data_read - time_start))
        screm_total.insert(INSERT, '\n第一阶段【数据读取】执行时长:%d秒' % (time_data_read - time_start), '\n')
        window_total.update()

        ######################################################################################################################################二
        print("二、数据格式处理..")
        screm_total.insert(INSERT, '\n二、数据格式处理..', '\n')
        window_total.update()

        base = base[['序列号', '区域', '行业中心', '设备类型', '客户简称', '大项目名称', '大项目号', '产品线名称', '产品线编码','核算项目号','项目经理', '设备名称', '项目数量'
            , '已出货数量', '在产数量', '生产状态', '集团收入', '软件收入',
                     '硬件收入', '一般工单号601/608', '工单开立时间', '工单完工时间', '系统出货时间', '实际出货时间','返工工单号603',
                     '系统验收时间', '实际验收时间', '项目号整理', '成品料号', '是否YY', 'OA状态', '自制/外包', '项目财经','子项目状态', '是否转移变更','来源台账判断']]
        print("2.1：核算进度底表..")
        screm_total.insert(INSERT, '\n2.1：核算进度底表..', '\n')
        window_total.update()
        base_str = ['序列号', '区域', '行业中心', '设备类型', '客户简称', '大项目名称', '大项目号', '产品线名称', '产品线编码',
                    '核算项目号','项目经理', '设备名称', '生产状态', '一般工单号601/608', '返工工单号603', '项目号整理', '成品料号', '是否YY',
                    'OA状态', '自制/外包', '项目财经', '子项目状态', '是否转移变更','来源台账判断']
        base[base_str] = base[base_str].fillna('')
        base['大项目名称'] = base['大项目名称'].str.strip()
        base['大项目名称'] = base['大项目名称'].replace(' ', '', regex=True).astype(str)
        base = base[base['设备类型'].str.contains('纯人力|增值改造') == False].reset_index(drop=True)
        base = base[base['是否转移变更'].str.contains('是') == False].reset_index(drop=True)

        base_num = ['项目数量', '已出货数量', '在产数量', '集团收入', '软件收入', '硬件收入']
        base[base_num] = base[base_num].fillna(0)

        base_date = ['工单开立时间', '工单完工时间', '系统出货时间', '实际出货时间', '系统验收时间', '实际验收时间']
        default_date = pd.Timestamp(1990, 1, 1)

        for dat in base_date:
            base[dat] = base[dat].fillna(default_date)
            base[dat] = pd.to_datetime(base[dat], errors='coerce')


        print("2.2：概预算表..")
        screm_total.insert(INSERT, '\n2.2：概预算表..', '\n')
        window_total.update()
        budget_estimate_str = ['大项目号', '项目号', '设备名称', '生产料号', '类型']
        budget_estimate[budget_estimate_str] = budget_estimate[budget_estimate_str].fillna('')
        budget_estimate['大项目号'] = budget_estimate['大项目号'].str.strip()
        budget_estimate['大项目号'] = budget_estimate['大项目号'].replace(' ', '', regex=True).astype(str)
        budget_estimate['项目号'] = budget_estimate['项目号'].str.strip()
        budget_estimate['项目号'] = budget_estimate['项目号'].replace(' ', '', regex=True).astype(str)

        budget_estimate_num = ['设备数量', '成本金额', '料', '生产工', '交付工', '设计工', '项目工', '其他', '制费']
        budget_estimate[budget_estimate_num] = budget_estimate[budget_estimate_num].fillna(0)
        estimate = budget_estimate[budget_estimate['类型'].str.contains('概算')].reset_index(drop=True)
        budget = budget_estimate[budget_estimate['类型'].str.contains('预算')].reset_index(drop=True)
        '''
        year_budget_str = ['项目号整理', '归属', '客户', '线体', '大项目', '产品线编码', '产品线', '核算项目号', '设备名称-整理',
                           '产能', '自制/外包', '生产主体', '销售主体', '业务', '项目经理', '产品经理', '产品\n类型', '全面预算\n有无']
        year_budget[year_budget_str] = year_budget[year_budget_str].fillna('')
        year_budget['线体'] = year_budget['线体'].str.strip()
        year_budget['线体'] = year_budget['线体'].replace(' ', '', regex=True).astype(str)
        year_budget['核算项目号'] = year_budget['核算项目号'].str.strip()
        year_budget['核算项目号'] = year_budget['核算项目号'].replace(' ', '', regex=True).astype(str)
        year_budget_num = ['数量', '成本合计', '料', '工', '生产工', '交付工', '费', '设计工', '其他费']
        year_budget[year_budget_num] = year_budget[year_budget_num].fillna(0)
        '''
        print("2.3：核算汇总表..")
        screm_total.insert(INSERT, '\n2.3：核算汇总表..', '\n')
        window_total.update()
        calcu = calcu[['序列号', '区域', '行业中心', '设备类型', '客户简称', '大项目名称', '大项目号', '产品线名称', '核算项目号',
                       '设备名称', '项目财经', '项目数量', '已出货数量', '在产数量', '生产状态', '集团收入', '软件收入', '硬件收入',
                       '成本', '毛利', '毛利率','料(采购单价)', '料', '工单料', '设变料', '采购PO',  '生产工', '交付工',  '设计工', '其他费',
                       '一般工单号601/608', '工单开立时间', '工单完工时间', '系统出货时间', '实际出货时间', '返工工单号603'
            , '系统验收时间', '实际验收时间', '项目号整理', '成品料号', '是否YY',  'OA状态','项目阶段','工时(生产交付)','生产工时','交付工时','设计工时',year1+'工时(生产交付)'
            ,year1+'生产工时',year1+'交付工时',year1+'设计工时','24年制费公摊金额','LOSS工成本','LOSS生产工','LOSS交付工','LOSS设计工','LOSS工时(生产交付)'
                    ,'LOSS生产工时','LOSS交付工时','LOSS设计工时',year1+'LOSS工时(生产交付)',year1+'LOSS生产工时',year1+'LOSS交付工时',year1+'LOSS设计工时']]

        calcu_str = ['序列号', '区域', '行业中心', '设备类型', '客户简称', '大项目名称', '大项目号', '产品线名称'
            , '核算项目号', '设备名称', '项目财经', '生产状态', '一般工单号601/608', '返工工单号603', '项目号整理'
            , '成品料号', '是否YY',  'OA状态','项目阶段']
        calcu[calcu_str] = calcu[calcu_str].fillna('')

        calcu_num = ['项目数量', '已出货数量', '在产数量', '集团收入', '软件收入', '硬件收入',
                     '成本', '毛利', '毛利率', '料', '工单料', '设变料', '采购PO', '生产工', '交付工', '设计工', '其他费','工时(生产交付)'
            ,'生产工时','交付工时','设计工时',year1+'工时(生产交付)',year1+'生产工时',year1+'交付工时',year1+'设计工时','24年制费公摊金额','LOSS工成本','LOSS生产工','LOSS交付工','LOSS设计工','LOSS工时(生产交付)'
                    ,'LOSS生产工时','LOSS交付工时','LOSS设计工时',year1+'LOSS工时(生产交付)',year1+'LOSS生产工时',year1+'LOSS交付工时',year1+'LOSS设计工时']
        calcu[calcu_num] = calcu[calcu_num].fillna(0)

        calcu_date = ['工单开立时间', '工单完工时间', '系统出货时间', '实际出货时间', '系统验收时间', '实际验收时间']
        default_date = pd.Timestamp(1990, 1, 1)
        for dat in calcu_date:
            calcu[dat] = calcu[dat].fillna(default_date)
            calcu[dat] = pd.to_datetime(calcu[dat], errors='coerce')
            #calcu[dat] = ['' if i == '1990-01-01' else i for i in calcu[dat]]
        '''
        print("2.4：财务成本表..")
        screm_total.insert(INSERT, '\n2.4：财务成本表..', '\n')
        window_total.update()
        finance = finance[['收入日期', '公司代码', '公司 \n简称', '中心', '内部关联交易', '收入类别', '销售类型', '行业类别',
                           '区域', '内销/外销', '是否报关（是/否）', '报关单号', 'PO（订单号）', '项目号', '客户名称', '业务员',
                           '产品编码', '产品名称', '规格型号', '数量', '合并收入', '合并料', '合并工', '合并费', '合并成本合计']]
        finance_str = ['收入日期', '公司代码', '公司 \n简称', '中心', '内部关联交易', '收入类别', '销售类型', '行业类别',
                       '区域', '内销/外销', '是否报关（是/否）', '报关单号', 'PO（订单号）', '项目号', '客户名称', '业务员', '产品编码', '产品名称', '规格型号']
        finance[finance_str] = finance[finance_str].fillna('')
        finance = finance[finance['项目号'] != ''].reset_index(drop=True)
        finance['项目号'] = finance['项目号'].str.strip()
        finance['项目号'] = finance['项目号'].replace(' ', '', regex=True).astype(str)

        finance_num = ['数量', '合并收入', '合并料', '合并工', '合并费', '合并成本合计']
        finance[finance_num] = finance[finance_num].fillna(0)
        finance[['合并收入', '合并料', '合并工', '合并费', '合并成本合计']] = finance[['合并收入', '合并料', '合并工', '合并费', '合并成本合计']] / 10000
        finance.loc[finance['项目号'].astype(str).str.contains('-R'), '数量'] = 0
        finance.loc[finance['产品编码'].astype(str).str.contains('311-'), '数量'] = 0

        print("2.5：周进度表..")
        screm_total.insert(INSERT, '\n2.5：周进度表..', '\n')
        window_total.update()
        advance = advance[['序列号', '设备类型', '客户简称', '大项目名称', '大项目号', '产品线名称', '核算项目号', '设备名称'
            , '项目财经', '项目经理', '项目数量', '已出货数量', '在产数量', '生产状态', '集团收入', '实际出货时间'
            , '实际验收时间', '看板实际验收时间', '项目号整理', '成品料号', 'OA状态', '区域', '项目阶段', '姓名'
            , '23年预算出货时间', '计划出货时间', '23年预算验收时间', '产品线计划验收时间', '关键问题或风险点'
            , '一览表进度', '是否有风险', '原因分类', '原因大类', '原因小类', 'PC备注', '生产实际进度', '风险等级'
            , '风险分类', '验收实际进度']]
        advance_str = ['序列号', '设备类型', '客户简称', '大项目名称', '大项目号', '产品线名称', '核算项目号', '设备名称'
            , '项目财经', '项目经理', '生产状态', '项目号整理', '成品料号', 'OA状态', '区域', '项目阶段', '姓名'
            , '关键问题或风险点', '一览表进度', '是否有风险', '原因分类', '原因大类', '原因小类', 'PC备注', '生产实际进度'
            , '风险等级', '风险分类', '验收实际进度']
        advance[advance_str] = advance[advance_str].fillna('')

        advance_num = ['项目数量', '已出货数量', '在产数量', '集团收入']
        advance[advance_num] = advance[advance_num].fillna(0)

        advance_date = ['实际出货时间', '实际验收时间', '看板实际验收时间', '23年预算出货时间'
            , '计划出货时间', '23年预算验收时间', '产品线计划验收时间']
        for dat in advance_date:
            advance[dat] = advance[dat].fillna(default_date)
            advance[dat] = pd.to_datetime(advance[dat], errors='coerce').dt.strftime('%Y-%m-%d').astype(str)
            advance[dat] = ['' if i == '1990-01-01' else i for i in advance[dat]]
        '''
        print("2.4：还需..")
        screm_total.insert(INSERT, '\n2.4：还需..', '\n')
        window_total.update()
        need = need[
            ['区域', '客户简称', '大项目名称', '大项目号', '产品线名称', '核算项目号', '已出货未验收数量', '成本', '料',  '生产工', '交付工', '设计工',
             '其他费']]
        need_str = ['区域', '客户简称', '大项目名称', '大项目号', '产品线名称', '核算项目号']
        need[need_str] = need[need_str].fillna('')
        need['核算项目号'] = need['核算项目号'].str.strip()
        need['核算项目号'] = need['核算项目号'].replace(' ', '', regex=True).astype(str)
        need['大项目号'] = need['大项目号'].str.strip()
        need['大项目号'] = need['大项目号'].replace(' ', '', regex=True).astype(str)
        need['大项目名称'] = need['大项目名称'].str.strip()
        need['大项目名称'] = need['大项目名称'].replace(' ', '', regex=True).astype(str)
        ######暂不填充，华南空值取预算-核算
        '''
        need_num = ['已出货未验收数量', '成本', '料', '工', '生产工', '交付工', '费', '设计工', '其他费']
        need[need_num] = need[need_num].fillna(0)
        '''
        print("2.5：Bom明细..")
        screm_total.insert(INSERT, '\n2.5：Bom明细..', '\n')
        window_total.update()
        bom[['模组用量', '最近本币未税采购单价','最近成本单价        ']] = bom[['模组用量','最近本币未税采购单价','最近成本单价        ']].fillna(0)
        bom = bom.fillna('')

        bom['项目号整理'] = ''
        if len(bom[bom['项目号'].str.contains('-')]) > 0:
            bom['项目整'] = bom['项目号'].str.split('-', expand=True)[0]
            bom['项目整1'] = bom['项目号'].str.split('-', expand=True)[1]
            bom['项目整1'] = bom['项目整1'].fillna('空值')
            bom['项目号整理'] = bom['项目整']
            bom.loc[(bom['项目整1'].str.isdigit()) | (bom['项目整1'].str.contains('SH')), '项目号整理'] = bom['项目号整理'] + '-' + bom['项目整1']
            del bom['项目整']
            del bom['项目整1']
        else:
            bom['项目号整理'] = bom['项目号']
        bom.loc[(bom['项目号整理'].str[0] == 'F') & (bom['项目号整理'].str[:3].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX')), '项目号整理'] = \
            bom['项目号整理'].str[3:]
        bom.loc[(bom['项目号整理'].str[:2].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX', na=False)), '项目号整理'] = bom['项目号整理'].str[2:]
        bom=bom[bom['元件料号            '].str[:4].str.contains('F5')==False].reset_index(drop=True)
        bom = bom[bom['分类说明'].str.contains('模组') == False].reset_index(drop=True)
        bom['使用单价']=bom['最近本币未税采购单价'].copy()
        bom.loc[bom['使用单价']==0,'使用单价']=bom['最近成本单价        ']
        bom['金额'] = bom['模组用量'] * bom['使用单价']/10000
        bom1 = bom[["项目号整理", '金额']]
        bom_use = pd.DataFrame(bom1.groupby(['项目号整理'])['金额'].sum()).add_suffix('').reset_index()

        print("2.6：年初预算..")
        screm_total.insert(INSERT, '\n2.6：年初预算..', '\n')
        window_total.update()
        year_budget[['成本合计','收入-本年累计','成本-料','成本-生产工','成本-交付工','成本-设计工','成本-费']] =year_budget[['成本合计','收入-本年累计','成本-料','成本-生产工','成本-交付工','成本-设计工','成本-费']].fillna(0)
        year_budget =year_budget.fillna('')

        print("2.7：台账验收表")
        screm_total.insert(INSERT, '\n2.7：台账验收表..', '\n')
        window_total.update()
        bill_rece['系统录入日期'] = bill_rece['系统录入日期'].fillna(default_date)
        bill_rece['系统录入日期'] = pd.to_datetime(bill_rece['系统录入日期'], errors='coerce')
        bill_rece['本币未税金额'] = bill_rece['本币未税金额'].fillna(0) / 10000
        bill_rece = bill_rece.fillna('')
        bill_rece = bill_rece[(bill_rece['大项目名称'].str.contains('增值改造')) | (
                bill_rece['大项目名称'] == '/')].reset_index(drop=True)
        current_date = datetime.now()
        # 获取上个月的第一天
        first_day_last_month = (current_date.replace(day=1) - relativedelta(months=1))
        # 获取这个月第一天
        first_day_this_month = (current_date.replace(day=1))
        bill_rece = bill_rece[
            (bill_rece['系统录入日期'] < pd.Timestamp(first_day_this_month.strftime('%Y-%m-%d'))) & (
                    bill_rece['系统录入日期'] >= pd.Timestamp(
                first_day_last_month.strftime('%Y-%m-%d')))].reset_index(drop=True)
        bill_rece=bill_rece[bill_rece['产品线'].isin(line_list['产品线'])].reset_index(drop=True)
        bill_rece['产品线整理-一级'] = pd.merge(bill_rece, line_list, on='产品线', how='left')[
            '产品线整理-一级'].fillna('')
        bill_rece.loc[bill_rece['产品线整理-一级'] == '', '产品线整理-一级'] = bill_rece['产品线']
        bill_rece_group = bill_rece.groupby(['产品线整理-一级']).agg(
            {"本币未税金额": "sum"}).add_suffix('').reset_index()
        bill_rece_group['序列号'] = str(int(first_day_last_month.strftime('%m'))) + '月增值改造验收'
        bill_rece_group['区域'] = '国内'
        bill_rece_group['验收分类'] = str(int(first_day_last_month.strftime('%Y'))) + '年增值改造验收'
        bill_rece_group['验收年份（实际和最新预测）'] = str(int(first_day_last_month.strftime('%Y')))
        bill_rece_group['验收月份（实际和最新预测）'] = str(int(first_day_last_month.strftime('%m')))
        bill_rece_group['生产状态'] = '已验收'

        if len(in_transit) > 0:
            screm_total.insert(INSERT, '\n2.8：在途物料成本..', '\n')
            window_total.update()
            in_transit = in_transit[(in_transit['采购单据状态'].str.contains('已审核|留置|送签中'))&(in_transit['行状态'].str.contains('一般|留置'))&(in_transit['未交量']>0)].reset_index(drop=True)
            in_transit['在途物料成本'] = in_transit['未交量'] * in_transit['税前金额']/(in_transit['采购数量']*10000)
            ##剔除
            in_transit.loc[
                (in_transit['采购供应商'].str.contains('海目星（江门）激光智能装备|海目星激光科技集团股份有限')) & (
                    in_transit['品名'].str[-4:].str.contains('激光器')), '在途物料成本'] = 0
            in_transit.loc[(in_transit['采购单类型'].str.contains('期初采购单')), '在途物料成本'] = 0
            in_transit.loc[in_transit['料件编号'].str.contains(
                "S31-AS10072|S31-AS10527|S31-AS10873|S31-AS10781|S31-AS10802|S31-AS10849|S31-AS10801|S14-AS10051|S31-AS10593"), '在途物料成本'] = 0

            in_transit.loc[in_transit['采购单类型'].str.contains(
                "多角采购a\(LEBG-->LEBGJM销售\)|多角采购m\(LEBG-->LEBGJM销售\)内外价|多角采购p\(LEBGJS-->LEBGJM销售\)|多角采购n\(LEBG-->LEBGJS销售\)内外价|多角采购e\(LEBG-->LEBGJS销售\)|多角采购t\(LEBG-->LEBGHX销售\)人民币"),'在途物料成本'] = 0
            in_transit.loc[in_transit['采购单类型'].str.contains(
                "多角采购t\(LEBG-->LEBGHX销售\)人民币|关联交易采购订单|多角采购u\(LEBG-->LEBGHX销售\)外币|多角采购b\(LEBGJS-->LEBGHX销售\)人民币|多角采购p\(LEBGJS-->LEBGJM销售\)内外价|多角采购s\(LEBGJS-->LEBG销售\)|集采多角采购q\(LEBG-->LEBGJM销售\)"), '在途物料成本'] = 0

            in_transit_group = in_transit.groupby(['项目编号']).agg(
                {"在途物料成本": "sum"}).add_suffix('').reset_index()
        else:
            in_transit_group = pd.DataFrame(columns=['项目编号','在途物料成本'])
        if len(cybersecurity) > 0:
            screm_total.insert(INSERT, '\n2.9：在库物料成本..', '\n')
            window_total.update()

            cybersecurity['在库物料成本'] = (cybersecurity['库存数量']-cybersecurity['在拣量']) * cybersecurity['平均单价']/10000
            cybersecurity_group = cybersecurity.groupby(['库存管理特征']).agg(
                {"在库物料成本": "sum"}).add_suffix('').reset_index()
        else:
            cybersecurity_group = pd.DataFrame(columns=['库存管理特征','在库物料成本']) 

        time_data_format = time.time()
        print('第二阶段【数据格式处理】执行时长:%d秒' % (time_data_format - time_data_read))
        screm_total.insert(INSERT, '\n第二阶段【数据格式处理】执行时长:%d秒' % (time_data_format - time_data_read), '\n')
        window_total.update()

        ######################################################################################################################################三
        print("三、生成概预核决汇总表..")
        print("3.1：生成概预核决汇总表基础数据(根据底表)..")
        screm_total.insert(INSERT, '\n三、生成概预核决汇总表..', '\n')
        screm_total.insert(INSERT, '\n3.1：生成概预核决汇总表基础数据(根据底表)..', '\n')
        window_total.update()
        total_out = base.copy()
        total_out = total_out[['序列号', '区域', '行业中心', '客户简称', '大项目名称', '大项目号', '产品线名称'
            , '核算项目号','项目经理', '设备名称', '项目数量', '已出货数量', '在产数量', '生产状态', '设备类型', '集团收入'
            , '软件收入', '硬件收入', '成品料号', '一般工单号601/608', '返工工单号603', '实际出货时间'
            , '实际验收时间', '系统验收时间', '项目号整理', '是否YY',  '子项目状态','OA状态','项目财经','来源台账判断']]

        print("3.2：拉取概算数据..")
        screm_total.insert(INSERT, '\n3.2：拉取概算数据..', '\n')
        window_total.update()
        total_out['大小项目'] = total_out['大项目号'] + total_out['核算项目号']
        total_out_group = total_out.groupby(['大小项目']).agg({'核算项目号': "count"}).add_suffix('数量').reset_index()
        total_out['大小项目数量'] = pd.merge(total_out, total_out_group, on='大小项目', how='left')['核算项目号数量']

        estimate['大小项目'] = estimate['大项目号'] + estimate['项目号']
        estimate_group = estimate.groupby(['大小项目']).agg(
            {'成本金额': "sum", '料': "sum", '生产工': "sum", '交付工': "sum", '设计工': "sum", '制费': "sum"}).add_suffix(
            '').reset_index()
        total_out[['成本合计-概算', '料-概算', '生产工-概算', '交付工-概算', '设计工-概算', '制费-概算']] = \
            pd.merge(total_out, estimate_group, on='大小项目', how='left')[['成本金额', '料', '生产工', '交付工', '设计工', '制费']]

        estimate_num = ['成本合计-概算', '料-概算', '生产工-概算', '交付工-概算', '设计工-概算', '制费-概算']
        for num in estimate_num:
            total_out[num] = total_out[num] / total_out['大小项目数量']

        print("3.3：拉取预算数据..")
        screm_total.insert(INSERT, '\n3.3：拉取预算数据..', '\n')
        window_total.update()
        budget['大小项目'] = budget['大项目号'] + budget['项目号']
        budget_group = budget.groupby(['大小项目']).agg(
            {'成本金额': "sum", '料': "sum", '生产工': "sum", '交付工': "sum", '设计工': "sum", '制费': "sum"}).add_suffix(
            '').reset_index()

        total_out[['成本合计-预算', '料-预算', '生产工-预算', '交付工-预算', '设计工-预算', '制费-预算']] = \
            pd.merge(total_out, budget_group, on='大小项目', how='left')[['成本金额', '料', '生产工', '交付工', '设计工', '制费']]

        budget_num = ['成本合计-预算', '料-预算', '生产工-预算', '交付工-预算', '设计工-预算', '制费-预算']
        for num in budget_num:
            total_out[num] = total_out[num] / total_out['大小项目数量']
        '''
        print("3.4：拉取财务成本表..")
        screm_total.insert(INSERT, '\n3.4：拉取财务成本表..', '\n')
        window_total.update()
        finance_use = finance[['项目号', '数量', '合并料', '合并工', '合并费', '合并成本合计']]
        finance_use['项目号'] = finance_use['项目号'].fillna('').astype(str)
        if len(finance_use[finance_use['项目号'].str.contains('-')]) > 0:
            finance_use['项目号整理'] = ''
            finance_use['项目整'] = finance_use['项目号'].str.split('-', expand=True)[0]
            finance_use['项目整1'] = finance_use['项目号'].str.split('-', expand=True)[1]
            finance_use['项目整1'] = finance_use['项目整1'].fillna('空值')
            finance_use['项目号整理'] = finance_use['项目整']
            finance_use.loc[
                (finance_use['项目整1'].str.isdigit()) | (finance_use['项目整1'].str.contains('SH')), '项目号整理'] = \
                finance_use['项目号整理'] + '-' + finance_use['项目整1']
        if len(finance_use[finance_use['项目号'].str.contains('-')]) == 0:
            finance_use['项目号整理'] = finance_use['项目号']
        finance_use.loc[(finance_use['项目号整理'].str[0] == 'F') & (
            finance_use['项目号整理'].str[:3].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX')), '项目号整理'] = finance_use['项目号整理'].str[3:]
        finance_use.loc[(finance_use['项目号整理'].str[:2].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX',na=False)), '项目号整理'] =finance_use['项目号整理'].str[2:]
        finance_use_group = finance_use.groupby(['项目号整理']).agg(
            {'数量': 'sum', '合并成本合计': "sum", '合并料': "sum", '合并工': "sum", '合并费': "sum"}).add_suffix('').reset_index()
        finance_num = ['合并料', '合并工', '合并费', '合并成本合计']
        for num in finance_num:
            finance_use_group.loc[finance_use_group['数量'] != 0, num] = finance_use_group[num] / finance_use_group[
                '数量']
        total_out[['成本合计-财务', '料-财务', '工-财务', '费-财务']] = \
            pd.merge(total_out, finance_use_group, on=['项目号整理'], how='left')[['合并成本合计', '合并料', '合并工', '合并费']]
        '''
        print("3.4：拉取核算汇总表..")
        screm_total.insert(INSERT, '\n3.4：拉取核算汇总表..', '\n')
        window_total.update()
        calcu_use = calcu[['序列号', '采购PO', '成本','料(采购单价)', '料', '工单料', '设变料', '生产工', '交付工', '设计工', '其他费', '毛利', '毛利率','工时(生产交付)','生产工时','交付工时','设计工时','项目阶段',year1+'工时(生产交付)',year1+'生产工时',year1+'交付工时',year1+'设计工时','24年制费公摊金额'
            ,'LOSS工成本','LOSS生产工','LOSS交付工','LOSS设计工','LOSS工时(生产交付)'
                ,'LOSS生产工时','LOSS交付工时','LOSS设计工时',year1+'LOSS工时(生产交付)',year1+'LOSS生产工时',year1+'LOSS交付工时',year1+'LOSS设计工时']]
        calcu_use=calcu_use.drop_duplicates(subset=['序列号']).reset_index(drop=True)
        total_out[
            ['采购PO', '成本合计-核算','料(采购单价)', '料-核算', '工单料', '设变料',  '生产工-核算', '交付工-核算', '设计工-核算', '其他费-核算',
             '毛利-核算','毛利率-核算','工时(生产交付)','生产工时','交付工时','设计工时','项目阶段',year1+'工时(生产交付)',year1+'生产工时',year1+'交付工时',year1+'设计工时','24年制费公摊金额','LOSS工成本','LOSS生产工','LOSS交付工','LOSS设计工','LOSS工时(生产交付)'
                ,'LOSS生产工时','LOSS交付工时','LOSS设计工时',year1+'LOSS工时(生产交付)',year1+'LOSS生产工时',year1+'LOSS交付工时',year1+'LOSS设计工时']] = pd.merge(total_out, calcu_use, on='序列号', how='left')[
            ['采购PO', '成本','料(采购单价)', '料', '工单料', '设变料', '生产工', '交付工', '设计工', '其他费', '毛利', '毛利率','工时(生产交付)','生产工时','交付工时','设计工时','项目阶段',year1+'工时(生产交付)',year1+'生产工时',year1+'交付工时',year1+'设计工时','24年制费公摊金额'
                ,'LOSS工成本','LOSS生产工','LOSS交付工','LOSS设计工','LOSS工时(生产交付)'
                ,'LOSS生产工时','LOSS交付工时','LOSS设计工时',year1+'LOSS工时(生产交付)',year1+'LOSS生产工时',year1+'LOSS交付工时',year1+'LOSS设计工时']]
        total_out['项目阶段']=total_out['项目阶段'].fillna('')

        print("3.5：拉取年初预算..")
        screm_total.insert(INSERT, '\n3.5：拉取年初预算..', '\n')
        window_total.update()
        year_budget_use = year_budget[year_budget['序列号']!=""].reset_index(drop=True)
        year_budget_use=year_budget_use.drop_duplicates(subset=['序列号']).reset_index(drop=True)
        total_out[['年初合计','收入-年初', '料-年初','生产工-年初','交付工-年初','设计工-年初','费-年初']] = pd.merge(total_out, year_budget_use, on='序列号', how='left')[['成本合计','收入-本年累计','成本-料','成本-生产工','成本-交付工','成本-设计工','成本-费']]

        print("3.6：拉取还需表..")
        screm_total.insert(INSERT, '\n3.6：拉取还需表..', '\n')
        window_total.update()
        ####给华东还需做项目号整理
        need_use = need[['大项目名称', '大项目号', '核算项目号', '成本', '料',  '生产工', '交付工', '设计工', '其他费', '区域']]
        if len(need_use[need_use['核算项目号'].str.contains('-')]) > 0:
            need_use['项目号整理'] = ''
            need_use['项目整'] = need_use['核算项目号'].str.split('-', expand=True)[0]
            need_use['项目整1'] = need_use['核算项目号'].str.split('-', expand=True)[1]
            need_use['项目整1'] = need_use['项目整1'].fillna('空值')
            need_use['项目号整理'] = need_use['项目整']
            need_use.loc[(need_use['项目整1'].str.isdigit()) | (need_use['项目整1'].str.contains('SH')), '项目号整理'] = \
                need_use['项目号整理'] + '-' + need_use['项目整1']
        if len(need_use[need_use['核算项目号'].str.contains('-')]) == 0:
            need_use['项目号整理'] = need_use['核算项目号']
        need_use.loc[(need_use['项目号整理'].str[0] == 'F') & (
            need_use['项目号整理'].str[:3].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX')), '项目号整理'] = need_use['项目号整理'].str[3:]
        need_use.loc[(need_use['项目号整理'].str[:2].str.contains('JM|JS|SZ|jm|jM|Jm|js|Js|jS|Sz|sz|sZ|HX', na=False)), '项目号整理'] = \
            need_use['项目号整理'].str[2:]

        need_east = need_use[need_use['区域'].str.contains('华东')].reset_index(drop=True)
        need_north = need_use[need_use['区域'].str.contains('华南')].reset_index(drop=True)
        need_num = ['成本', '料',  '生产工', '交付工',  '设计工', '其他费']
        need_east[need_num] = need_east[need_num].fillna(0)
        need_north[['成本', '料',  '生产工', '交付工']]= need_north[['成本', '料', '生产工', '交付工']].fillna(0)
        need_north[['设计工', '其他费']]=need_north[[ '设计工', '其他费']].fillna(987654321)

        need_north['大小项目号整理'] = need_north['大项目号'] + need_north['项目号整理']
        need_north__1=need_north[need_north['设计工']==987654321].reset_index(drop=True)
        need_north__2= need_north[need_north['其他费'] == 987654321].reset_index(drop=True)

        need_north_group = need_north.groupby(['大小项目号整理']).agg({'交付工': "sum", '其他费': "sum", '设计工': "sum",'料':'sum'}).add_suffix('').reset_index()  ####华南大小项目号
        need_east_group = need_east.groupby(['项目号整理']).agg(
            {'成本': "sum", '料': "sum",  '生产工': "sum", '交付工': "sum",  '设计工': "sum",'其他费': "sum"}).add_suffix('').reset_index()  ####大项目名称

        #####将汇总表排序并做拆分
        total_out['排个序'] = 0
        for i in range(len(total_out)):
            total_out.loc[i, '排个序'] = i
            i = i + 1
        ####拆出未出货、已出货未验收、已验收、子项目
        ##在产
        total_out_a = total_out[total_out['生产状态'].str.contains('在产')].reset_index(drop=True)  #####预算-核算
        ###出货未验收
        total_out_b1 = total_out[total_out['生产状态'].str.contains('已出货')].reset_index(drop=True)
        total_out_b2 = total_out[(total_out['生产状态'].str.contains('YY')) ].reset_index(drop=True)
        total_out_b = pd.concat([total_out_b1, total_out_b2]).reset_index(drop=True)
        # 子项目
        total_out_c = total_out[total_out['生产状态'].str.contains('子项目')].reset_index(drop=True)
        # 已验收
        total_out_d = total_out[(total_out['生产状态'].str.contains('已验收'))].reset_index(drop=True)
        #####华东
        total_out_b3 = total_out_b[total_out_b['产品线名称'].str.contains('华东|制片产品线|激光切产品线|涂布辊压分切产品线|激光表面处理产品线|卷绕产品线|叠片产品线|模组pack产品线|研究院')].reset_index(drop=True)
        # total_out_b3=total_out_b[total_out_b['项目号整理'].isin(need_east_group['项目号整理'])].reset_index(drop=True)
        total_out_b3_group = total_out_b3.groupby(['项目号整理']).agg({'项目数量': "sum"}).add_suffix('统计').reset_index()  ####h核算项目号
        total_out_b3['小项目数量'] = pd.merge(total_out_b3, total_out_b3_group, on='项目号整理', how='left')['项目数量统计']
        #####华南
        # total_out_b4= total_out_b[~total_out_b['项目号整理'].isin(need_east_group['项目号整理'])].reset_index(drop=True)
        total_out_b4 = total_out_b[total_out_b['产品线名称'].str.contains('华南|方形装配产品线|激光A产品线|激光B产品线|自动化A产品线|自动化B产品线|装配产品线|圆柱装配产品线|搬运干燥产品线|其他|大装配')].reset_index(drop=True)
        total_out_b4['大小项目号整理'] = total_out_b4['大项目号'] + total_out_b4['项目号整理']
        total_out_b4_group = total_out_b4.groupby(['大小项目号整理']).agg({'项目数量': "sum"}).add_suffix(
            '统计').reset_index()  ####大项目名称
        total_out_b4['大小项目号整理数量'] = pd.merge(total_out_b4, total_out_b4_group, on='大小项目号整理', how='left')['项目数量统计']

        total_out_b3[['成本合计-还需', '料-还需', '生产工-还需', '交付工-还需',  '设计工-还需', '其他费-还需']] = \
            pd.merge(total_out_b3, need_east_group, on='项目号整理', how='left')[
                ['成本', '料',  '生产工', '交付工',  '设计工', '其他费']]

        total_out_b4[['交付工-还需','其他费-还需','设计工-还需', '料-还需']] = pd.merge(total_out_b4, need_north_group, on='大小项目号整理', how='left')[['交付工','其他费','设计工', '料']]
        total_out_b4.loc[~total_out_b4['大小项目号整理'].isin(need_north_group['大小项目号整理']), '交付工-还需'] = total_out_b4['交付工-预算'] -total_out_b4['交付工-核算']
        total_out_b4.loc[~total_out_b4['大小项目号整理'].isin(need_north_group['大小项目号整理']), '其他费-还需'] = total_out_b4['制费-预算'] -total_out_b4['其他费-核算']
        total_out_b4.loc[~total_out_b4['大小项目号整理'].isin(need_north_group['大小项目号整理']), '设计工-还需'] = total_out_b4['设计工-预算'] - total_out_b4['设计工-核算']

        total_out_b4['交付工-还需'] = total_out_b4['交付工-还需'].fillna(0)
        total_out_b4['其他费-还需'] = total_out_b4['其他费-还需'].fillna(0)
        total_out_b4['设计工-还需'] = total_out_b4['设计工-还需'].fillna(0)
        total_out_b4['料-还需'] = total_out_b4['料-还需'].fillna(0)

        total_out_b4.loc[total_out_b4['大小项目号整理'].isin(need_north_group['大小项目号整理']), '交付工-还需']  = total_out_b4['交付工-还需'] / total_out_b4['大小项目号整理数量']
        total_out_b4.loc[total_out_b4['大小项目号整理'].isin(need_north_group['大小项目号整理']), '其他费-还需']= total_out_b4['其他费-还需'] / total_out_b4['大小项目号整理数量']
        total_out_b4.loc[total_out_b4['大小项目号整理'].isin(need_north_group['大小项目号整理']), '设计工-还需'] = total_out_b4['设计工-还需'] / total_out_b4['大小项目号整理数量']
        total_out_b4.loc[total_out_b4['大小项目号整理'].isin(need_north_group['大小项目号整理']),'料-还需'] = total_out_b4['料-还需'] / total_out_b4['大小项目号整理数量']
        #########空值转填
        total_out_b4.loc[total_out_b4['大小项目号整理'].isin(need_north__2['大小项目号整理']), '其他费-还需'] = \
            total_out_b4['制费-预算'] - total_out_b4['其他费-核算']
        total_out_b4.loc[total_out_b4['大小项目号整理'].isin(need_north__1['大小项目号整理']), '设计工-还需'] = \
            total_out_b4['设计工-预算'] - total_out_b4['设计工-核算']

        if special_del==0:
            total_out_b4['料-还需'] = 0
        total_out_b4['生产工-还需'] = 0
        #total_out_b4['其他费-还需'] = total_out_b4['制费-预算'] - total_out_b4['其他费-核算']
        #total_out_b4['其他费-还需'] = total_out_b4['制费-预算'] - total_out_b4['其他费-核算']        #total_out_b4['工-还需'] = total_out_b4['交付工-还需'] + total_out_b4['生产工-还需']
        total_out_b4['成本合计-还需'] = total_out_b4['交付工-还需'] + total_out_b4['生产工-还需']+ total_out_b4['其他费-还需'] + total_out_b4['设计工-还需']+ total_out_b4['料-还需']

        total_out_b3[['成本合计-还需', '料-还需', '生产工-还需', '交付工-还需',  '设计工-还需', '其他费-还需']] = total_out_b3[
            ['成本合计-还需', '料-还需', '生产工-还需', '交付工-还需', '设计工-还需', '其他费-还需']].fillna(0)
        for num in ['成本合计', '料','生产工', '交付工', '设计工', '其他费']:
            total_out_b3[num + '-还需'] = total_out_b3[num + '-还需'] / total_out_b3['小项目数量']
        for num in ['成本合计', '交付工', '设计工']:
            total_out_b3.loc[~total_out_b3['项目号整理'].isin(need_east_group['项目号整理']), num + '-还需'] = total_out_b3[num + '-预算'] - total_out_b3[num + '-核算']
        total_out_b3.loc[~total_out_b3['项目号整理'].isin(need_east_group['项目号整理']), '其他费-还需'] = total_out_b3['制费-预算'] - total_out_b3['其他费-核算']
        ##未知华东华南之外
        total_out_b5=total_out_b[(~total_out_b['产品线名称'].isin(total_out_b3['产品线名称']))&(~total_out_b['产品线名称'].isin(total_out_b4['产品线名称']))].reset_index(drop=True)
        ###已验收
        for num in ['成本合计-还需', '料-还需',  '生产工-还需', '交付工-还需', '设计工-还需', '其他费-还需']:
            total_out_d[num] = 0
        ###子项目
        total_out_c['成本合计-还需'] = -1 * total_out_c['成本合计-核算']
        total_out_c['料-还需'] = -1 * total_out_c['料-核算']
        #total_out_c['工-还需'] = -1 * total_out_c['工-核算']
        #total_out_c['费-还需'] = -1 * total_out_c['费-核算']
        total_out_c['设计工-还需'] = -1 * total_out_c['设计工-核算']
        total_out_c['其他费-还需'] = -1 * total_out_c['其他费-核算']
        total_out_c['核算项目号']=total_out_c['核算项目号'].fillna('')
        total_out_c['料-还需']=0
        total_out_c['交付工-还需'] = 0
        total_out_c['生产工-还需'] = 0

        for num in ['成本合计-还需', '料-还需',  '生产工-还需', '交付工-还需', '设计工-还需', '其他费-还需']:
            total_out_c.loc[total_out_c['子项目状态'].str.contains('验收'), num] = 0
            total_out_c.loc[total_out_c['核算项目号'].str[:3].str.contains('JS'), num] = 0
            total_out_c[num]=0
        ###未出货
        total_out_a['成本合计-还需'] = total_out_a['成本合计-预算'] - total_out_a['成本合计-核算']
        total_out_a['料-还需'] = total_out_a['料-预算'] - total_out_a['料-核算']
        total_out_a['生产工-还需'] = total_out_a['生产工-预算'] - total_out_a['生产工-核算']
        total_out_a['交付工-还需'] = total_out_a['交付工-预算'] - total_out_a['交付工-核算']

        total_out_a['设计工-还需'] = total_out_a['设计工-预算'] - total_out_a['设计工-核算']
        total_out_a['其他费-还需'] = total_out_a['制费-预算'] - total_out_a['其他费-核算']

        total_out = pd.concat([total_out_a, total_out_b3, total_out_b4,total_out_b5, total_out_c, total_out_d]).reset_index(drop=True)
        total_out = total_out.sort_values(by=['排个序'], ascending=True).reset_index(drop=True)
        for num in ['成本合计-还需', '料-还需',  '生产工-还需', '交付工-还需', '设计工-还需', '其他费-还需']:
            total_out.loc[(total_out[num] < 0) & (total_out['生产状态'].str.contains('子项目') == False), num] = 0

        #total_out['费-还需'] = total_out['其他费-还需'] + total_out['设计工-还需']
        #total_out['工-还需'] = total_out['生产工-还需'] + total_out['交付工-还需']
        total_out['成本合计-还需'] = total_out['生产工-还需'] + total_out['交付工-还需'] + total_out['其他费-还需'] + total_out['设计工-还需']+ total_out['料-还需']

        summary=total_out.copy()
        summary = summary.sort_values(by=['排个序'], ascending=[True]).reset_index(drop=True)

        print("3.7：拉取BOM金额..")
        screm_total.insert(INSERT, '\n3.7：拉取还需表..', '\n')
        window_total.update()
        summary['BOM'] = pd.merge(summary, bom_use, on='项目号整理', how='left')[["金额"]]
        summary['BOM'] = summary['BOM'].fillna(0)
        bom=bom[bom['项目号整理'].isin(summary['项目号整理'])].reset_index(drop=True)

        print("3.8：拉取23年公摊期初数..")
        screm_total.insert(INSERT, '\n3.8：拉取23年公摊期初数..', '\n')
        window_total.update()
        summary['23年制费公摊期初数'] = pd.merge(summary, average23, on='序列号', how='left')[["23年制费公摊期初数"]].fillna('')

        print("3.9：拉取验收数据..")
        screm_total.insert(INSERT, '\n3.9：拉取验收数据..', '\n')
        window_total.update()
        summary_2023 = summary[summary['序列号'].isin(scroll_2023_1['序列号'])].reset_index(drop=True)
        summary_2023[['验收分类', '验收年份（实际和最新预测）', '验收月份（实际和最新预测）', '产品线整理-一级']] = \
            pd.merge(summary_2023, scroll_2023_1, how='left', on='序列号')[
                ['验收分类', '验收年份（实际和最新预测）', '验收月份（实际和最新预测）', '产品线整理-一级']].fillna('')
        summary_else_1 = summary[~summary['排个序'].isin(summary_2023['排个序'])].reset_index(drop=True)

        summary_2024 = summary_else_1[summary_else_1['序列号'].isin(rece_2024_1['序列号'])].reset_index(drop=True)
        summary_2024[['验收分类', '验收年份（实际和最新预测）', '验收月份（实际和最新预测）', '产品线整理-一级']] = pd.merge(summary_2024, rece_2024_1, how='left', on='序列号')[['验收分类', '验收年份（实际和最新预测）', '验收月份（实际和最新预测）', '产品线整理-一级']].fillna('')
        summary_else_2 = summary_else_1[~summary_else_1['排个序'].isin(summary_2024['排个序'])].reset_index(drop=True)

        pause_list_east_1=pause_list_east[pause_list_east['一般工单号601/608']==''].reset_index(drop=True)
        summary_else_2['项目单号']=summary_else_2['核算项目号'].astype(str)+summary_else_2['一般工单号601/608'].astype(str)
        summary_pause1 = summary_else_2[(summary_else_2['一般工单号601/608']!='')&(summary_else_2['项目单号'].isin(pause_list_east['项目单号']))].reset_index(drop=True)
        summary_pause1['验收分类'] = '暂停'
        summary_pause1 = summary_pause1.fillna('')
        summary_else_3 = summary_else_2[~summary_else_2['排个序'].isin(summary_pause1['排个序'])].reset_index(drop=True)
        summary_pause1_1=summary_else_3[summary_else_3['核算项目号'].isin(pause_list_east_1['核算项目号'])].reset_index(drop=True)
        summary_pause1_1['验收分类'] = '暂停'
        summary_else_3_1 = summary_else_3[~summary_else_3['排个序'].isin(summary_pause1_1['排个序'])].reset_index(drop=True)

        pause_list_north_1 = pause_list_north[pause_list_north['一般工单号601/608'] == ''].reset_index(drop=True)
        summary_pause2 = summary_else_3_1[(summary_else_3_1['一般工单号601/608']!='')&(summary_else_3_1['项目单号'].isin(pause_list_north['项目单号']))].reset_index(drop=True)
        summary_pause2['验收分类'] = '暂停'
        summary_pause2 = summary_pause2.fillna('')
        summary_else_4 = summary_else_3_1[~summary_else_3_1['排个序'].isin(summary_pause2['排个序'])].reset_index(drop=True)
        summary_pause2_1 = summary_else_4[summary_else_4['核算项目号'].isin(pause_list_north_1['核算项目号'])].reset_index(drop=True)
        summary_pause2_1['验收分类'] = '暂停'
        summary_else_4_1 = summary_else_4[~summary_else_4['排个序'].isin(summary_pause2_1['排个序'])].reset_index(drop=True)

        summary_else_4_1['验收年份（实际和最新预测）'] = '2025'
        summary_else_4_1['验收分类'] = '2025年验收目标'
        summary_else_4_1['验收月份（实际和最新预测）'] = pd.to_datetime(summary_else_4_1['实际出货时间'],errors='coerce').dt.strftime('%m').astype(int)
        summary_else_4_1.loc[ summary_else_4['实际出货时间'] <= pd.Timestamp(2000, 1, 1), '验收月份（实际和最新预测）'] = '12'

        summary = pd.concat([summary_2023, summary_2024, summary_pause1,summary_pause1_1, summary_pause2,summary_pause2_1,summary_else_4_1]).reset_index(drop=True)
        summary = summary.sort_values(by=['排个序'], ascending=[True]).reset_index(drop=True)
        summary['验收月份（实际和最新预测）'] = summary['验收月份（实际和最新预测）'].replace('月', '', regex=True)

        summary['产品线一级'] = pd.merge(summary, line_list, left_on='产品线名称', right_on='产品线', how='left')['产品线整理-一级_y'].fillna('')
        summary['产品线整理-一级'] = summary['产品线整理-一级'].fillna('')
        summary.loc[summary['产品线整理-一级'] == '', '产品线整理-一级'] = summary['产品线一级']

        old_add=old_rece[list(old_rece.columns)[:19]]
        old_add[['验收分类', '验收年份（实际和最新预测）', '验收月份（实际和最新预测）', '产品线整理-一级']]=old_rece[['验收分类', '验收年份（实际和最新预测）', '验收月份（实际和最新预测）', '产品线整理-一级']].copy()
        new_add=bill_rece_group[['序列号','区域','生产状态','验收分类', '验收年份（实际和最新预测）', '验收月份（实际和最新预测）', '产品线整理-一级']].copy()
        for col in ['事业部收入', '集团收入']:
            new_add[col]=bill_rece_group["本币未税金额"].copy()

        new_add['产品线名称']=new_add['产品线整理-一级']
        add=pd.concat([old_add,new_add]).reset_index(drop=True)
        add[['实际出货时间', '实际验收时间', '系统验收时间']]=default_date


        for col in ['成本合计-概算', '成本合计-核算']:
            add[col]=add[ '集团收入']*0.45
        for col in ['生产工-概算', '交付工-概算', '设计工-概算', '制费-概算',  '生产工-核算', '交付工-核算',  '设计工-核算', '其他费-核算']:
            add[col]=add['集团收入'] * 0.45*0.05
        for col in ['料-概算', '料-核算', '工单料']:
            add[col]=add['集团收入'] * 0.45*0.8

        add[['成本合计-还需', '料-还需', '生产工-还需', '交付工-还需', '设计工-还需', '其他费-还需']]=0
        summary = pd.concat([summary, add]).reset_index(drop=True)

        #####################################################################################
        # 在途物料成本、在库物料成本
        summary_group = summary.groupby(['核算项目号']).agg({'序列号': "count"}).add_suffix('数量').reset_index()
        summary['项目数量汇总'] = pd.merge(summary, summary_group, on='核算项目号', how='left')['序列号数量']
        if len(in_transit_group) > 0:
            summary['在途物料成本'] = pd.merge(summary, in_transit_group,left_on='核算项目号', right_on='项目编号', how='left')['在途物料成本'].fillna(0)
            summary['在途物料成本'] = summary['在途物料成本'] /summary['项目数量汇总']
        if len(cybersecurity_group) > 0:
            summary['在库物料成本'] = pd.merge(summary, cybersecurity_group, left_on='核算项目号', right_on='库存管理特征' ,how='left')['在库物料成本'].fillna(0)
            summary['在库物料成本'] = summary['在库物料成本'] /summary['项目数量汇总']
        del summary['项目数量汇总']
        #####################################################################################
        summary= summary.fillna('')

        time_data_catch = time.time()
        print('第三阶段【数据拉取】执行时长:%d秒' % (time_data_catch - time_data_format))

        ######################################################################################################################################四
        print("四、汇总表字段加工..")
        screm_total.insert(INSERT, '\n四、汇总表字段加工..', '\n')
        window_total.update()
        summary_num = ['成本合计-概算', '料-概算', '生产工-概算', '交付工-概算', '设计工-概算', '制费-概算'
            , '成本合计-预算', '料-预算', '生产工-预算', '交付工-预算', '设计工-预算', '制费-预算', '采购PO'
            , '成本合计-核算','料(采购单价)', '料-核算', '工单料', '设变料', '生产工-核算', '交付工-核算',  '设计工-核算', '其他费-核算', '毛利-核算','毛利率-核算'
            , '成本合计-还需', '料-还需', '生产工-还需', '交付工-还需', '设计工-还需', '其他费-还需'
            , '年初合计','收入-年初', '料-年初','生产工-年初','交付工-年初','设计工-年初','费-年初'
            , '工时(生产交付)','生产工时','交付工时','设计工时',year1+'工时(生产交付)',year1+'生产工时',year1+'交付工时',year1+'设计工时','24年制费公摊金额','23年制费公摊期初数'
            ,'LOSS工成本','LOSS生产工','LOSS交付工','LOSS设计工','LOSS工时(生产交付)'
            ,'LOSS生产工时','LOSS交付工时','LOSS设计工时',year1+'LOSS工时(生产交付)',year1+'LOSS生产工时',year1+'LOSS交付工时',year1+'LOSS设计工时']
        summary[summary_num] = summary[summary_num].fillna('')

        for col in summary_num:
            if '概算' in col  or '还需' in col or '预算' in col:
                summary.loc[summary['OA状态'].str.contains('不'),col]=0

        print("4.1：加工出货&验收年月..")
        screm_total.insert(INSERT, '\n4.1：加工出货&验收年月..', '\n')
        window_total.update()

        print("4.2：滚动预测..")  ##核算+还需
        screm_total.insert(INSERT, '\n4.2：滚动预测..', '\n')
        window_total.update()
        summary['成本合计-滚动'] = ''
        summary['料-滚动'] = ''
        #summary['工-滚动'] = ''
        summary['生产工-滚动'] = ''
        summary['交付工-滚动'] = ''
        #summary['费-滚动'] = ''
        summary['设计工-滚动'] = ''
        summary['其他费-滚动'] = ''
        '''
        summary.loc[(summary['成本合计-核算']!='')&(summary['成本合计-还需']!=''),'成本合计-滚动']=summary['成本合计-核算'] + summary['成本合计-还需']
        summary.loc[(summary['料-核算']!='')&(summary['料-还需']!=''),'料-滚动'] = summary['料-核算'] + summary['料-还需']
        summary.loc[(summary['工-核算']!='')&(summary['工-还需']!=''),'工-滚动'] = summary['工-核算'] + summary['工-还需']
        summary.loc[(summary['生产工-核算']!='')&(summary['生产工-还需']!=''),'生产工-滚动'] = summary['生产工-核算'] + summary['生产工-还需']
        summary.loc[(summary['交付工-核算']!='')&(summary['交付工-还需']!=''),'交付工-滚动'] = summary['交付工-核算'] + summary['交付工-还需']
        summary.loc[(summary['费-核算']!='')&(summary['费-还需']!=''),'费-滚动'] = summary['费-核算'] + summary['费-还需']
        summary.loc[(summary['设计工-核算']!='')&(summary['设计工-还需']!=''),'设计工-滚动'] = summary['设计工-核算'] + summary['设计工-还需']
        summary.loc[(summary['其他费-核算']!='')&(summary['其他费-还需']!=''),'其他费-滚动'] = summary['其他费-核算'] + summary['其他费-还需']
        '''
        '''
        for i in range(len(summary)):
            if summary.loc[i, "成本合计-核算"] != '' and summary.loc[i, "成本合计-还需"] != '':
                summary.loc[i, "成本合计-滚动"] = summary.loc[i, '成本合计-核算'] + summary.loc[i, '成本合计-还需']
            if summary.loc[i, "料-核算"] != '' and summary.loc[i, "料-还需"] != '':
                summary.loc[i, "料-滚动"] = summary.loc[i, '料-核算'] + summary.loc[i, '料-还需']
            if summary.loc[i, "工-核算"] != '' and summary.loc[i, "工-还需"] != '':
                summary.loc[i, "工-滚动"] = summary.loc[i, '工-核算'] + summary.loc[i, '工-还需']
            if summary.loc[i, "生产工-核算"] != '' and summary.loc[i, "生产工-还需"] != '':
                summary.loc[i, "生产工-滚动"] = summary.loc[i, '生产工-核算'] + summary.loc[i, '生产工-还需']
            if summary.loc[i, "交付工-核算"] != '' and summary.loc[i, "交付工-还需"] != '':
                summary.loc[i, "交付工-滚动"] = summary.loc[i, '交付工-核算'] + summary.loc[i, '交付工-还需']
            if summary.loc[i, "费-核算"] != '' and summary.loc[i, "费-还需"] != '':
                summary.loc[i, "费-滚动"] = summary.loc[i, '费-核算'] + summary.loc[i, '费-还需']
            if summary.loc[i, "设计工-核算"] != '' and summary.loc[i, "设计工-还需"] != '':
                summary.loc[i, "设计工-滚动"] = summary.loc[i, '设计工-核算'] + summary.loc[i, '设计工-还需']
            if summary.loc[i, "其他费-核算"] != '' and summary.loc[i, "其他费-还需"] != '':
                summary.loc[i, "其他费-滚动"] = summary.loc[i, '其他费-核算'] + summary.loc[i, '其他费-还需']
        '''

        print("4.3：毛利&毛利率..")
        screm_total.insert(INSERT, '\n4.3：毛利&毛利率..', '\n')
        window_total.update()


        print("4.3：整理格式..")
        screm_total.insert(INSERT, '\n4.3：整理格式..', '\n')
        window_total.update()
        summary['事业部收入'] = summary['事业部收入'].fillna('')
        summary['汇率收入差异\n(海外项目验收汇率和订单汇率差异)'] = ''

        summary['原材料-存货'] = ''
        summary['大项目整理'] = ''

        summary['集团毛利-概算'] = ''
        summary['集团毛利率-概算'] = ''

        summary['考核毛利-概算'] = ''
        summary['考核毛利率-概算'] = ''

        summary['考核毛利-核算'] = ''
        summary['考核毛利率-核算'] = ''
        summary['毛利-核算'] = ''
        summary['毛利率-核算'] = ''
        summary['考核毛利-滚动'] = ''
        summary['考核毛利率-滚动'] = ''

        summary['生产工价差'] = ''
        summary['交付工价差'] = ''
        summary['设计工价差'] = ''
        summary['价差合计'] = ''


        #summary['是否在手订单'] = ''
        list_col=[
            '序列号', '区域', '行业中心', '客户简称', '大项目名称', '大项目号', '产品线名称', '核算项目号', '设备名称', '项目数量', '已出货数量', '在产数量', '生产状态',
             '设备类型'
            , '项目经理', '项目阶段'
            , '事业部收入', '集团收入', '软件收入', '硬件收入','汇率收入差异\n(海外项目验收汇率和订单汇率差异)'
            , '成本合计-概算', '料-概算', '生产工-概算', '交付工-概算', '设计工-概算', '制费-概算', '集团毛利-概算', '集团毛利率-概算','考核毛利-概算','考核毛利率-概算'
            , '成本合计-预算', '料-预算', '生产工-预算', '交付工-预算', '设计工-预算', '制费-预算'
            , 'BOM','采购PO','原材料-存货'
            , '成本合计-核算', '料(采购单价)','料-核算', '工单料', '设变料',  '生产工-核算', '交付工-核算',  '设计工-核算', '其他费-核算', '毛利-核算','毛利率-核算','考核毛利-核算','考核毛利率-核算'
            , '成本合计-还需', '料-还需',  '生产工-还需', '交付工-还需',  '设计工-还需', '其他费-还需'
            , '成本合计-滚动', '料-滚动', '生产工-滚动', '交付工-滚动', '设计工-滚动', '其他费-滚动', '毛利-滚动', '毛利率-滚动','考核毛利-滚动','考核毛利率-滚动'
            , '项目号整理', '子项目状态', '成品料号', '一般工单号601/608', '返工工单号603'
            , '大项目整理'
            , '实际出货时间', '实际验收时间', '系统验收时间', '是否YY','OA状态'
            , '年初合计','收入-年初', '料-年初','生产工-年初','交付工-年初','设计工-年初','费-年初'
            , '工时(生产交付)','生产工时','交付工时','设计工时'
            ,year1+'工时(生产交付)', year1+'生产工时', year1+'交付工时',year1+'设计工时', '24年制费公摊金额','23年制费公摊期初数'
            ,'价差合计','生产工价差','交付工价差','设计工价差','项目财经','验收分类', '验收年份（实际和最新预测）', '验收月份（实际和最新预测）', '产品线整理-一级','LOSS工成本','LOSS生产工','LOSS交付工','LOSS设计工'
            ,'LOSS工时(生产交付)','LOSS生产工时','LOSS交付工时','LOSS设计工时',year1+'LOSS工时(生产交付)',year1+'LOSS生产工时',year1+'LOSS交付工时',year1+'LOSS设计工时','来源台账判断']
        
        if '在途物料成本' in summary.columns:
            list_col.append('在途物料成本')
        if '在库物料成本' in summary.columns:
            list_col.append('在库物料成本')
        summary = summary.reindex(columns=list_col)
        summary_date = ['实际出货时间', '实际验收时间', '系统验收时间']
        for dat in summary_date:
            summary[dat]=summary[dat].dt.strftime("%Y-%m-%d").astype(str)
            summary[dat] = ['' if i == '1990-01-01' else i for i in summary[dat]]
        time_data_refresh = time.time()
        print('第四阶段【字段加工】执行时长:%d秒' % (time_data_refresh - time_data_catch))
        screm_total.insert(INSERT, '\n第四阶段【字段加工】执行时长:%d秒' % (time_data_refresh - time_data_catch), '\n')
        window_total.update()

        ######################################################################################################################################五
        print('五、表格输出...')
        screm_total.insert(INSERT, '\n五、表格输出...', '\n')
        window_total.update()

        def writer_contents(sheet, array, start_row, start_col, format=None, percent_format=None, percentlist=[]):
            # start_col = 0
            for col in array:
                if percentlist and (start_col in percentlist):
                    sheet.write_column(start_row, start_col, col, percent_format)
                else:
                    sheet.write_column(start_row, start_col, col, format)
                start_col += 1

        def write_color(book, sheet, data, fmt, col_num='I'):
            start = 3
            format_red = book.add_format({'font_name': 'Arial',
                                          'font_size': 10,
                                          'bg_color': '#F86470'})
            format_red.set_align('center')
            format_red.set_align('vcenter')

            for item in data:
                if '找不到' in str(item) in str(item):
                    sheet.write(col_num + str(start), item, format_red)
                else:
                    sheet.write(col_num + str(start), item, fmt)
                start += 1

        # 写入表格
        print('5.1：正在设置表格格式...')
        screm_total.insert(INSERT, '\n5.1：正在设置表格格式...', '\n')
        window_total.update()
        now_time = time.strftime("%Y-%m-%d-%H", time.localtime(time.time()))
        book_name = '4、概预核决汇总-数据\概预核决汇总表' + now_time
        workbook = xlsxwriter.Workbook(book_name + '.xlsx', {'nan_inf_to_errors': True})
        worksheet0 = workbook.add_worksheet('汇总表')
        worksheet1 = workbook.add_worksheet('01-底表基础数据')  ##base
        worksheet2 = workbook.add_worksheet('02-概预算')  # budget_estimate
        worksheet3 = workbook.add_worksheet('03-核算表')  ##calcu

        worksheet4 = workbook.add_worksheet('04-还需')  ##need
        worksheet5 = workbook.add_worksheet('05-台账增值改造')  ##need


        ######主色调
        title_format = workbook.add_format({'font_name': 'Regular',
                                            'font_size': 10,
                                            'font_color': 'white',
                                            'bg_color': '#1F4E78',
                                            'bold': True,
                                            'align': 'center',
                                            'valign': 'vcenter',
                                            'border': 1,
                                            'border_color': 'white'
                                            })
        title_format.set_align('vcenter')
        ######项目&数量  |收入
        title_format1 = workbook.add_format({'font_name': 'Regular',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#9D4B73',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format1.set_align('vcenter')
        ######出货&验收
        title_format2 = workbook.add_format({'font_name': 'Regular',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#3EA9C0',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format2.set_align('vcenter')
        ######概算
        title_format3 = workbook.add_format({'font_name': 'Regular',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#248E87',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format3.set_align('vcenter')
        ######预算
        title_format4 = workbook.add_format({'font_name': 'Regular',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#789966',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format4.set_align('vcenter')
        ######核算
        title_format5 = workbook.add_format({'font_name': 'Regular',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#B9A035',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format5.set_align('vcenter')
        ######还需
        title_format6 = workbook.add_format({'font_name': 'Regular',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#429EB0',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format6.set_align('vcenter')
        ######滚动
        title_format7 = workbook.add_format({'font_name': 'Regular',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#815E8C',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format7.set_align('vcenter')
        ######财务
        title_format8 = workbook.add_format({'font_name': 'Regular',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#7896A8',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format8.set_align('vcenter')
        ######存货
        title_format9 = workbook.add_format({'font_name': 'Regular',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#9A7F76',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format9.set_align('vcenter')
        ######项目进度
        title_format10 = workbook.add_format({'font_name': 'Regular',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#455391',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format10.set_align('vcenter')
        ######年初
        title_format11 = workbook.add_format({'font_name': 'Regular',
                                              'font_size': 10,
                                              'font_color': 'white',
                                              'bg_color': '#3B757D',
                                              'bold': True,
                                              'align': 'center',
                                              'valign': 'vcenter',
                                              'border': 1,
                                              'border_color': 'white'
                                              })
        title_format11.set_align('vcenter')
        ######工时
        title_format12 = workbook.add_format({'font_name': 'Regular',
                                              'font_size': 10,
                                              'font_color': 'white',
                                              'bg_color': '#B99C31',
                                              'bold': True,
                                              'align': 'center',
                                              'valign': 'vcenter',
                                              'border': 1,
                                              'border_color': 'white'
                                              })
        title_format12.set_align('vcenter')
        col_format = workbook.add_format({'font_name': 'Regular',
                                          'font_size': 8,
                                          'font_color': 'white',
                                          'bg_color': '#595959',
                                          'text_wrap': True,
                                          'border': 1,
                                          'border_color': 'white',
                                          'align': 'center',
                                          'valign': 'vcenter'
                                          })

        data_format = workbook.add_format({'font_name': 'Regular',
                                           'font_size': 9,
                                           'align': 'left',
                                           'valign': 'vcenter'
                                           })
        data_format1 = workbook.add_format({'font_name': 'Regular',
                                            'font_size': 9,
                                            'align': 'center',
                                            'valign': 'vcenter'
                                            })
        data_format2 = workbook.add_format({'font_name': 'Regular',
                                            'font_size': 9,
                                            'align': 'center',
                                            'valign': 'vcenter'
                                            })
        data_format2.set_num_format('0.00')
        data_format3 = workbook.add_format({'font_name': 'Regular',
                                            'font_size': 9,
                                            'align': 'center',
                                            'valign': 'vcenter'
                                            })
        data_format3.set_num_format('0.00%')
        num_percent_data_format = workbook.add_format({'font_name': 'Regular',
                                                       'font_size': 9,
                                                       'align': 'center',
                                                       'valign': 'vcenter',
                                                       'num_format': '0.00%'})
        statis_format2 = workbook.add_format({'font_name': 'Regular',  # 系列总计
                                              'font_size': 9,
                                              'align': 'center',
                                              'valign': 'vcenter',
                                              'bg_color': '#92CDDC'
                                              })
        data_format_percent = workbook.add_format({'font_name': 'Regular',
                                                   'font_size': 10,
                                                   'align': 'center',
                                                   'valign': 'vcenter'
                                                   })
        data_format_percent.set_num_format('0.00%')

        print('5.2：正在写入EXCEL表格...')
        screm_total.insert(INSERT, '\n5.2：正在写入EXCEL表格...', '\n')
        window_total.update()
        worksheet0.write_row("A2", summary.columns, title_format)
        writer_contents(sheet=worksheet0, array=summary.T.values, start_row=2, start_col=0)
        # end = len(report_work1) + 1
        worksheet0.merge_range('A1:P1', '项目基础信息', title_format)
        worksheet0.merge_range('Q1:U1', '收入数据', title_format1)
        worksheet0.merge_range('V1:AE1', '概算数据', title_format3)
        worksheet0.merge_range('AF1:AK1', '预算数据', title_format4)
        worksheet0.merge_range('AL1:BA1', '核算数据', title_format5)
        worksheet0.merge_range('BB1:BG1', '还需数据', title_format6)
        worksheet0.merge_range('BH1:BQ1', '滚动预测数据', title_format7)

        worksheet0.merge_range('BR1:BW1', '项目辅助信息', title_format)
        worksheet0.merge_range('BX1:CB1', '项目进度信息', title_format10)
        worksheet0.merge_range("CC1:CI1", '年初预算数据', title_format8)
        worksheet0.merge_range("CJ1:CM1", '工时汇总', title_format12)
        worksheet0.merge_range("CN1:CQ1", '24年工时汇总', title_format11)
        worksheet0.merge_range("CR1:CS1", '制费公摊', title_format11)

        worksheet0.merge_range("CT1:CW1", '核算工价差异（标准和实际工价）', title_format9)
        worksheet0.merge_range("CY1:DB1", '验收信息', title_format)
        worksheet0.merge_range("DC1:DF1", 'LOSS工时成本汇总', title_format)
        worksheet0.merge_range("DG1:DJ1", 'LOSS工时汇总', title_format)
        worksheet0.merge_range("DK1:DN1", year1+'LOSS工时汇总', title_format)
        worksheet0.write_row("J2:L2", ['项目数量', '已出货数量', '在产数量'], title_format6)

        worksheet0.write_row("Q2:U2", ['事业部收入', '集团收入', '软件收入', '硬件收入','汇率收入差异\n(海外项目验收汇率和订单汇率差异)'], title_format1)

        worksheet0.write_row("V2:AE2", ['成本合计', '料', '生产工', '交付工', '设计工', '其他费', '集团毛利-概算', '集团毛利率-概算','考核毛利-概算','考核毛利率-概算'], title_format3)
        worksheet0.write_row("AF2:AK2", ['成本合计', '料', '生产工', '交付工', '设计工', '其他费'], title_format4)
        worksheet0.write_row("AL2:BA2",['BOM','采购PO', '原材料-存货', '成本合计','料(采购单价)', '料', '工单料', '设变料',  '生产工', '交付工', '设计工', '其他费','集团毛利','集团毛利率','考核毛利','考核毛利率'], title_format5)
        worksheet0.write_row("BB2:BG2", ['成本合计', '料', '生产工', '交付工', '设计工', '其他费'], title_format6)
        worksheet0.write_row("BH2:BQ2", ['成本合计', '料',  '生产工', '交付工', '设计工', '其他费','集团毛利','集团毛利率','考核毛利','考核毛利率'],title_format7)

        worksheet0.write_row("BX2:CB2",['实际出货时间', '实际验收时间', '系统验收时间', '是否YY','OA状态'],title_format10)
        worksheet0.write_row("CC2:CI2", ['收入合计','成本合计','料','生产工','交付工','设计工','其他费'], title_format8)

        worksheet0.write_row("CJ2:CM2", ['工时(生产交付)', '生产工时', '交付工时','设计工时'], title_format12)
        worksheet0.write_row("CN2:CQ2", ['工时(生产交付)', '生产工时', '交付工时', '设计工时'], title_format11)
        worksheet0.write_row("CR2:CS2", ["24年制费公摊金额","23年制费公摊期初数"],title_format11)
        worksheet0.write_row("CT2:CW2", ['价差合计', '生产工价差', '交付工价差', '设计工价差'], title_format9)

        worksheet0.write("CX1", '', title_format)
        worksheet0.write("DO1", '', title_format)
        if '在途物料成本' in summary.columns:
            worksheet0.write("DP1", '', title_format)
            worksheet0.write("DQ1", '', title_format)
        worksheet0.write("CX2", '项目财经', title_format)
        worksheet0.set_row(0, 25)
        worksheet0.set_row(1, 22)
        worksheet0.set_column('AC:AC', 8, data_format_percent)
        worksheet0.set_column('AE:AE', 8, data_format_percent)
        worksheet0.set_column('AY:AY', 8, data_format_percent)
        worksheet0.set_column('BA:BA', 8, data_format_percent)
        worksheet0.set_column('BO:BO', 8, data_format_percent)
        worksheet0.set_column('BQ:BQ', 8, data_format_percent)

        worksheet0.set_column('A:P', 9, data_format)
        worksheet0.set_column('Q:AB', 8, data_format2)
        worksheet0.set_column('AD:AD', 8, data_format2)
        worksheet0.set_column('AF:AX', 8, data_format2)
        worksheet0.set_column('AZ:AZ', 8, data_format2)
        worksheet0.set_column('BB:BN', 8, data_format2)
        worksheet0.set_column('BP:BP', 8, data_format2)
        worksheet0.set_column('BR:CB', 8, data_format)
        worksheet0.set_column('CC:CW', 9, data_format2)
        worksheet0.set_column('CX:CX', 8, data_format)
        worksheet0.set_column('CY:DB', 10, data_format)
        worksheet0.set_column('DC:DQ', 10, data_format2)

        ##########写入公式
        for i in range(len(summary)):
            n=i+3
            worksheet0.write_formula('BH'+str(n), '=IF(AO%d<>"",IF(BB%d<>"",AO%d+BB%d,""),"")'%(n,n,n,n)) #'=B2+C2+D2'
            worksheet0.write_formula('BI' + str(n), '=IF(BC%d<>"",IF(AQ%d<>"",AQ%d+BC%d,""),"")'%(n,n,n,n))
            worksheet0.write_formula('BJ' + str(n), '=IF(AT%d<>"",IF(BD%d<>"",AT%d+BD%d,""),"")'%(n,n,n,n))
            worksheet0.write_formula('BK' + str(n), '=IF(AU%d<>"",IF(BE%d<>"",AU%d+BE%d,""),"")'%(n,n,n,n))
            worksheet0.write_formula('BL' + str(n), '=IF(AV%d<>"",IF(BF%d<>"",AV%d+BF%d,""),"")'%(n,n,n,n))
            worksheet0.write_formula('BM' + str(n), '=IF(AW%d<>"",IF(BG%d<>"",AW%d+BG%d,""),"")'%(n,n,n,n))

            worksheet0.write_formula('BN' + str(n), '=IF(R%d<>"",IF(BH%d<>"",R%d-BH%d,""),"")'%(n,n,n,n))
            worksheet0.write_formula('BO' + str(n), '=IF(R%d<>"",IF(R%d<>0,IF(BN%d<>"",BN%d/R%d,""),""),"")' % (n, n, n, n,n))
            worksheet0.write_formula('BP' + str(n), '=IF(Q%d<>"",IF(BH%d<>"",Q%d-BH%d,""),"")' % (n, n, n, n))
            worksheet0.write_formula('BQ' + str(n), '=IF(Q%d<>"",IF(Q%d<>0,IF(BP%d<>"",BP%d/Q%d,""),""),"")' % (n, n, n, n, n))

            worksheet0.write_formula('AB' + str(n), '=IF(R%d<>"",IF(V%d<>"",R%d-V%d,""),"")' % (n, n, n, n))
            worksheet0.write_formula('AC' + str(n), '=IF(R%d<>"",IF(R%d<>0,IF(AB%d<>"",AB%d/R%d,""),""),"")' % (n, n, n, n, n))
            worksheet0.write_formula('AD' + str(n), '=IF(Q%d<>"",IF(V%d<>"",Q%d-V%d,""),"")' % (n, n, n, n))
            worksheet0.write_formula('AE' + str(n), '=IF(Q%d<>"",IF(Q%d<>0,IF(AD%d<>"",AD%d/Q%d,""),""),"")' % (n, n, n, n, n))

            worksheet0.write_formula('AX' + str(n), '=IF(R%d<>"",IF(AO%d<>"",R%d-AO%d,""),"")' % (n, n, n, n))
            worksheet0.write_formula('AY' + str(n), '=IF(R%d<>"",IF(R%d<>0,IF(AX%d<>"",AX%d/R%d,""),""),"")' % (n, n, n, n, n))
            worksheet0.write_formula('AZ' + str(n), '=IF(Q%d<>"",IF(AO%d<>"",Q%d-AO%d,""),"")' % (n, n, n, n))
            worksheet0.write_formula('BA' + str(n), '=IF(Q%d<>"",IF(Q%d<>0,IF(AZ%d<>"",AZ%d/Q%d,""),""),"")' % (n, n, n, n, n))

            worksheet0.write_formula('CU' + str(n), '=IFERROR(AT%d-CK%d*50/10000,"")' % (n, n))
            worksheet0.write_formula('CV' + str(n), '=IFERROR(AU%d-CL%d*50/10000,"")' % (n, n))
            worksheet0.write_formula('CW' + str(n), '=IFERROR(AV%d-CM%d*100/10000,"")' % (n, n))
            worksheet0.write_formula('CT' + str(n), '=IFERROR(CU%d+CV%d+CW%d,"")' % (n, n, n))

        worksheet1.write_row("A1", base.columns, title_format)
        writer_contents(sheet=worksheet1, array=base.T.values, start_row=1, start_col=0)
        worksheet1.set_row(0, 25)

        worksheet2.write_row("A1", budget_estimate.columns, title_format)
        writer_contents(sheet=worksheet2, array=budget_estimate.T.values, start_row=1, start_col=0)
        worksheet2.set_row(0, 25)

        worksheet3.write_row("A1", calcu.columns, title_format)
        writer_contents(sheet=worksheet3, array=calcu.T.values, start_row=1, start_col=0)
        worksheet3.set_row(0, 25)
        '''
        worksheet4.write_row("A1", finance.columns, title_format)
        writer_contents(sheet=worksheet4, array=finance.T.values, start_row=1, start_col=0)
        worksheet4.set_row(0, 25)
      
        worksheet5.write_row("A1", advance.columns, title_format)
        writer_contents(sheet=worksheet5, array=advance.T.values, start_row=1, start_col=0)
        worksheet5.set_row(0, 25)
        '''
        need=need.fillna('')
        worksheet4.write_row("A1", need.columns, title_format)
        writer_contents(sheet=worksheet4, array=need.T.values, start_row=1, start_col=0)
        worksheet4.set_row(0, 25)

        bill_rece = bill_rece.fillna('')
        worksheet5.write_row("A1", bill_rece.columns, title_format)
        writer_contents(sheet=worksheet5, array=bill_rece.T.values, start_row=1, start_col=0)
        worksheet5.set_row(0, 25)

        print('明细表已写入。。。')
        workbook.close()
        time_excel = time.time()
        print('第五阶段【输出表格】执行时长:%d秒' % (time_excel - time_data_refresh))

    except Exception as f:
        # print('异常信息为:', e)  # 异常信息为: division by zero
        print('——#@*&程序报错，异常信息为:' + traceback.format_exc())
        screm_total.insert(INSERT, '\n——#@*&程序报错，异常信息为:' + traceback.format_exc(), '\n')
        window_total.update()

    time_end = time.time()
    print('执行完成！！！！！')
    print('执行总时长:%d秒' % (time_end - time_start))
    screm_total.insert(INSERT, '\n明细表已写入。。。', '\n')
    screm_total.insert(INSERT, '\n执行总时长:%d秒' % (time_end - time_start), '\n')
    window_total.update()
