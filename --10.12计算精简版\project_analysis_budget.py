#coding:utf-8
import zipfile
####################################################抬头
import threading
import pandas as pd
import numpy as np
import time
import os
import xlsxwriter
from pathlib import Path
import warnings
import tkinter as tk
import tkinter.messagebox #弹出框
from tkinter import *
from PIL import Image, ImageTk, ImageSequence
import xlsxwriter
import traceback
import datetime
from tkinter import scrolledtext
from datetime import datetime
from openpyxl.utils import get_column_letter
warnings.filterwarnings('ignore')

time_start = time.time()
print("一、数据读取..")
'''
screm_total.insert(INSERT, '\n一、数据读取..', '\n')
window_total.update()
'''
###############################################################################################################一、生成项目预算表
Path_budget = r'单项目分析-数据\01.概算表'

'''#################一、料'''
filename_budget = os.listdir(Path_budget)
for i in range(len(filename_budget)):
    if str(filename_budget[i]).count('~$') == 0:
        # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
        budget_mater = pd.read_excel(Path_budget + '/' + str(filename_budget[i]), header=4, sheet_name='概算总表').loc[
            0, '价格(万)']
        budget_mater=budget_mater*10000

'''#################二、工'''
work = []
for i in range(len(filename_budget)):
    writer = pd.ExcelFile(Path_budget + '\\' + str(filename_budget[i]))
    sheet_len = len(writer.sheet_names)
    for k in range(0, sheet_len):
        if str(writer.sheet_names[k]).count('~$') == 0 and '工' in str(writer.sheet_names[k]) and '费' not in str(
                writer.sheet_names[k]):
            # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
            print('正在读取'+str(writer.sheet_names[k]))
            df_work = pd.read_excel(Path_budget + '/' + str(filename_budget[i]), sheet_name=writer.sheet_names[k],
                                    header=9, usecols='A:M')
            work.append(df_work)
budget_work = pd.concat(work).reset_index(drop=True)[['阶段', '岗位', '工时成本', '阶段.1']]

####工格式
budget_work[['阶段', '岗位', '阶段.1']] = budget_work[['阶段', '岗位', '阶段.1']].fillna('')
budget_work.loc[budget_work['阶段.1'].str.contains('m2|M2'), '阶段.1'] = '产品设计阶段'
budget_work.loc[budget_work['阶段.1'].str.contains('m3|M3'), '阶段.1'] = '生成准备阶段'
budget_work.loc[budget_work['阶段.1'].str.contains('m4|M4'), '阶段.1'] = '调试'
budget_work.loc[budget_work['阶段.1'].str.contains('m5|M5'), '阶段.1'] = '完成预验收'
budget_work.loc[budget_work['阶段.1'].str.contains('m6|M6'), '阶段.1'] = '完成验收'
budget_work['工时成本'] = budget_work['工时成本'].fillna(0)
budget_work = budget_work[budget_work['阶段.1'] != ''].reset_index(drop=True)

budget_work_group = budget_work.groupby(['岗位', '阶段.1'])[['工时成本']].sum().add_suffix('-之和').reset_index()
budget_work_group1 = budget_work_group[budget_work_group['阶段.1'].str.contains('设计')].reset_index(drop=True)
budget_work_group2 = budget_work_group[budget_work_group['阶段.1'].str.contains('准备')].reset_index(drop=True)
budget_work_group3 = budget_work_group[budget_work_group['阶段.1'].str.contains('装配')].reset_index(drop=True)
budget_work_group4 = budget_work_group[budget_work_group['阶段.1'].str.contains('调试')].reset_index(drop=True)
budget_work_group5 = budget_work_group[budget_work_group['阶段.1'].str.contains('完成预验收')].reset_index(drop=True)
budget_work_group6 = budget_work_group[budget_work_group['阶段.1'].str.contains('完成验收')].reset_index(drop=True)
print('工——阶段判断：')
for m in list(budget_work_group['阶段.1'].drop_duplicates()):
    if '-' in str(m):
        print('#######warning##########')
        print('概算工存在不识别阶段：' + str(m))
        print('#######warning##########')

###建立岗位模板
post_list = ["项目总负责人", "售后负责人", "项目经理", "技术负责人", "产品经理", "机械工程师", "电气工程师", "PLC控制工程师", "上位机工程师", "机器人工程师", "视觉工程师",
             "激光工艺工程师", "激光软件工程师", "机械装配技术员", "电气装配技术员", "调试技术员"]
print('工——岗位判断：')
for m in list(budget_work_group['岗位'].drop_duplicates()):
    if str(m) not in post_list:
        print('#######warning##########' )
        print('概算工存在不识别岗位：' + str(m))
        print('#######warning##########')

budget_work_use = pd.DataFrame(post_list, columns=['类别'])
budget_work_use['工时1'] = 10
budget_work_use['人数1'] = 0
budget_work_use['合计1'] = pd.merge(budget_work_use, budget_work_group1, left_on='类别', right_on='岗位', how='left')[
    '工时成本-之和']

budget_work_use['工时2'] = 10
budget_work_use['人数2'] = 0
budget_work_use['合计2'] = pd.merge(budget_work_use, budget_work_group2, left_on='类别', right_on='岗位', how='left')[
    '工时成本-之和']

budget_work_use['工时3'] = 10
budget_work_use['人数3'] = 0
budget_work_use['合计3'] = pd.merge(budget_work_use, budget_work_group3, left_on='类别', right_on='岗位', how='left')[
    '工时成本-之和']

budget_work_use['工时4'] = 10
budget_work_use['人数4'] = 0
budget_work_use['合计4'] = pd.merge(budget_work_use, budget_work_group4, left_on='类别', right_on='岗位', how='left')[
    '工时成本-之和']

budget_work_use['工时5'] = 10
budget_work_use['人数5'] = 0
budget_work_use['合计5'] = pd.merge(budget_work_use, budget_work_group5, left_on='类别', right_on='岗位', how='left')[
    '工时成本-之和']

budget_work_use['工时6'] = 10
budget_work_use['人数6'] = 0
budget_work_use['合计6'] = pd.merge(budget_work_use, budget_work_group6, left_on='类别', right_on='岗位', how='left')[
    '工时成本-之和']
budget_work_use['工时总计'] = 0
for num in range(6):
    budget_work_use['合计' + str(num + 1)] = budget_work_use['合计' + str(num + 1)].fillna(0)
    budget_work_use['工时总计'] = budget_work_use['工时总计'] + budget_work_use['合计' + str(num + 1)]
    budget_work_use['人数' + str(num + 1)] = budget_work_use['合计' + str(num + 1)] / budget_work_use['工时' + str(num + 1)]

budget_work_use['人数1'] = budget_work_use['人数1'] / 26
budget_work_use['人数2'] = budget_work_use['人数2'] / 50
budget_work_use['人数3'] = budget_work_use['人数3'] / 10
budget_work_use['人数4'] = budget_work_use['人数4'] / 20
budget_work_use['人数5'] = budget_work_use['人数5'] / 70
budget_work_use['人数6'] = budget_work_use['人数6'] / 30

'''#################三、费'''
cost = []
for i in range(len(filename_budget)):
    writer = pd.ExcelFile(Path_budget + '\\' + str(filename_budget[i]))
    sheet_len = len(writer.sheet_names)
    for k in range(0, sheet_len):
        if str(writer.sheet_names[k]).count('~$') == 0 and str(writer.sheet_names[k]) in ['费', '海外费', '海外费-物流线']:
            # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
            print('正在读取'+str(writer.sheet_names[k]))
            df_cost = pd.read_excel(Path_budget + '/' + str(filename_budget[i]), sheet_name=writer.sheet_names[k],
                                    header=7, usecols='B:L')
            df_cost = df_cost.rename(columns={df_cost.columns[2]: '金额'})
            cost.append(df_cost)
budget_cost = pd.concat(cost).reset_index(drop=True)[['金额', '阶段', '科目']]

budget_cost[['阶段', '科目']] = budget_cost[['阶段', '科目']].fillna('')
budget_cost['金额'] = budget_cost['金额'].fillna(0)
budget_cost = budget_cost[budget_cost['科目'] != ''].reset_index(drop=True)

budget_cost.loc[budget_cost['科目'].str.contains('住宿'), '科目'] = '住宿费'
budget_cost.loc[budget_cost['科目'].str.contains('交通'), '科目'] = '差旅费'

budget_cost['阶段'] = budget_cost['阶段'].replace('m', 'M', regex=True).astype(str)
budget_cost = budget_cost[budget_cost['阶段'].str.contains('M')].reset_index(drop=True)
print('费--阶段判断：')
for m in list(budget_cost['阶段'].drop_duplicates()):
    if '-' in str(m):
        print('#######warning##########')
        print('概算费存在不识别阶段：' + str(m))
        print('#######warning##########')
print('费--科目判断：')
project_list = ["海外小时工", "出差补贴", "住宿费", "差旅费", "物流费用", "认证费", "其他费用", "签证费", "CCD等供应商海外费"]
for m in list(budget_cost['科目'].drop_duplicates()):
    if str(m) not in project_list:
        print('#######warning##########')
        print('概算费存在不识别科目：' + str(m))
        print('#######warning##########')

budget_cost_group = budget_cost.groupby(['科目', '阶段'])[['金额']].sum().add_suffix('-之和').reset_index()
budget_cost_group1 = budget_cost_group[budget_cost_group['阶段'].str.contains('M1')].reset_index(drop=True)
budget_cost_group2 = budget_cost_group[budget_cost_group['阶段'].str.contains('M2')].reset_index(drop=True)
budget_cost_group3 = budget_cost_group[budget_cost_group['阶段'].str.contains('M3')].reset_index(drop=True)
budget_cost_group4 = budget_cost_group[budget_cost_group['阶段'].str.contains('M4')].reset_index(drop=True)
budget_cost_group5 = budget_cost_group[budget_cost_group['阶段'].str.contains('M5')].reset_index(drop=True)
budget_cost_group6 = budget_cost_group[budget_cost_group['阶段'].str.contains('M6')].reset_index(drop=True)

budget_cost_use = pd.DataFrame(project_list, columns=['科目'])

budget_cost_use['合计1'] = pd.merge(budget_cost_use, budget_cost_group1, left_on='科目', right_on='科目', how='left')['金额-之和']
budget_cost_use['合计2'] = pd.merge(budget_cost_use, budget_cost_group2, left_on='科目', right_on='科目', how='left')['金额-之和']
budget_cost_use['合计3'] = pd.merge(budget_cost_use, budget_cost_group3, left_on='科目', right_on='科目', how='left')['金额-之和']
budget_cost_use['合计4'] = pd.merge(budget_cost_use, budget_cost_group4, left_on='科目', right_on='科目', how='left')['金额-之和']
budget_cost_use['合计5'] = pd.merge(budget_cost_use, budget_cost_group5, left_on='科目', right_on='科目', how='left')['金额-之和']
budget_cost_use['合计6'] = pd.merge(budget_cost_use, budget_cost_group6, left_on='科目', right_on='科目', how='left')['金额-之和']

budget_cost_use['总计'] = 0
for num in range(6):
    budget_cost_use['合计' + str(num + 1)] = budget_cost_use['合计' + str(num + 1)].fillna(0)
    budget_cost_use['总计'] = budget_cost_use['总计'] + budget_cost_use['合计' + str(num + 1)]

len1 = len(budget_cost_use)
budget_cost_use.loc['费合计'] = budget_cost_use.iloc[0:len1, 1:8].sum(axis=0)
budget_cost_use.loc['费合计', '科目'] = '费合计'
budget_cost_use = budget_cost_use.reset_index(drop=True)

############################################################################################################################
Path_summary = r'单项目分析-数据\02.概预核决汇总表'
Path_calcu = r'单项目分析-数据\03.核算明细表'
Path_bom = r'单项目分析-数据\04.BOM明细表'
###############################################################################################################二、概预核决汇总表
filename_summary = os.listdir(Path_summary)
if os.listdir(Path_summary):
    for i in range(len(filename_summary)):
        if str(filename_summary[i]).count('~$') == 0:
            # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
            summary = pd.read_excel(Path_summary + '/' + str(filename_summary[i]), header=1)
else:
    summary=pd.DataFrame(columns=['无汇总表'])
###############################################################################################################三、核算明细表表
filename_calcu= os.listdir(Path_calcu)
if os.listdir(Path_calcu):
    for i in range(len(filename_calcu)):
        if str(filename_calcu[i]).count('~$') == 0:
            # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
            po = pd.read_excel(Path_calcu + '/' + str(filename_calcu[i]),sheet_name='采购PO')
            mater = pd.read_excel(Path_calcu + '/' + str(filename_calcu[i]), sheet_name='料')
            work = pd.read_excel(Path_calcu + '/' + str(filename_calcu[i]), sheet_name='工')
            cost = pd.read_excel(Path_calcu + '/' + str(filename_calcu[i]), sheet_name='费')
else:
    po= pd.DataFrame(columns=['没有采购明细'])
    mater=pd.DataFrame(columns=['没有料'])
    work = pd.DataFrame(columns=['没有工'])
    cost = pd.DataFrame(columns=['没有费'])
###############################################################################################################四、BOM明细表
filename_bom= os.listdir(Path_bom)
if os.listdir(Path_bom):
    for i in range(len(filename_bom)):
        if str(filename_bom[i]).count('~$') == 0:
            # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
            bom = pd.read_excel(Path_bom + '/' + str(filename_bom[i]))
else:
    bom=pd.DataFrame(columns=['无BOM明细'])

######################输出表格
def writer_contents(sheet, array, start_row, start_col, format=None, percent_format=None, percentlist=[]):
    # start_col = 0
    for col in array:
        if percentlist and (start_col in percentlist):
            sheet.write_column(start_row, start_col, col, percent_format)
        else:
            sheet.write_column(start_row, start_col, col, format)
        start_col += 1

# 写入表格
print('：正在设置表格格式...')

now_time = time.strftime("%Y-%m-%d-%H", time.localtime(time.time()))
book_name = '项目预算表' + now_time
workbook = xlsxwriter.Workbook(book_name + '.xlsx', {'nan_inf_to_errors': True})
worksheet0 = workbook.add_worksheet('项目预算表')

######主色调
main_format = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 16,
                                    'font_color': 'black',
                                    'bg_color': '#D9D9D9',
                                    'bold': True,
                                    'align': 'center',
                                    'valign': 'vcenter',
                                    'right': 1,
                                    'bottom':0,
                                    'border_color': 'black'
                                    })
main_format.set_align('vcenter')
main_format1 = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 14,
                                    'font_color': 'black',
                                    'bg_color': '#D9D9D9',
                                    'bold': True,
                                    'align': 'center',
                                    'valign': 'vcenter',
                                    'right': 1,
                                    'top':0,
                                    'border_color': 'black'
                                    })
main_format1.set_align('vcenter')
title_format = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 10,
                                    'font_color': 'black',
                                    'bg_color': 'D9D9D9',
                                    'bold': True,
                                    'align': 'center',
                                    'valign': 'vcenter',
                                    'border': 1,
                                    'border_color': 'black'
                                    })
title_format.set_align('vcenter')
######项目&数量  |存货
title_format1 = workbook.add_format({'font_name': 'Arial',
                                     'font_size': 10,
                                     'font_color': 'black',
                                     'bg_color': '#FFFFFF',
                                     'bold': True,
                                     'align': 'center',
                                     'valign': 'vcenter',
                                     'border': 1,
                                     'border_color': 'black'
                                     })
title_format1.set_align('vcenter')

col_format = workbook.add_format({'font_name': 'Arial',
                                  'font_size': 8,
                                  'font_color': 'white',
                                  'bg_color': '#595959',
                                  'text_wrap': True,
                                  'border': 1,
                                  'border_color': 'white',
                                  'align': 'center',
                                  'valign': 'vcenter'
                                  })

data_format = workbook.add_format({'font_name': 'Arial',
                                   'font_size': 10,
                                   'align': 'center',
                                   'valign': 'vcenter',
                                   'text_wrap': True,
                                   'border': 1,
                                    'border_color': 'black'
                                   })
data_format1 = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 10,
                                    'align': 'center',
                                    'bg_color': '#F2F2F2',
                                    'valign': 'vcenter',
                                    'text_wrap': True,
                                   'border': 1,
                                    'border_color': 'black'
                                    })
data_format1_1 = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 10,
                                    'align': 'left',
                                    'bg_color': '#F2F2F2',
                                    'valign': 'vcenter',
                                    'text_wrap': True,
                                   'border': 1,
                                    'border_color': 'black'
                                    })
data_format2 = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 10,
                                    'align': 'center',
                                    'valign': 'vcenter'
                                    })
data_format2.set_num_format('0.00')
data_format3 = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 10,
                                    'align': 'center',
                                    'valign': 'vcenter'
                                    })
data_format3.set_num_format('0.00%')
num_percent_data_format = workbook.add_format({'font_name': 'Arial',
                                               'font_size': 10,
                                               'align': 'center',
                                               'valign': 'vcenter',
                                               'num_format': '0.00%'
                                               })
statis_format2 = workbook.add_format({'font_name': 'Arial',  # 系列总计
                                      'font_size': 9,
                                      'align': 'center',
                                      'valign': 'vcenter',
                                      'bg_color': '#92CDDC'
                                      })
data_format_percent = workbook.add_format({'font_name': 'Arial',
                                           'font_size': 10,
                                           'align': 'center',
                                           'valign': 'vcenter'
                                           })
data_format_percent.set_num_format('0.00%')


worksheet0.merge_range('A2:U2', '项目预算表', main_format1)
worksheet0.merge_range('A1:U1', '海目星激光', main_format)
worksheet0.merge_range('A3:B3', '项目名称', title_format)
worksheet0.merge_range('C3:H3', '', title_format1)
worksheet0.merge_range('I3:K3', '项目号', title_format)
worksheet0.merge_range('L3:Q3', '', title_format1)
worksheet0.merge_range('R3:T3', '订单数量', title_format)
worksheet0.write('U3', '', title_format1)

worksheet0.merge_range('A4:A5', '大类', title_format)
worksheet0.merge_range('B4:B5', '小类', title_format)
worksheet0.merge_range('C4:E5', '产品设计阶段', title_format)
worksheet0.merge_range('F4:H5', '生产准备阶段', title_format)
worksheet0.merge_range('I4:N4', '装配调试阶段（设变率1%）', title_format)
worksheet0.merge_range('I5:K5', '装配', title_format)
worksheet0.merge_range('L5:N5', '装配', title_format)
worksheet0.merge_range('O4:T4', '装配调试阶段（设变率1%）', title_format)
worksheet0.merge_range('O5:Q5', '完成预验收', title_format)
worksheet0.merge_range('R5:T5', '完成验收', title_format)
worksheet0.merge_range('U4:U5', '合计',title_format)

worksheet0.merge_range('A6:A7', '料\t(元)',data_format1)
worksheet0.write('B6', '装备线材料费', data_format1)
worksheet0.write('B7', '合计', data_format1)

worksheet0.merge_range('C6:E6', 0, data_format)
worksheet0.merge_range('F6:H6', budget_mater*0.95, data_format)
worksheet0.merge_range('I6:K6', budget_mater*0.02, data_format)
worksheet0.merge_range('L6:N6', budget_mater*0.02, data_format)
worksheet0.merge_range('O6:Q6', budget_mater*0.01, data_format)
worksheet0.merge_range('R6:T6', 0, data_format)
worksheet0.write('U6', budget_mater, data_format)

worksheet0.merge_range('C7:E7', 0, data_format1)
worksheet0.merge_range('F7:H7', budget_mater*0.95, data_format1)
worksheet0.merge_range('I7:K7', budget_mater*0.02, data_format1)
worksheet0.merge_range('L7:N7', budget_mater*0.02, data_format1)
worksheet0.merge_range('O7:Q7', budget_mater*0.01, data_format1)
worksheet0.merge_range('R7:T7', 0, data_format1)
worksheet0.write('U7', budget_mater, data_format1)

worksheet0.merge_range('A8:A27', "工", data_format1)
worksheet0.write('B8', "周期", data_format1)
worksheet0.merge_range('C8:E8', 26, data_format)
worksheet0.merge_range('F8:H8', 50, data_format)
worksheet0.merge_range('I8:K8', 10, data_format)
worksheet0.merge_range('L8:N8', 20, data_format)
worksheet0.merge_range('O8:Q8', 70, data_format)
worksheet0.merge_range('R8:T8', 30, data_format)
worksheet0.write('U8', '/', data_format)
worksheet0.write_row('B9:U9',["类别","工时/天","人数","合计","工时/天","人数","合计","工时/天","人数","合计","工时/天","人数","合计","工时/天","人数","合计","工时/天","人数","合计","工时总计"],data_format1)
#worksheet0.write_row('C10:U25',budget_work_use.T.values,data_format)
#writer_contents(sheet=worksheet0, array=budget_work_use.T.values, start_row=9, start_col=1)
worksheet0.write_column('B10:B25',list(budget_work_use[budget_work_use.columns[0]]),data_format1)
for num in range(3,22):
    worksheet0.write_column(get_column_letter(num)+'10:'+get_column_letter(num)+'25', list(budget_work_use[budget_work_use.columns[num-2]]), data_format)
worksheet0.write('B26', '工时合计-H', data_format1)
worksheet0.write('B27', '工成本金额合计', data_format1)
worksheet0.merge_range('C26:E26', '=sum(E10:E25)', data_format1)
worksheet0.merge_range('F26:H26', '=sum(H10:H25)', data_format1)
worksheet0.merge_range('I26:K26', '=sum(K10:K25)', data_format1)
worksheet0.merge_range('L26:N26', '=sum(N10:N25)', data_format1)
worksheet0.merge_range('O26:Q26', '=sum(Q10:Q25)', data_format1)
worksheet0.merge_range('R26:T26', '=sum(T10:T25)', data_format1)
worksheet0.write('U26','=sum(U10:U25)', data_format1)

worksheet0.merge_range('C27:E27', '=sum(E10:E22)*75+SUM(E23:E25)*50', data_format1)
worksheet0.merge_range('F27:H27', '=sum(H10:H22)*75+SUM(H23:H25)*50', data_format1)
worksheet0.merge_range('I27:K27', '=sum(K10:K22)*75+SUM(K23:K25)*50', data_format1)
worksheet0.merge_range('L27:N27', '=sum(N10:N22)*75+SUM(N23:N25)*50', data_format1)
worksheet0.merge_range('O27:Q27', '=sum(Q10:Q22)*75+SUM(Q23:Q25)*50', data_format1)
worksheet0.merge_range('R27:T27', '=sum(T10:T22)*75+SUM(T23:T25)*50', data_format1)
worksheet0.write('U27','=sum(U10:U22)*75+SUM(U23:U25)*50', data_format1)

worksheet0.merge_range('A28:A37', "费", data_format1)
worksheet0.write_column('B28:B37',list(budget_cost_use[budget_cost_use.columns[0]]),data_format1)

t=0
for i in range(3,19):
    if i%3==0:
        t=t+1
        k=0
        for j in range(28,38):
            worksheet0.merge_range(get_column_letter(i)+str(j)+':'+get_column_letter(i+2)+str(j),budget_cost_use.loc[k,budget_cost_use.columns[t]],data_format)
            k=k+1

for i in range(28,37):
    worksheet0.write('U'+str(i), '=sum(C%d+F%d+I%d+L%d+O%d+R%d)'%(i,i,i,i,i,i),data_format)



worksheet0.write('C37','=sum(C28:C36)', data_format1)
worksheet0.write('F37','=sum(F28:F36)', data_format1)
worksheet0.write('I37','=sum(I28:I36)', data_format1)
worksheet0.write('L37','=sum(L28:L36)', data_format1)
worksheet0.write('O37','=sum(O28:O36)', data_format1)
worksheet0.write('R37','=sum(R28:R36)', data_format1)
worksheet0.write('U37','=sum(U28:U36)', data_format1)

worksheet0.merge_range('A38:U38', '1、此表在立项前由项目经理编制，经项目总负责人、交付中心负责人、产品线负责人、经管部负责人审核，由海外业务中心负责人审批；\n2、需将《预算总表》作为附件一同签批；\n3、此表作为《项目指标责任书》的附件一同签批；\n4、如在项目执行期间，因客观因素导致项目预算发生变化，需要按照此表重新发起预算变更申请。', data_format1_1)
worksheet0.merge_range('A39:G39', '制表：', data_format)
worksheet0.merge_range('H39:N39', '审核：', data_format)
worksheet0.merge_range('O39:U39', '审批：', data_format)

worksheet0.set_column("A:A", 5)
worksheet0.set_column("B:B", 12)
worksheet0.set_column("C:T", 6)
'''
worksheet0.set_column("B10:B25", 10,data_format1)
worksheet0.set_column("C10:T25", 6,data_format)
worksheet0.set_column("U10:U25", 8,data_format)
get_column_letter(c + 1)
'''
worksheet0.set_row(0, 28)
worksheet0.set_row(1, 23)
worksheet0.set_row(2, 20)
worksheet0.set_row(3, 18)
worksheet0.set_row(4, 18)
worksheet0.set_row(37, 80)
worksheet0.set_row(38, 80)
######项目预算表



workbook.close()
