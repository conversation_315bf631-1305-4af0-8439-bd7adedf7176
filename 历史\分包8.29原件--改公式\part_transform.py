#coding:utf-8
import zipfile
####################################################抬头
import threading
import pandas as pd
import numpy as np
import time
import os
import xlsxwriter
from pathlib import Path
import warnings
import tkinter as tk
import tkinter.messagebox #弹出框
from tkinter import *

from PIL import Image, ImageTk, ImageSequence
import xlsxwriter
import traceback
import datetime
from tkinter import scrolledtext
from datetime import datetime
from picture import load_image
from wholeuse import*

def  transform():
    time_start = time.time()
    frame_total.create_image(740, 60, image=load_image(r'软件附带文件\框子.png'), anchor="n")
    screm_total = scrolledtext.ScrolledText(frame_total, bg='powderblue',  # 标签背景颜色
                                            highlightthickness=0,
                                            font=('微软雅黑', 12),  # 字体和字体大小
                                            width=72, height=14  # 标签长宽(以字符长度计算)
                                            )
    # roll = Scrollbar(window_total, orient='vertical', command=frame_total.yview)
    frame_total.create_window(750, 240, window=screm_total)
    try:
        screm_total.insert(INSERT, '1：读取两个表', '\n')
        window_total.update()
        path_cal = r'转移变更处理-数据\核算汇总表'
        # need_price = ['料件编号', '最新价格']
        report_cal = []
        index = 0
        file_cal = os.listdir(path_cal)

        for i in file_cal:
            if '~$' in i:
                file_cal.remove(i)

        if os.listdir(path_cal):
            for i in range(len(file_cal)):
                if str(file_cal[i]).count('~$') == 0:
                    # report_item = pd.read_excel(filePath1 + '/' + str(file_name1[i]))
                    report_cal = pd.read_excel(path_cal + '\\' + file_cal[i], header=2)
        path_transform = r'转移变更处理-数据\转移变更对应表'
        # need_price = ['料件编号', '最新价格']
        report_transform = []
        index = 0
        file_transform = os.listdir(path_transform)

        for i in file_transform:
            if '~$' in i:
                file_transform.remove(i)

        if os.listdir(path_transform):
            for i in range(len(file_transform)):
                if str(file_transform[i]).count('~$') == 0:
                    # report_item = pd.read_excel(filePath1 + '/' + str(file_name1[i]))
                    report_transform = pd.read_excel(path_transform + '\\' + file_transform[i], sheet_name='唯一码对应',header=1)

        screm_total.insert(INSERT, '\n2：数据处理', '\n')
        window_total.update()
        report_transform = report_transform.drop_duplicates(subset=['序列号']).reset_index(drop=True)
        report_transform = report_transform.drop_duplicates(subset=['序列号.1']).reset_index(drop=True)
        report_cal['排个序'] = 0
        for i in range(len(report_cal)):
            report_cal.loc[i, '排个序'] = i + 1
        cal_start = report_cal[report_cal['序列号'].isin(report_transform['序列号'])].reset_index(drop=True)
        cal_end = report_cal[report_cal['序列号'].isin(report_transform['序列号.1'])].reset_index(drop=True)
        cal_end['变更前序列号'] = pd.merge(cal_end, report_transform, left_on=['序列号'], right_on='序列号.1', how='left')['序列号_y']

        for i in range(len(cal_end)):
            cal_start_target = cal_start[cal_start['序列号'] == cal_end.loc[i, '变更前序列号']].reset_index(drop=True)
            for num in ['成本', '料', '工单料', '设变料', '采购PO', '工', '生产工', '交付工', '费', '设计工', '其他费'
                , 'M2-3成本合计', 'M2-3料', 'M2-3工单料', 'M2-3设变料', 'M2-3采购PO', 'M2-3工', 'M2-3生产工', 'M2-3交付工', 'M2-3费', 'M2-3设计工',
                        'M2-3其他费'
                , 'M4成本合计', 'M4料', 'M4工单料', 'M4设变料', 'M4采购PO', 'M4工', 'M4生产工', 'M4交付工', 'M4费', 'M4设计工', 'M4其他费'
                , 'M5成本合计', 'M5料', 'M5工单料', 'M5设变料', 'M5采购PO', 'M5工', 'M5生产工', 'M5交付工', 'M5费', 'M5设计工', 'M5其他费']:
                cal_end.loc[i, num] = cal_start_target[num].sum() + cal_end.loc[i, num]
        report_cal_out = report_cal[~report_cal['排个序'].isin(cal_start)].reset_index(drop=True)
        report_cal_out = report_cal[~report_cal['排个序'].isin(cal_end)].reset_index(drop=True)
        report_cal_out = pd.concat([report_cal_out, cal_end]).reset_index(drop=True)
        report_cal_out = report_cal_out.sort_values(by=['排个序'], ascending=True).reset_index(drop=True)
        report_cal_out['毛利'] = report_cal_out['集团收入'] - report_cal_out['成本']
        report_cal_out.loc[report_cal_out['集团收入'] != 0, '毛利率'] = report_cal_out['毛利'] /report_cal_out['集团收入']
        report_cal_out.loc[report_cal_out['集团收入'] == 0, '毛利率'] = ''
        report_cal_out = report_cal_out.reindex(
            columns=['序列号', '区域', '行业中心', '设备类型', "客户简称", "大项目名称", "大项目号", "产品线名称", "核算项目号", '设备名称', "项目财经",
                 '项目数量', '已出货数量', '在产数量', '生产状态', '集团收入', '软件收入', '硬件收入', '成本', '毛利', '毛利率'
                , '料', '工单料', '设变料', '采购PO', '工', '生产工', '交付工', '费', '设计工', '其他费'
                , 'M2-3成本合计', 'M2-3料', 'M2-3工单料', 'M2-3设变料', 'M2-3采购PO', 'M2-3工', 'M2-3生产工', 'M2-3交付工', 'M2-3费',
                     'M2-3设计工', 'M2-3其他费'
                , '一般工单号601/608', '工单开立时间', '工单完工时间', 'M4成本合计', 'M4料', 'M4工单料', 'M4设变料', 'M4采购PO', 'M4工', 'M4生产工',
                     'M4交付工', 'M4费', 'M4设计工', 'M4其他费', '系统出货时间', '实际出货时间'
                , '返工工单号603', 'M5成本合计', 'M5料', 'M5工单料', 'M5设变料', 'M5采购PO', 'M5工', 'M5生产工', 'M5交付工', 'M5费', 'M5设计工',
                     'M5其他费', '系统验收时间', '实际验收时间'
                , '项目号整理', '成品料号', '是否预验收', '全面预算有无', 'OA状态'])
        default_date = pd.Timestamp(1990, 1, 1)
        for dat in [ '工单开立时间', '工单完工时间', '系统出货时间', '实际出货时间'
                ,  '系统验收时间', '实际验收时间']:
            report_cal_out[dat]=report_cal_out[dat].fillna(default_date)
            report_cal_out[dat] = pd.to_datetime(report_cal_out[dat], errors='coerce').dt.strftime('%Y-%m-%d').astype(str)
            report_cal_out[dat] = ['' if i == '1990-01-01' else i for i in report_cal_out[dat]]

        report_cal_out=report_cal_out.fillna('')
        l = 0
        screm_total.insert(INSERT, '\n3：数据输出', '\n')
        window_total.update()

        def writer_contents(sheet, array, start_row, start_col, format=None, percent_format=None, percentlist=[]):
            start_col = 0
            for col in array:
                if percentlist and (start_col in percentlist):
                    sheet.write_column(start_row, start_col, col, percent_format)
                else:
                    sheet.write_column(start_row, start_col, col, format)
                start_col += 1


        ####################################
        now_time = time.strftime("%Y-%m-%d-%H", time.localtime(time.time()))
        book_name = '转移变更处理-数据\成本汇总' + now_time
        workbook = xlsxwriter.Workbook(book_name + '.xlsx', {'nan_inf_to_errors': True})
        worksheet0 = workbook.add_worksheet('成本汇总')

        title_format = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 10,
                                            'font_color': 'white',
                                            'bg_color': '#1F4E78',
                                            'bold': True,
                                            'bold': True,
                                            'align': 'center',
                                            'valign': 'vcenter',
                                            'border': 1,
                                            'border_color': 'white'
                                            })
        title_format.set_align('vcenter')
        title_format1 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#006666',
                                             'bold': True,

                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format1.set_align('vcenter')
        title_format2 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#008000',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format2.set_align('vcenter')
        title_format3 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#7030A0',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format3.set_align('vcenter')

        title_format4 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#2F75B5',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format4.set_align('vcenter')
        title_format5 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#305496',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format5.set_align('vcenter')
        title_format6 = workbook.add_format({'font_name': 'Arial',
                                             'font_size': 10,
                                             'font_color': 'white',
                                             'bg_color': '#333F4F',
                                             'bold': True,
                                             'align': 'center',
                                             'valign': 'vcenter',
                                             'border': 1,
                                             'border_color': 'white'
                                             })
        title_format6.set_align('vcenter')
        col_format = workbook.add_format({'font_name': 'Arial',
                                          'font_size': 8,
                                          'font_color': 'white',
                                          'bg_color': '#1F4E78',
                                          'text_wrap': True,
                                          'border': 1,
                                          'border_color': 'white',
                                          'align': 'center',
                                          'valign': 'vcenter'
                                          })

        data_format = workbook.add_format({'font_name': 'Arial',
                                           'font_size': 10,
                                           'align': 'left',
                                           'valign': 'vcenter'
                                           })
        data_format2 = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 10,
                                            'align': 'center',
                                            'valign': 'vcenter'
                                            })
        data_format2.set_num_format('0.00')

        data_format1 = workbook.add_format({'font_name': 'Arial',
                                            'font_size': 10,
                                            'align': 'center',
                                            'valign': 'vcenter'
                                            })
        data_format_percent = workbook.add_format({'font_name': 'Arial',
                                                   'font_size': 10,
                                                   'align': 'center',
                                                   'valign': 'vcenter'
                                                   })
        data_format_percent.set_num_format('0.00%')
        num_percent_data_format = workbook.add_format({'font_name': 'Arial',
                                                       'font_size': 10,
                                                       'align': 'center',
                                                       'valign': 'vcenter',
                                                       'num_format': '0.00%'
                                                       })
        statis_format2 = workbook.add_format({'font_name': 'Arial',  # 系列总计
                                              'font_size': 9,
                                              'align': 'center',
                                              'valign': 'vcenter',
                                              'bg_color': '#92CDDC'
                                              })
        worksheet0.write_row("A3", report_cal_out.columns, title_format)

        worksheet0.write_row("AF3:AP3",
                             ['M2-3成本合计', 'M2-3料', 'M2-3工单料', 'M2-3设变料', 'M2-3采购PO', 'M2-3工', 'M2-3生产工',
                              'M2-3交付工', 'M2-3费', 'M2-3设计工', 'M2-3其他费'], title_format4)
        worksheet0.write_row("AQ3:BF3",
                             ['一般工单号601/608', '工单开立时间', '工单完工时间', 'M4成本合计', 'M4料', 'M4工单料', 'M4设变料', 'M4采购PO',
                              'M4工', 'M4生产工', 'M4交付工', 'M4费', 'M4设计工', 'M4其他费', '系统出货时间', '实际出货时间'],
                             title_format5)
        worksheet0.write_row("BG3:BT3",
                             ['返工工单号603', 'M5成本合计', 'M5料', 'M5工单料', 'M5设变料', 'M5采购PO', 'M5工', 'M5生产工', 'M5交付工',
                              'M5费', 'M5设计工', 'M5其他费', '系统验收时间', '实际验收时间'], title_format6)

        writer_contents(sheet=worksheet0, array=report_cal_out.T.values, start_row=3, start_col=0)
        worksheet0.merge_range("A1:BY1",
                               "成本汇总——————————————————————————成本汇总——————————————————————————成本汇总——————————————————————————成本汇总——————————————————————————成本汇总———————————————————————————成本汇总——————————————————————————成本汇总——————————————————————————成本汇总——————————————————————————成本汇总——————————————————————————成本汇总——————————————————————————成本汇总",
                               title_format)

        worksheet0.merge_range("A2:O2", "项目基本信息", title_format)
        worksheet0.merge_range("P2:AE2", "项目核算毛利", title_format)
        # worksheet0.merge_range("Q2:X2", "M2-5阶段明细汇总", title_format)
        worksheet0.merge_range("AF2:AP2", "M2-3阶段信息", title_format4)
        worksheet0.merge_range("AQ2:BF2", "M4阶段信息", title_format5)
        worksheet0.merge_range("BG2:BT2", "M5阶段信息", title_format6)
        worksheet0.merge_range("BU2:BY2", "辅助信息", title_format)

        worksheet0.set_row(0, 25)
        worksheet0.set_row(1, 22)
        worksheet0.set_column('A:D', 8, data_format)
        worksheet0.set_column('E:J', 12, data_format)
        worksheet0.set_column('K:M', 10, data_format2)
        worksheet0.set_column('U:U', 8, data_format_percent)
        worksheet0.set_column('P:T', 8, data_format2)
        worksheet0.set_column('V:AP', 8, data_format2)
        worksheet0.set_column('AQ:AS', 11, data_format)
        worksheet0.set_column('AT:BD', 8, data_format2)
        worksheet0.set_column('BE:BG', 11, data_format)
        worksheet0.set_column('BH:BR', 8, data_format2)
        worksheet0.set_column('BS:BV', 11, data_format)
        worksheet0.set_column('BW:BY', 8, data_format)
        workbook.close()
    except Exception as f:
        # print('异常信息为:', e)  # 异常信息为: division by zero
        print('——#@*&程序报错，异常信息为:' + traceback.format_exc())
        screm_total.insert(INSERT, '\n——#@*&程序报错，异常信息为:' + traceback.format_exc(), '\n')
        window_total.update()

    time_end = time.time()
    print('执行完成！！！！！')
    print('执行总时长:%d秒' % (time_end - time_start))
    screm_total.insert(INSERT, '\n执行完成！！！！！', '\n')
    screm_total.insert(INSERT, '\n执行总时长:%d秒' % (time_end - time_start), '\n')
    window_total.update()