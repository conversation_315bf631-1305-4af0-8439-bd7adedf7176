<!DOCTYPE html>
<!-- saved from url=(0034)https://uigradients.com/#TaranTado -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><title _msthash="149916" _msttexthash="43074018">ui渐变 - 美丽的彩色渐变</title><meta name="description" content="A handpicked collection of beautiful color gradients for designers and developers"><meta property="og:type" content="website"><meta property="og:site_name" content="uiGradients"><meta property="og:url" content="http://uigradients.com"><meta property="og:title" content="uiGradients - Beautiful colored gradients"><meta property="og:description" content="uiGradients is a handpicked collection of beautiful color gradients for designers and developers."><meta property="og:image" content="http://uigradients.com/static/images/uigradients.jpg"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:creator" content="@_ighosh"><meta name="twitter:title" content="Handpicked beautiful color gradients"><meta name="twitter:description" content="uiGradients is a handpicked collection of beautiful color gradients for designers and developers."><meta name="twitter:image" content="http://uigradients.com/static/images/uigradients.jpg"><meta name="twitter:image:width" content="1200"><meta name="twitter:image:height" content="630"><meta name="viewport" content="width=device-width,initial-scale=1"><link rel="icon" type="image/png" href="https://uigradients.com/static/images/favicon-32x32.png" sizes="32x32"><link rel="icon" type="image/png" href="https://uigradients.com/static/images/favicon-16x16.png" sizes="16x16"><link href="./ui渐变 - 美丽的彩色渐变_files/app.33da80d69744798940b135da93bc7b98.css" rel="stylesheet"><script async="" src="./ui渐变 - 美丽的彩色渐变_files/hotjar-54906.js.下载"></script><script async="" src="./ui渐变 - 美丽的彩色渐变_files/analytics.js.下载" charset="utf8"></script><script async="" src="./ui渐变 - 美丽的彩色渐变_files/modules.352fddba5b21bbfc3a08.js.下载" charset="utf-8"></script><style type="text/css">iframe#_hjRemoteVarsFrame {display: none !important; width: 1px !important; height: 1px !important; opacity: 0 !important; pointer-events: none !important;}</style></head><body><script>(function(h,o,t,j,a,r){
          h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
          h._hjSettings={hjid:54906,hjsv:5};
          a=o.getElementsByTagName('head')[0];
          r=o.createElement('script');r.async=1;
          r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
          a.appendChild(r);
      })(window,document,'//static.hotjar.com/c/hotjar-','.js?sv=');</script><main id="app"><!----> <header class="header"><div class="header__branding"><svg xmlns="http://www.w3.org/2000/svg" width="113" height="18" viewBox="0 0 113 18" class="header__logo"><path fill-rule="evenodd" d="M104.573 7.877c-1.65 1.446-6.2 5.171-6.761 5.643-.445.376-.855.752-.992.598-.137-.136.325-1.077.582-1.539.29-.547 1.316-2.462 2.445-4.582h2.035l.461-.873-2.034.018c1.573-2.976 2.223-4.087 2.223-4.087h-2.72s-.53.923-2.222 4.087h-1.967s-.17.307-.462.855h1.984l-.585 1.088c-1.777 1.703-4.283 4.066-4.733 4.435-.428.376-.838.752-.975.598-.137-.136.325-1.077.565-1.539a104.506 104.506 0 0 0 1.436-2.701c1.3-2.48.256-3.232-1.077-3.232-.804 0-3.284.735-6.72 5.215l2.787-5.198H85.14l-3.023 5.629c-2.082 2.195-4.146 3.673-5.938 3.673-1.47 0-2.069-.872-1.778-***********.53.41.906.513 1.488.462 3.181-.342 4.652-2.172 2.445-3.06.906-5.42-1.574-5.42-1.43 0-3.62 1.206-5.479 3.943-1.422 1.35-2.821 2.663-3.139 2.93-.445.377-.855.753-.992.6-.137-.137.325-1.078.582-1.54.735-1.385 3.18-5.933 3.18-5.933l-2.719.017s-.547 1.025-1.273 2.38c-1.773 1.705-4.31 4.104-4.763 4.477-.445.376-.838.752-.992.598-.137-.136.325-1.077.582-1.539.735-1.385 6.104-11.44 6.104-11.44h-2.702s-2 3.745-3.95 7.405c-.205-1.01-.923-1.898-1.898-1.898-1.418 0-3.443 1.093-5.703 4.309-1.28 1.214-2.465 2.322-2.761 2.565-.445.376-.838.752-.992.598-.137-.136.325-1.077.581-1.539.736-1.385 3.181-5.933 3.181-5.933l-2.719.017s-.41.786-1.009 1.898c-.205-1.01-.923-1.915-1.898-1.915-1.418 0-3.443 1.093-5.703 4.309-1.28 1.214-2.465 2.322-2.761 2.565-.428.376-.838.752-.975.598-.137-.136.308-1.077.564-1.539.735-1.385.855-1.47 1.283-2.291 1.145-2.189-.548-1.317-1.864-2.07a1.338 1.338 0 0 1-.332-.257 2.952 2.952 0 0 0-.005-.093c1.008-1.098 1.796-2.222.901-2.796-.838-.547-2.326-.786-3.317.445-1.09 1.373-.213 2.693.642 3.326-1.782 1.711-4.514 4.29-4.986 4.677-.427.376-.838.752-.974.598-.137-.136.324-1.077.564-1.539a438.001 438.001 0 0 0 1.624-3.026l-1.333.017H29.24c-.257 0-.496.12-.616.342l-.65 1.077a.403.403 0 0 0 0 .393c.069.12.189.206.325.206h2.087c-1.403 1.727-3.42 3.676-5.831 3.676-2.89 0-2.839-3.146-.48-6.977 2.36-3.83 5.456-5.626 6.79-5.626 1.71 0 1.35 2.446-1.232 4.43 0 0 1.129 1.128 1.95.58 2.821-1.88 3.078-6.532-.753-6.532-1.95 0-6.122 1.266-9.627 6.19-.96 1.349-1.59 2.601-1.949 3.74-1.235 1.168-2.354 2.215-2.634 2.45-.444.377-.855.753-.992.6-.136-.137.325-1.078.582-1.54.735-1.385 3.18-5.933 3.18-5.933l-2.718.017-1.288 2.407c-1.776 1.703-4.292 4.08-4.732 4.45-.444.376-.855.752-.992.598-.136-.136.325-1.077.582-1.539.735-1.385 3.18-5.933 3.18-5.933l-2.718.017s-.616 1.145-1.403 2.633c-1.026 1.779-3.915 4.822-4.873 4.822-.992 0-.479-1.077-.222-1.539.735-1.385 3.18-5.933 3.18-5.933l-2.719.017S1.231 13.093.53 14.375C-.445 16.17-.085 18 1.625 18c1.111 0 3.231-1.813 5.061-3.83-.308.564-.547.991-.667 1.23-.975 1.78-.461 2.6.667 2.6.718 0 1.642-.41 3.13-1.813.636-.605 2.2-2.159 3.738-3.697a608.615 608.615 0 0 1-1.567 2.91c-.975 1.78-.462 2.6.667 2.6.718 0 1.642-.41 3.13-1.813.548-.52 1.783-1.745 3.097-3.056C18.773 16.18 20.9 18 23.46 18c1.676 0 3.232-.718 4.6-1.761-.41 1.197.103 1.761 1.043 1.761.718 0 1.642-.41 3.13-1.813 1.133-1.076 5.188-5.157 6.819-6.808.297.22.45.614-.065 1.473-.838 1.402-1.778 3.266-2.48 4.549-.957 1.778-.461 2.599.667 2.599.701 0 1.66-.41 3.13-1.813.372-.349 1.05-1.014 1.852-1.81C41.569 16.569 42.4 18 44.084 18c1.18 0 2.394-.838 3.403-1.847-.462 1.266.051 1.847 1.009 1.847.718 0 1.658-.41 3.129-1.813.369-.35 1.049-1.02 1.855-1.82-.593 2.197.24 3.633 1.924 3.633 1.18 0 2.394-.838 3.403-1.847-.462 1.266.051 1.847 1.009 1.847.718 0 1.658-.41 3.13-1.813.641-.61 2.225-2.184 3.775-3.734a617.245 617.245 0 0 1-1.587 2.948C64.159 17.179 64.672 18 65.8 18c.718 0 1.641-.41 3.13-1.813.469-.446 1.443-1.409 2.539-2.5-.555 2.492.71 4.313 3.855 4.313 1.614 0 3.24-.769 4.773-1.946L79.053 18h2.702l.034-.051-.034.051c2.223-4.104 6.789-8.379 7.78-8.379.582 0 .36.752-.341 2.07-.787 1.436-1.283 2.427-1.984 3.71-.975 1.778-.462 2.599.667 2.599.701 0 1.641-.41 3.13-1.813.636-.604 2.194-2.156 3.726-3.693-.783 1.463-1.375 2.574-1.555 2.907-.975 1.778-.462 2.599.667 2.599.718 0 1.641-.41 3.13-1.813 1.026-.975 4.933-4.417 7.02-6.283-.139.726-.243 1.475-.369 2.18-.154.803-.29 1.419-.462 1.932-2.189.581-4.206.974-4.412 2.154-.273 1.488 1.232 1.83 2.292 1.83 1.744 0 3.488-.564 4.651-3.557 2.223-1.077 5.592-3.59 7.13-5.9.138-.222.257-1.504-.153-1.162-2.497 3.249-4.72 4.788-6.515 5.592.102-.377.205-.787.273-1.214l.684-3.42s3.147-2.685.154-2.48c-1.426.098-2.212.907-2.695 2.018zm-85.61-3.42c-1.042 0-1.521-.838-1.06-1.881.48-1.043 1.694-1.898 2.737-1.898 1.06 0 1.522.855 1.06 1.898-.462 1.043-1.693 1.88-2.736 1.88zm27.019 10.841c.89 0 2.496-1.505 3.59-3.351 1.112-1.847 1.283-3.352.411-3.352-.889 0-2.496 1.505-3.608 3.352-1.094 1.846-1.265 3.351-.393 3.351zm11.32 0c.89 0 2.497-1.505 3.591-3.351 1.112-1.847 1.283-3.352.41-3.352-.889 0-2.496 1.505-3.608 3.352-1.094 1.846-1.265 3.351-.393 3.351zM72.111 4.457c1.043 0 2.274-.838 2.736-1.881.461-1.043 0-1.898-1.06-1.898-1.044 0-2.258.855-2.737 1.898-.461 1.043.018 1.88 1.06 1.88zm6.652 4.138c-.89 0-2.497 1.488-3.609 3.352-.034.085-.085.154-.136.239.838.65 1.795 0 2.599-.667 1.094-.923 2.018-2.924 1.146-2.924zm21.888 7.934c-.12.633.735.496 1.162-.085.206-.274.428-.513.65-.855-.992.308-1.744.581-1.812.94z"></path></svg> <a href="https://skillshare.evyy.net/c/488940/298081/4650" target="_blank" class="sponsor"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="sponsor__add"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"></path><path d="M0 0h24v24H0z" fill="none"></path></svg> <svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 24.7" class="sponsor__logo"><path d="M17.5 16.9c-.6-3.9-4.6-5.3-7.3-6.2l-.2-.1c-.2-.1-.5-.2-.7-.2-2.6-.9-4.5-1.7-4.7-3.5-.1-1.1.2-2 .9-2.7.8-.9 2.2-1.3 3.9-1.3 3.1.1 4.7 1.9 4.8 2 .5.6 1.3.7 1.9.2.6-.5.7-1.3.2-1.9-.1-.1-2.4-3-6.8-3.1C7 .1 4.9.9 3.6 2.3 2.4 3.5 1.8 5.2 2 7.1c.3 3.7 4 5 6.4 5.8.2.1.5.1.7.2l.3.1c2.3.8 5.2 1.8 5.5 4.1.2 1.2-.1 2.2-.7 3-.9 1.1-2.7 1.6-4.8 1.6-3.6-.1-5.4-2.2-5.5-2.3-.5-.6-1.3-.7-1.9-.2-.6.5-.7 1.3-.2 1.9.1.1 2.6 3.3 7.5 3.4h.2c2.8 0 5.2-.9 6.6-2.5 1.2-1.5 1.7-3.3 1.4-5.3zm91.3 0c-.6-3.9-4.6-5.3-7.3-6.2l-.3-.1c-.2-.1-.5-.2-.7-.2-2.5-.9-4.4-1.7-4.5-3.5-.1-1.1.2-2 .9-2.7.8-.9 2.2-1.3 3.9-1.3 3.1.1 4.7 1.9 4.8 2 .5.6 1.3.7 1.9.2.6-.5.7-1.3.2-1.9-.1-.1-2.4-3-6.8-3.1-2.5 0-4.5.7-5.9 2.1-1.2 1.2-1.7 2.9-1.6 4.8.3 3.7 4 5 6.4 5.8.2.1.5.2.7.2l.3.1c2.3.8 5.2 1.8 5.5 4.1.2 1.2-.1 2.2-.7 3-.9 1.1-2.7 1.6-4.8 1.6-3.6-.1-5.4-2.2-5.5-2.3-.5-.6-1.3-.7-1.9-.2-.6.5-.7 1.3-.2 1.9.1.1 2.6 3.3 7.5 3.4h.2c2.8 0 5.2-.9 6.6-2.5 1.1-1.4 1.6-3.2 1.3-5.2zm-40.3 4.9H57.4V1.7c0-.7-.6-1.3-1.3-1.3-.7 0-1.3.6-1.3 1.3v21.4c0 .7.6 1.3 1.3 1.3h12.5c.7 0 1.3-.6 1.3-1.3-.1-.7-.7-1.3-1.4-1.3zM46.4.4C45.6.4 45 1 45 1.7v21.4c0 .7.6 1.3 1.3 1.3.7 0 1.3-.6 1.3-1.3V1.7c.1-.7-.5-1.3-1.2-1.3zm41.3 21.4H76.5V1.7c0-.7-.6-1.3-1.3-1.3-.7 0-1.3.6-1.3 1.3v21.4c0 .7.6 1.3 1.3 1.3h12.5c.7 0 1.3-.6 1.3-1.3 0-.7-.6-1.3-1.3-1.3zm40-21.4c-.7 0-1.3.6-1.3 1.3v9.5h-10.8V1.7c0-.7-.6-1.3-1.3-1.3-.7 0-1.3.6-1.3 1.3v21.4c0 .7.6 1.3 1.3 1.3.7 0 1.3-.6 1.3-1.3v-9.3h10.8v9.3c0 .7.6 1.3 1.3 1.3.7 0 1.3-.6 1.3-1.3V1.7c0-.7-.6-1.3-1.3-1.3zM155 22.6l-9-21.4V.6H145c-.1 0-.2 0-.3.1H144.4v.4l-9 21.4c-.3.7 0 1.5.7 1.8.2.1.3.1.5.1.5 0 1-.3 1.2-.8l2.4-5.7h10.7l2.4 5.7c.2.5.7.8 1.2.8.2 0 .3 0 .5-.1-.1-.2.3-1 0-1.7zm-14.5-7.3l4.2-10.1 4.2 10.1h-8.4zm35.6 7.2l-4.4-7.4c1.6-1.1 4.2-3.4 4.2-7 0-4.9-3.1-7.8-8.4-7.8h-6.1c-.7 0-1.3.6-1.3 1.3V23c0 .7.6 1.3 1.3 1.3.7 0 1.3-.6 1.3-1.3v-7h6.3l4.7 7.9c.3.4.7.7 1.2.7.2 0 .5-.1.7-.2.7-.5.9-1.3.5-1.9zm-6.5-9.2h-6.8V3.1h4.7c3.8 0 5.7 1.7 5.7 5.1 0 2.8-2.8 4.6-3.6 5.1zm27.6 8.5h-11.8v-8h9.9c.7 0 1.3-.6 1.3-1.3 0-.7-.6-1.3-1.3-1.3h-9.9v-8h11.8c.7 0 1.3-.6 1.3-1.3 0-.7-.6-1.3-1.3-1.3H184c-.7 0-1.3.6-1.3 1.3v21.4c0 .7.6 1.3 1.3 1.3h13.1c.7 0 1.3-.6 1.3-1.3.1-.9-.5-1.5-1.2-1.5zm-157.6.6l-8.4-12.1 7.4-7.6c.5-.5.5-1.4 0-1.9s-1.4-.5-1.9 0L25.4 12.3V1.7c0-.7-.6-1.3-1.3-1.3-.7 0-1.3.6-1.3 1.3v21.4c0 .7.6 1.3 1.3 1.3.7 0 1.3-.6 1.3-1.3v-7l3.8-3.9 8.1 11.7c.3.4.7.6 1.1.6.3 0 .5-.1.8-.2.6-.5.8-1.3.4-1.9z" class="st0"></path></svg> <div class="sponsor__cta"><p class="sponsor__byline" _msthash="820040" _msttexthash="20366502">免费获得 2 个月</p> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="sponsor__arrow"><path d="M7 10l5 5 5-5z"></path><path d="M0 0h24v24H0z" fill="none"></path></svg></div></a></div> <ul class="social header__social"><li><a href="https://uigradients.com/#" class="button button--twitter"><svg xmlns="http://www.w3.org/2000/svg" width="92" height="75" viewBox="0 0 92 75" class="button__icon"><path fill="inherit" fill-rule="evenodd" d="M92 8.85c-3.39 1.5-7.03 2.52-10.84 2.97 3.89-2.33 6.89-6.03 8.3-10.44a37.754 37.754 0 0 1-11.99 4.58A18.843 18.843 0 0 0 63.69 0C53.27 0 44.82 8.45 44.82 18.88c0 1.48.16 2.92.49 4.3-15.69-.79-29.6-8.3-38.91-19.72a18.809 18.809 0 0 0-2.55 9.49c0 6.54 3.33 12.32 8.39 15.71-3.09-.1-6-.95-8.55-2.36v.23c0 9.15 6.51 16.78 15.14 18.51-1.58.43-3.25.66-4.97.66-1.22 0-2.4-.11-3.55-.34 2.4 7.5 9.37 12.96 17.63 13.11A37.828 37.828 0 0 1 4.5 66.55c-1.52 0-3.03-.09-4.5-.26 8.35 5.35 18.27 8.48 28.93 8.48 34.72 0 53.71-28.76 53.71-53.71 0-.81-.02-1.63-.06-2.44 3.69-2.66 6.89-5.98 9.42-9.77"></path></svg> <span _msthash="862095" _msttexthash="16853863">在推特上分享</span></a></li> <li class="ml10"><a href="https://uigradients.com/#" class="button button--facebook"><svg xmlns="http://www.w3.org/2000/svg" width="86" height="87" viewBox="0 0 86 87" class="button__icon"><path fill="inherit" fill-rule="evenodd" d="M75.25.979h-64.5C4.84.979 0 5.809 0 11.729v64.5c0 5.91 4.84 10.75 10.75 10.75h32.62v-30.84h-10.3v-13.43h10.3v-6.73c0-10.38 7.66-18.5 17.44-18.5h9.49v15.16h-8.49c-2.22 0-2.87 1.28-2.87 3.04v7.03H70.3v13.43H58.94v30.84h16.31c5.91 0 10.75-4.84 10.75-10.75v-64.5C86 5.809 81.16.979 75.25.979"></path></svg> <span _msthash="862368" _msttexthash="16560440">在脸书上分享</span></a></li> <li class="ml10"><a href="https://github.com/Ghosh/uiGradients" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" width="438.549" height="438.549" viewBox="0 0 438.549 438.549" class="header__github"><path d="M409.132 114.573c-19.608-33.596-46.205-60.194-79.798-79.8-33.598-19.607-70.277-29.408-110.063-29.408-39.781 0-76.472 9.804-110.063 29.408-33.596 19.605-60.192 46.204-79.8 79.8C9.803 148.168 0 184.854 0 224.63c0 47.78 13.94 90.745 41.827 128.906 27.884 38.164 63.906 64.572 108.063 79.227 5.14.954 8.945.283 11.419-1.996 2.475-2.282 3.711-5.14 3.711-8.562 0-.571-.049-5.708-.144-15.417a2549.81 2549.81 0 0 1-.144-25.406l-6.567 1.136c-4.187.767-9.469 1.092-15.846 1-6.374-.089-12.991-.757-19.842-1.999-6.854-1.231-13.229-4.086-19.13-8.559-5.898-4.473-10.085-10.328-12.56-17.556l-2.855-6.57c-1.903-4.374-4.899-9.233-8.992-14.559-4.093-5.331-8.232-8.945-12.419-10.848l-1.999-1.431c-1.332-.951-2.568-2.098-3.711-3.429-1.142-1.331-1.997-2.663-2.568-3.997-.572-1.335-.098-2.43 1.427-3.289 1.525-.859 4.281-1.276 8.28-1.276l5.708.853c3.807.763 8.516 3.042 14.133 6.851 5.614 3.806 10.229 8.754 13.846 14.842 4.38 7.806 9.657 13.754 15.846 17.847 6.184 4.093 12.419 6.136 18.699 6.136 6.28 0 11.704-.476 16.274-1.423 4.565-.952 8.848-2.383 12.847-4.285 1.713-12.758 6.377-22.559 13.988-29.41-10.848-1.14-20.601-2.857-29.264-5.14-8.658-2.286-17.605-5.996-26.835-11.14-9.235-5.137-16.896-11.516-22.985-19.126-6.09-7.614-11.088-17.61-14.987-29.979-3.901-12.374-5.852-26.648-5.852-42.826 0-23.035 7.52-42.637 22.557-58.817-7.044-17.318-6.379-36.732 1.997-58.24 5.52-1.715 13.706-.428 24.554 3.853 10.85 4.283 18.794 7.952 23.84 10.994 5.046 3.041 9.089 5.618 12.135 7.708 17.705-4.947 35.976-7.421 54.818-7.421s37.117 2.474 54.823 7.421l10.849-6.849c7.419-4.57 16.18-8.758 26.262-12.565 10.088-3.805 17.802-4.853 23.134-3.138 8.562 21.509 9.325 40.922 2.279 58.24 15.036 16.18 22.559 35.787 22.559 58.817 0 16.178-1.958 30.497-5.853 42.966-3.9 12.471-8.941 22.457-15.125 29.979-6.191 7.521-13.901 13.85-23.131 18.986-9.232 5.14-18.182 8.85-26.84 11.136-8.662 2.286-18.415 4.004-29.263 5.146 9.894 8.562 14.842 22.077 14.842 40.539v60.237c0 3.422 1.19 6.279 3.572 8.562 2.379 2.279 6.136 2.95 11.276 1.995 44.163-14.653 80.185-41.062 108.068-79.226 27.88-38.161 41.825-81.126 41.825-128.906-.01-39.771-9.818-76.454-29.414-110.049z"></path></svg></a></li></ul></header> <section class="actionbar" closemodals="function () { [native code] }"><div class="actionbar__section"><a href="https://uigradients.com/#" class="burger actionbar__burger menu is-active"><span class="menu__icon"></span><font _mstmutation="1" _msthash="495807" _msttexthash="19358716">显示所有渐变</font></a></div> <div class="actionbar__section actionbar__section--swatch tac"><li id="c-ffffff" class="hex mono"><span class="hex__block" style="background: rgb(35, 7, 77);"></span> <span class="hex__name" _msthash="251875" _msttexthash="53469">#23074d</span> <!----></li> <span class="hex__arrow" _msthash="611845" _msttexthash="782054">→</span><li id="c-ffffff" class="hex mono last"><span class="hex__block" style="background: rgb(204, 83, 51);"></span> <span class="hex__name" _msthash="251876" _msttexthash="55822">#cc5333</span> <!----></li> <span class="hex__arrow" _msthash="612001" _msttexthash="782054" _msthidden="1">→&nbsp;</span></div> <div class="actionbar__section tar"><ul class="actionbar__nav"><li class="actionbar__nav-item"><a href="https://uigradients.com/#changeGradientDirection" id="js-direction" data-tooltip="Rotate gradient" class="actionbar__nav-link"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="19" viewBox="0 0 16 19" class="actionbar__nav-icon actionbar__nav-icon--rotate"><g fill="none" fill-rule="evenodd"><path d="M-4-1h24v24H-4z"></path><path fill="#000" fill-rule="nonzero" d="M11.55 4.55L7 0v3.07C3.06 3.56 0 6.92 0 11s3.05 7.44 7 7.93v-2.02c-2.84-.48-5-2.94-5-5.91s2.16-5.43 5-5.91V9l4.55-4.45zM15.93 10a7.906 7.906 0 0 0-1.62-3.89l-1.42 1.42c.54.75.88 1.6 1.02 2.47h2.02zM9 16.9v2.02c1.39-.17 2.74-.71 3.9-1.61l-1.44-1.44c-.75.54-1.59.89-2.46 1.03zm3.89-2.42l1.42 1.41c.9-1.16 1.45-2.5 1.62-3.89h-2.02c-.14.87-.48 1.72-1.02 2.48z"></path></g></svg></a></li> <li class="actionbar__nav-item"><a href="https://uigradients.com/#openCodeModal" id="js-code" data-tooltip="Get css" class="actionbar__nav-link"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="12" viewBox="0 0 20 12" class="actionbar__nav-icon actionbar__nav-icon--code"><g fill="none" fill-rule="evenodd"><path d="M-2-6h24v24H-2z"></path><path fill="#000" fill-rule="nonzero" d="M7.4 10.6L2.8 6l4.6-4.6L6 0 0 6l6 6 1.4-1.4zm5.2 0L17.2 6l-4.6-4.6L14 0l6 6-6 6-1.4-1.4z"></path></g></svg></a></li> <li class="actionbar__nav-item"><a href="https://uigradients.com/#OpenGradientModal" id="js-gradient" data-tooltip="Add gradient" class="actionbar__nav-link"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" class="actionbar__nav-icon actionbar__nav-icon--add"><g fill="none" fill-rule="evenodd"><path d="M-2-2h24v24H-2z"></path><path fill="#000" fill-rule="nonzero" d="M11 5H9v4H5v2h4v4h2v-4h4V9h-4V5zm-1-5C4.48 0 0 4.48 0 10s4.48 10 10 10 10-4.48 10-10S15.52 0 10 0zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"></path></g></svg></a></li> <li class="actionbar__nav-item"><a href="https://uigradients.com/#downloadGradient" id="js-download" data-tooltip="Get .jpg" class="actionbar__nav-link"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="17" viewBox="0 0 14 17" class="actionbar__nav-icon actionbar__nav-icon--download"><g fill="none" fill-rule="evenodd"><path fill="#000" fill-rule="nonzero" d="M14 6h-4V0H4v6H0l7 7 7-7zM0 15v2h14v-2H0z"></path><path d="M-5-3h24v24H-5z"></path></g></svg></a></li></ul></div></section> <div class="active palette"><ul class="shortlist"><li class="shortlist__item" style="background-color: rgb(203, 45, 62);"><!----></li><li class="shortlist__item" style="background-color: rgb(215, 107, 38);"><!----></li><li class="shortlist__item" style="background-color: rgb(255, 210, 0);"><!----></li><li class="shortlist__item" style="background-color: rgb(21, 153, 87);"><!----></li><li class="shortlist__item" style="background-color: rgb(28, 181, 224);"><!----></li><li class="shortlist__item" style="background-color: rgb(21, 87, 153);"><!----></li><li class="shortlist__item" style="background-color: rgb(239, 50, 217);"><!----></li><li class="shortlist__item" style="background-color: rgb(234, 234, 234);"><!----></li><li class="shortlist__item" style="background-color: rgb(192, 192, 203);"><!----></li><li class="shortlist__item" style="background-color: rgb(51, 51, 51);"><!----></li></ul> <ul class="palette__list"><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(189, 195, 199), rgb(44, 62, 80));"><p class="palette__name" _msthash="684073" _msttexthash="13999947">灰色等级</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(238, 156, 167), rgb(255, 221, 225));"><p class="palette__name" _msthash="684074" _msttexthash="13282022">小猪粉色</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(33, 147, 176), rgb(109, 213, 237));"><p class="palette__name" _msthash="684075" _msttexthash="11119732">酷蓝调</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(198, 255, 221), rgb(251, 215, 134), rgb(247, 121, 125));"><p class="palette__name" _msthash="684076" _msttexthash="8788208">威震 天</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(15, 32, 39), rgb(32, 58, 67), rgb(44, 83, 100));"><p class="palette__name" _msthash="684077" _msttexthash="24077495">月光下的小行星</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(18, 194, 233), rgb(196, 113, 237), rgb(246, 79, 89));"><p class="palette__name" _msthash="684078" _msttexthash="5781035">吉辛</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(185, 43, 39), rgb(21, 101, 192));"><p class="palette__name" _msthash="684079" _msttexthash="11805456">傍晚阳光</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(55, 59, 68), rgb(66, 134, 244));"><p class="palette__name" _msthash="684080" _msttexthash="17615754">黑暗的海洋</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(41, 128, 185), rgb(109, 213, 250), rgb(255, 255, 255));"><p class="palette__name" _msthash="684081" _msttexthash="15947399">凉爽的天空</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 0, 153), rgb(73, 50, 64));"><p class="palette__name" _msthash="684082" _msttexthash="5973500">尤达</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(170, 75, 107), rgb(107, 107, 131), rgb(59, 141, 153));"><p class="palette__name" _msthash="684083" _msttexthash="16905499">梅马里亚尼</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(142, 45, 226), rgb(74, 0, 224));"><p class="palette__name" _msthash="684084" _msttexthash="6217237">阿明</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(31, 64, 55), rgb(153, 242, 200));"><p class="palette__name" _msthash="684085" _msttexthash="5355064">哈维</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(249, 83, 198), rgb(185, 29, 115));"><p class="palette__name" _msthash="684086" _msttexthash="17887844">神经漫游者</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(127, 127, 213), rgb(134, 168, 231), rgb(145, 234, 228));"><p class="palette__name" _msthash="684087" _msttexthash="9452521">蔚蓝巷</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(195, 20, 50), rgb(36, 11, 54));"><p class="palette__name" _msthash="684088" _msttexthash="10481965">巫师时刻</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(241, 39, 17), rgb(245, 175, 25));"><p class="palette__name" _msthash="684089" _msttexthash="5685992">耀斑</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(101, 153, 153), rgb(244, 121, 31));"><p class="palette__name" _msthash="684090" _msttexthash="14504516">梅塔波利斯</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(221, 62, 84), rgb(107, 229, 133));"><p class="palette__name" _msthash="684091" _msttexthash="4271852">京宝</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(131, 96, 195), rgb(46, 191, 145));"><p class="palette__name" _msthash="684092" _msttexthash="5211518">桂梅</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(84, 74, 125), rgb(255, 212, 82));"><p class="palette__name" _msthash="684093" _msttexthash="4206332">京大</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 159, 255), rgb(236, 47, 75));"><p class="palette__name" _msthash="684094" _msttexthash="10209784">按设计</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(101, 78, 163), rgb(234, 175, 200));"><p class="palette__name" _msthash="684095" _msttexthash="17126538">超沃伊莱特</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 65, 108), rgb(255, 75, 43));"><p class="palette__name" _msthash="684096" _msttexthash="16087903">燃烧的橙子</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(138, 35, 135), rgb(233, 64, 87), rgb(242, 113, 33));"><p class="palette__name" _msthash="684097" _msttexthash="5095025">窃听</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(168, 255, 120), rgb(120, 255, 214));"><p class="palette__name" _msthash="684098" _msttexthash="8227232">夏日狗</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(30, 150, 0), rgb(255, 242, 0), rgb(255, 0, 0));"><p class="palette__name" _msthash="684099" _msttexthash="16613389">拉斯塔法里</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(237, 33, 58), rgb(147, 41, 30));"><p class="palette__name" _msthash="684100" _msttexthash="15439359">罪恶之城红</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(253, 200, 48), rgb(243, 115, 53));"><p class="palette__name" _msthash="684101" _msttexthash="8804497">柑橘皮</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 180, 219), rgb(0, 131, 176));"><p class="palette__name" _msthash="684102" _msttexthash="9806758">蓝树莓</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 239, 186), rgb(255, 255, 255));"><p class="palette__name" _msthash="684103" _msttexthash="5549817">玛 歌</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(89, 193, 115), rgb(161, 127, 224), rgb(93, 38, 193));"><p class="palette__name" _msthash="684104" _msttexthash="6516068">魔法</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 90, 167), rgb(255, 253, 228));"><p class="palette__name" _msthash="684105" _msttexthash="4756830">晚夜</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(218, 68, 83), rgb(137, 33, 107));"><p class="palette__name" _msthash="684106" _msttexthash="8882458">瓦努萨</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(99, 99, 99), rgb(162, 171, 88));"><p class="palette__name" _msthash="684107" _msttexthash="5772455">猥琐</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(173, 83, 137), rgb(60, 16, 83));"><p class="palette__name" _msthash="684108" _msttexthash="118287">eXpresso</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(168, 192, 255), rgb(63, 43, 150));"><p class="palette__name" _msthash="684109" _msttexthash="8528026">浅海景</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(51, 51, 51), rgb(221, 24, 24));"><p class="palette__name" _msthash="684110" _msttexthash="17163562">纯粹的欲望</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(78, 84, 200), rgb(143, 148, 251));"><p class="palette__name" _msthash="684111" _msttexthash="8244015">月亮紫</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(53, 92, 125), rgb(108, 91, 123), rgb(192, 108, 132));"><p class="palette__name" _msthash="684112" _msttexthash="13875849">红色日落</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(188, 78, 156), rgb(248, 7, 89));"><p class="palette__name" _msthash="684113" _msttexthash="2841657">移</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(64, 224, 208), rgb(255, 140, 0), rgb(255, 0, 128));"><p class="palette__name" _msthash="684114" _msttexthash="13971685">婚礼蓝调</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(62, 81, 81), rgb(222, 203, 164));"><p class="palette__name" _msthash="684115" _msttexthash="20270653">从沙子到蓝色</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(17, 153, 142), rgb(56, 239, 125));"><p class="palette__name" _msthash="684116" _msttexthash="7343206">奎帕尔</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(16, 141, 199), rgb(239, 142, 56));"><p class="palette__name" _msthash="684117" _msttexthash="15812069">双关语耶塔</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(252, 92, 125), rgb(106, 130, 251));"><p class="palette__name" _msthash="684118" _msttexthash="12544675">崇高的光</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(252, 70, 107), rgb(63, 94, 251));"><p class="palette__name" _msthash="684119" _msttexthash="12548536">崇高生动</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(201, 75, 75), rgb(75, 19, 79));"><p class="palette__name" _msthash="684120" _msttexthash="3653923">鳙</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(35, 7, 77), rgb(204, 83, 51));"><p class="palette__name" _msthash="684121" _msttexthash="128752">Taran Tado</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 251, 213), rgb(178, 10, 44));"><p class="palette__name" _msthash="684122" _msttexthash="179426">Relaxing red</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(15, 12, 41), rgb(48, 43, 99), rgb(36, 36, 62));"><p class="palette__name" _msthash="684123" _msttexthash="158678">Lawrencium</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 176, 155), rgb(150, 201, 61));"><p class="palette__name" _msthash="684124" _msttexthash="183066">Ohhappiness</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(211, 204, 227), rgb(233, 228, 240));"><p class="palette__name" _msthash="684125" _msttexthash="110253">Delicate</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(60, 59, 63), rgb(96, 92, 60));"><p class="palette__name" _msthash="684126" _msttexthash="115544">Selenium</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(202, 197, 49), rgb(243, 249, 167));"><p class="palette__name" _msthash="684127" _msttexthash="99307">Sulphur</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(128, 0, 128), rgb(255, 192, 203));"><p class="palette__name" _msthash="684128" _msttexthash="180882">Pink Flavour</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 242, 96), rgb(5, 117, 230));"><p class="palette__name" _msthash="684129" _msttexthash="175305">Rainbow Blue</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(252, 74, 26), rgb(247, 183, 51));"><p class="palette__name" _msthash="684130" _msttexthash="129753">Orange Fun</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(116, 235, 213), rgb(172, 182, 229));"><p class="palette__name" _msthash="684131" _msttexthash="200655">Digital Water</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(109, 96, 39), rgb(211, 203, 184));"><p class="palette__name" _msthash="684132" _msttexthash="96616">Lithium</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(3, 0, 30), rgb(115, 3, 192), rgb(236, 56, 188), rgb(253, 239, 249));"><p class="palette__name" _msthash="684133" _msttexthash="59982">Argon</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(102, 125, 182), rgb(0, 130, 200), rgb(0, 130, 200), rgb(102, 125, 182));"><p class="palette__name" _msthash="684134" _msttexthash="114686">Hydrogen</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(173, 169, 150), rgb(242, 242, 242), rgb(219, 219, 219), rgb(234, 234, 234));"><p class="palette__name" _msthash="684135" _msttexthash="44850">Zinc</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(225, 238, 195), rgb(240, 80, 83));"><p class="palette__name" _msthash="684136" _msttexthash="135616">Velvet Sun</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(26, 42, 108), rgb(178, 31, 31), rgb(253, 187, 45));"><p class="palette__name" _msthash="684137" _msttexthash="90285">King Yna</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(34, 193, 195), rgb(253, 187, 45));"><p class="palette__name" _msthash="684138" _msttexthash="78871">Summer</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 153, 102), rgb(255, 94, 98));"><p class="palette__name" _msthash="684139" _msttexthash="172978">Orange Coral</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(127, 0, 255), rgb(225, 0, 255));"><p class="palette__name" _msthash="684140" _msttexthash="97604">Purpink</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(201, 214, 255), rgb(226, 226, 226));"><p class="palette__name" _msthash="684141" _msttexthash="45032">Dull</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(57, 106, 252), rgb(41, 72, 255));"><p class="palette__name" _msthash="684142" _msttexthash="360958">Kimoby Is The New Blue</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(217, 167, 199), rgb(255, 252, 220));"><p class="palette__name" _msthash="684143" _msttexthash="202085">Broken Hearts</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(12, 235, 235), rgb(32, 227, 178), rgb(41, 255, 198));"><p class="palette__name" _msthash="684144" _msttexthash="46397">Subu</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(6, 190, 182), rgb(72, 177, 191));"><p class="palette__name" _msthash="684145" _msttexthash="133965">Socialive</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(100, 43, 115), rgb(198, 66, 110));"><p class="palette__name" _msthash="684146" _msttexthash="175643">Crimson Tide</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(28, 146, 210), rgb(242, 252, 254));"><p class="palette__name" _msthash="684147" _msttexthash="112658">Telegram</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 0, 0), rgb(15, 155, 15));"><p class="palette__name" _msthash="684148" _msttexthash="113880">Terminal</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(54, 209, 220), rgb(91, 134, 229));"><p class="palette__name" _msthash="684149" _msttexthash="96876">Scooter</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(203, 53, 107), rgb(189, 63, 50));"><p class="palette__name" _msthash="684150" _msttexthash="59215">Alive</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(58, 28, 113), rgb(215, 109, 119), rgb(255, 175, 123));"><p class="palette__name" _msthash="684151" _msttexthash="60515">Relay</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(40, 60, 134), rgb(69, 162, 71));"><p class="palette__name" _msthash="684152" _msttexthash="111592">Meridian</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(239, 59, 54), rgb(255, 255, 255));"><p class="palette__name" _msthash="684153" _msttexthash="154271">Compare Now</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(192, 57, 43), rgb(142, 68, 173));"><p class="palette__name" _msthash="684154" _msttexthash="60060">Mello</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(21, 153, 87), rgb(21, 87, 153));"><p class="palette__name" _msthash="684155" _msttexthash="199407">Crystal Clear</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 0, 70), rgb(28, 181, 224));"><p class="palette__name" _msthash="684156" _msttexthash="349362">Visions of Grandeur</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 121, 145), rgb(120, 255, 214));"><p class="palette__name" _msthash="684157" _msttexthash="430118">Chitty Chitty Bang Bang</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(86, 204, 242), rgb(47, 128, 237));"><p class="palette__name" _msthash="684158" _msttexthash="131170">Blue Skies</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(242, 153, 74), rgb(242, 201, 76));"><p class="palette__name" _msthash="684159" _msttexthash="99060">Sunkist</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(235, 87, 87), rgb(0, 0, 0));"><p class="palette__name" _msthash="684160" _msttexthash="43030">Coal</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(228, 77, 38), rgb(241, 101, 41));"><p class="palette__name" _msthash="684161" _msttexthash="45409">Html</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(74, 194, 154), rgb(189, 255, 243));"><p class="palette__name" _msthash="684162" _msttexthash="135447">Cinnamint</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(178, 254, 250), rgb(14, 210, 247));"><p class="palette__name" _msthash="684163" _msttexthash="114153">Maldives</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(48, 232, 191), rgb(255, 130, 53));"><p class="palette__name" _msthash="684164" _msttexthash="44447">Mini</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(214, 109, 117), rgb(226, 149, 135));"><p class="palette__name" _msthash="684165" _msttexthash="90870">Sha la la</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(32, 0, 44), rgb(203, 180, 212));"><p class="palette__name" _msthash="684166" _msttexthash="159042">Purplepine</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(195, 55, 100), rgb(29, 38, 113));"><p class="palette__name" _msthash="684167" _msttexthash="133367">Celestial</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(247, 151, 30), rgb(255, 210, 0));"><p class="palette__name" _msthash="684168" _msttexthash="367237">Learning and Leading</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(52, 232, 158), rgb(15, 52, 67));"><p class="palette__name" _msthash="684169" _msttexthash="192855">Pacific Dream</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(97, 144, 232), rgb(167, 191, 232));"><p class="palette__name" _msthash="684170" _msttexthash="74763">Venice</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(68, 160, 141), rgb(9, 54, 55));"><p class="palette__name" _msthash="684171" _msttexthash="43238">Orca</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(32, 1, 34), rgb(111, 0, 0));"><p class="palette__name" _msthash="684172" _msttexthash="257127">Love and Liberty</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(5, 117, 230), rgb(2, 27, 121));"><p class="palette__name" _msthash="684173" _msttexthash="111839">Very Blue</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(69, 104, 220), rgb(176, 106, 179));"><p class="palette__name" _msthash="684174" _msttexthash="587236">Can You Feel The Love Tonight</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(67, 198, 172), rgb(25, 22, 84));"><p class="palette__name" _msthash="684175" _msttexthash="221468">The Blue Lagoon</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(9, 48, 40), rgb(35, 122, 87));"><p class="palette__name" _msthash="684176" _msttexthash="196638">Under the Lake</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(67, 198, 172), rgb(248, 255, 174));"><p class="palette__name" _msthash="684177" _msttexthash="110734">Honey Dew</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 175, 189), rgb(255, 195, 160));"><p class="palette__name" _msthash="684178" _msttexthash="112866">Roseanna</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(240, 242, 240), rgb(0, 12, 64));"><p class="palette__name" _msthash="684179" _msttexthash="254748">What lies Beyond</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(232, 203, 192), rgb(99, 111, 164));"><p class="palette__name" _msthash="684180" _msttexthash="342277">Rose Colored Lenses</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(220, 227, 91), rgb(69, 182, 73));"><p class="palette__name" _msthash="684181" _msttexthash="89219">EasyMed</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(192, 192, 170), rgb(28, 239, 255));"><p class="palette__name" _msthash="684182" _msttexthash="122707">Cocoaa Ice</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(156, 236, 251), rgb(101, 199, 247), rgb(0, 82, 212));"><p class="palette__name" _msthash="684183" _msttexthash="97032">Jodhpur</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(219, 230, 246), rgb(197, 121, 109));"><p class="palette__name" _msthash="684184" _msttexthash="78182">Jaipur</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(52, 148, 230), rgb(236, 110, 173));"><p class="palette__name" _msthash="684185" _msttexthash="111046">Vice City</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(103, 178, 111), rgb(76, 162, 205));"><p class="palette__name" _msthash="684186" _msttexthash="43563">Mild</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(243, 144, 79), rgb(59, 67, 113));"><p class="palette__name" _msthash="684187" _msttexthash="44499">Dawn</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(238, 9, 121), rgb(255, 106, 0));"><p class="palette__name" _msthash="684188" _msttexthash="180661">Ibiza Sunset</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(167, 112, 239), rgb(207, 139, 243), rgb(253, 185, 155));"><p class="palette__name" _msthash="684189" _msttexthash="58162">Radar</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(65, 41, 90), rgb(47, 7, 67));"><p class="palette__name" _msthash="684190" _msttexthash="141011">80's Purple</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(244, 196, 243), rgb(252, 103, 250));"><p class="palette__name" _msthash="684191" _msttexthash="154674">Black Rosé</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 195, 255), rgb(255, 255, 28));"><p class="palette__name" _msthash="684192" _msttexthash="303017">Brady Brady Fun Fun</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 126, 95), rgb(254, 180, 123));"><p class="palette__name" _msthash="684193" _msttexthash="368615">Ed's Sunset Gradient</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 252, 0), rgb(255, 255, 255));"><p class="palette__name" _msthash="684194" _msttexthash="112788">Snapchat</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 0, 204), rgb(51, 51, 153));"><p class="palette__name" _msthash="684195" _msttexthash="203385">Cosmic Fusion</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(222, 97, 97), rgb(38, 87, 235));"><p class="palette__name" _msthash="684196" _msttexthash="58760">Nepal</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(239, 50, 217), rgb(137, 255, 253));"><p class="palette__name" _msthash="684197" _msttexthash="113178">Azure Pop</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(58, 97, 134), rgb(137, 37, 62));"><p class="palette__name" _msthash="684198" _msttexthash="154518">Love Couple</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(78, 205, 196), rgb(85, 98, 112));"><p class="palette__name" _msthash="684199" _msttexthash="59306">Disco</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(161, 255, 206), rgb(250, 255, 209));"><p class="palette__name" _msthash="684200" _msttexthash="90259">Limeade</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(190, 147, 197), rgb(123, 198, 204));"><p class="palette__name" _msthash="684201" _msttexthash="56667">Dania</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(189, 195, 199), rgb(44, 62, 80));"><p class="palette__name" _msthash="684202" _msttexthash="242151">50 Shades of Grey</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 216, 155), rgb(25, 84, 123));"><p class="palette__name" _msthash="684203" _msttexthash="97266">Jupiter</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(128, 128, 128), rgb(63, 173, 168));"><p class="palette__name" _msthash="684204" _msttexthash="116831">IIIT Delhi</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(252, 234, 187), rgb(248, 181, 0));"><p class="palette__name" _msthash="684205" _msttexthash="292721">Sun on the Horizon</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(248, 80, 50), rgb(231, 56, 39));"><p class="palette__name" _msthash="684206" _msttexthash="107016">Blood Red</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(247, 157, 0), rgb(100, 243, 140));"><p class="palette__name" _msthash="684207" _msttexthash="115154">Sherbert</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(203, 45, 62), rgb(239, 71, 58));"><p class="palette__name" _msthash="684208" _msttexthash="133809">Firewatch</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(86, 171, 47), rgb(168, 224, 99));"><p class="palette__name" _msthash="684209" _msttexthash="46059">Lush</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 4, 40), rgb(0, 78, 146));"><p class="palette__name" _msthash="684210" _msttexthash="62751">Frost</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(66, 39, 90), rgb(115, 75, 109));"><p class="palette__name" _msthash="684211" _msttexthash="60567">Mauve</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(20, 30, 48), rgb(36, 59, 85));"><p class="palette__name" _msthash="684212" _msttexthash="61217">Royal</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(240, 0, 0), rgb(220, 40, 30));"><p class="palette__name" _msthash="684213" _msttexthash="148837">Minimal Red</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(44, 62, 80), rgb(253, 116, 108));"><p class="palette__name" _msthash="684214" _msttexthash="45721">Dusk</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(44, 62, 80), rgb(76, 161, 175));"><p class="palette__name" _msthash="684215" _msttexthash="189722">Deep Sea Space</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(233, 100, 67), rgb(144, 78, 149));"><p class="palette__name" _msthash="684216" _msttexthash="323284">Grapefruit Sunset</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(11, 72, 107), rgb(245, 98, 23));"><p class="palette__name" _msthash="684217" _msttexthash="80080">Sunset</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(58, 123, 213), rgb(58, 96, 115));"><p class="palette__name" _msthash="684218" _msttexthash="155974">Solid Vault</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 210, 255), rgb(146, 141, 171));"><p class="palette__name" _msthash="684219" _msttexthash="179608">Bright Vault</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(33, 150, 243), rgb(244, 67, 54));"><p class="palette__name" _msthash="684220" _msttexthash="115739">Politics</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 95, 109), rgb(255, 195, 113));"><p class="palette__name" _msthash="684221" _msttexthash="204685">Sweet Morning</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 75, 31), rgb(255, 144, 104));"><p class="palette__name" _msthash="684222" _msttexthash="78260">Sylvia</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(22, 191, 253), rgb(203, 48, 102));"><p class="palette__name" _msthash="684223" _msttexthash="134602">Transfile</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(238, 205, 163), rgb(239, 98, 159));"><p class="palette__name" _msthash="684224" _msttexthash="116961">Tranquil</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(29, 67, 80), rgb(164, 57, 49));"><p class="palette__name" _msthash="684225" _msttexthash="106262">Red Ocean</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(168, 0, 119), rgb(102, 255, 0));"><p class="palette__name" _msthash="684226" _msttexthash="90142">Shahabi</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(247, 255, 0), rgb(219, 54, 164));"><p class="palette__name" _msthash="684227" _msttexthash="157937">Alihossein</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 75, 31), rgb(31, 221, 255));"><p class="palette__name" _msthash="684228" _msttexthash="29432">Ali</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(186, 83, 112), rgb(244, 226, 216));"><p class="palette__name" _msthash="684229" _msttexthash="179101">Purple White</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(224, 234, 252), rgb(207, 222, 243));"><p class="palette__name" _msthash="684230" _msttexthash="176046">Colors Of Sky</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(76, 161, 175), rgb(196, 224, 229));"><p class="palette__name" _msthash="684231" _msttexthash="75231">Decent</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 0, 0), rgb(67, 67, 67));"><p class="palette__name" _msthash="684232" _msttexthash="126516">Deep Space</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(75, 121, 161), rgb(40, 62, 81));"><p class="palette__name" _msthash="684233" _msttexthash="130637">Dark Skies</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(131, 77, 155), rgb(208, 78, 214));"><p class="palette__name" _msthash="684234" _msttexthash="49725">Suzy</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 153, 247), rgb(241, 23, 18));"><p class="palette__name" _msthash="684235" _msttexthash="115674">Superman</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(41, 128, 185), rgb(44, 62, 80));"><p class="palette__name" _msthash="684236" _msttexthash="135317">Nighthawk</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(90, 63, 55), rgb(44, 119, 68));"><p class="palette__name" _msthash="684237" _msttexthash="78923">Forest</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(77, 160, 176), rgb(211, 157, 56));"><p class="palette__name" _msthash="684238" _msttexthash="228306">Miami Dolphins</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(86, 20, 176), rgb(219, 214, 92));"><p class="palette__name" _msthash="684239" _msttexthash="317941">Minnesota Vikings</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(47, 115, 54), rgb(170, 58, 56));"><p class="palette__name" _msthash="684240" _msttexthash="136942">Christmas</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(30, 60, 114), rgb(42, 82, 152));"><p class="palette__name" _msthash="684241" _msttexthash="76011">Joomla</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(17, 67, 87), rgb(242, 148, 146));"><p class="palette__name" _msthash="684242" _msttexthash="97084">Pizelex</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(253, 116, 108), rgb(255, 144, 104));"><p class="palette__name" _msthash="684243" _msttexthash="77506">Haikus</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(234, 205, 163), rgb(214, 174, 123));"><p class="palette__name" _msthash="684244" _msttexthash="109850">Pale Wood</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(106, 48, 147), rgb(160, 68, 255));"><p class="palette__name" _msthash="684245" _msttexthash="97760">Purplin</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(69, 127, 202), rgb(86, 145, 200));"><p class="palette__name" _msthash="684246" _msttexthash="61139">Inbox</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(178, 69, 146), rgb(241, 95, 121));"><p class="palette__name" _msthash="684247" _msttexthash="60749">Blush</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(192, 36, 37), rgb(240, 203, 53));"><p class="palette__name" _msthash="684248" _msttexthash="287391">Back to the Future</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(64, 58, 62), rgb(190, 88, 105));"><p class="palette__name" _msthash="684249" _msttexthash="76752">Poncho</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(194, 229, 156), rgb(100, 179, 244));"><p class="palette__name" _msthash="684250" _msttexthash="195741">Green and Blue</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 183, 94), rgb(237, 143, 3));"><p class="palette__name" _msthash="684251" _msttexthash="174434">Light Orange</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(142, 14, 0), rgb(31, 28, 24));"><p class="palette__name" _msthash="684252" _msttexthash="96538">Netflix</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(118, 184, 82), rgb(141, 194, 111));"><p class="palette__name" _msthash="684253" _msttexthash="149045">Little Leaf</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(103, 58, 183), rgb(81, 45, 168));"><p class="palette__name" _msthash="684254" _msttexthash="154479">Deep Purple</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 201, 255), rgb(146, 254, 157));"><p class="palette__name" _msthash="684255" _msttexthash="169572">Back To Earth</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(244, 107, 69), rgb(238, 168, 73));"><p class="palette__name" _msthash="684256" _msttexthash="149864">Master Card</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 92, 151), rgb(54, 55, 149));"><p class="palette__name" _msthash="684257" _msttexthash="111111">Clear Sky</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(229, 57, 53), rgb(227, 93, 91));"><p class="palette__name" _msthash="684258" _msttexthash="96694">Passion</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(252, 0, 255), rgb(0, 219, 222));"><p class="palette__name" _msthash="684259" _msttexthash="76284">Timber</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(44, 62, 80), rgb(52, 152, 219));"><p class="palette__name" _msthash="684260" _msttexthash="366626">Between Night and Day</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(204, 204, 178), rgb(117, 117, 25));"><p class="palette__name" _msthash="684261" _msttexthash="259207">Sage Persuasion</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(48, 67, 82), rgb(215, 210, 204));"><p class="palette__name" _msthash="684262" _msttexthash="76622">Lizard</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(238, 156, 167), rgb(255, 221, 225));"><p class="palette__name" _msthash="684263" _msttexthash="76830">Piglet</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(186, 139, 2), rgb(24, 24, 24));"><p class="palette__name" _msthash="684264" _msttexthash="152308">Dark Knight</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(82, 82, 82), rgb(61, 114, 180));"><p class="palette__name" _msthash="684265" _msttexthash="238095">Curiosity blue</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 79, 249), rgb(255, 249, 76));"><p class="palette__name" _msthash="684266" _msttexthash="94055">Ukraine</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(106, 145, 19), rgb(20, 21, 23));"><p class="palette__name" _msthash="684267" _msttexthash="180323">Green to dark</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(241, 242, 181), rgb(19, 80, 88));"><p class="palette__name" _msthash="684268" _msttexthash="289484">Fresh Turboscent</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(209, 145, 60), rgb(255, 209, 148));"><p class="palette__name" _msthash="684269" _msttexthash="173082">Koko Caramel</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(123, 67, 151), rgb(220, 36, 48));"><p class="palette__name" _msthash="684270" _msttexthash="222209">Virgin America</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(142, 158, 171), rgb(238, 242, 243));"><p class="palette__name" _msthash="684271" _msttexthash="117533">Portrait</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(19, 106, 138), rgb(38, 120, 113));"><p class="palette__name" _msthash="684272" _msttexthash="240708">Turquoise flow</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 191, 143), rgb(0, 21, 16));"><p class="palette__name" _msthash="684273" _msttexthash="44746">Vine</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 0, 132), rgb(51, 0, 27));"><p class="palette__name" _msthash="684274" _msttexthash="75842">Flickr</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(131, 58, 180), rgb(253, 29, 29), rgb(252, 176, 69));"><p class="palette__name" _msthash="684275" _msttexthash="134732">Instagram</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(254, 172, 94), rgb(199, 121, 208), rgb(75, 192, 200));"><p class="palette__name" _msthash="684276" _msttexthash="59670">Atlas</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(100, 65, 165), rgb(42, 8, 69));"><p class="palette__name" _msthash="684277" _msttexthash="77766">Twitch</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 179, 71), rgb(255, 204, 51));"><p class="palette__name" _msthash="684278" _msttexthash="446316">Pastel Orange at the Sun</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(67, 206, 162), rgb(24, 90, 157));"><p class="palette__name" _msthash="684279" _msttexthash="204217">Endless River</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 161, 127), rgb(0, 34, 62));"><p class="palette__name" _msthash="684280" _msttexthash="94978">Predawn</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(54, 0, 51), rgb(11, 135, 147));"><p class="palette__name" _msthash="684281" _msttexthash="179166">Purple Bliss</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(148, 142, 153), rgb(46, 20, 55));"><p class="palette__name" _msthash="684282" _msttexthash="294723">Talking To Mice Elf</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(30, 19, 12), rgb(154, 132, 120));"><p class="palette__name" _msthash="684283" _msttexthash="117351">Hersheys</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(211, 131, 18), rgb(168, 50, 121));"><p class="palette__name" _msthash="684284" _msttexthash="193986">Crazy Orange I</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(115, 200, 169), rgb(55, 59, 68));"><p class="palette__name" _msthash="684285" _msttexthash="309023">Between The Clouds</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(171, 186, 171), rgb(255, 255, 255));"><p class="palette__name" _msthash="684286" _msttexthash="196053">Metallic Toad</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(253, 252, 71), rgb(36, 254, 65));"><p class="palette__name" _msthash="684287" _msttexthash="95433">Martini</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(131, 164, 212), rgb(182, 251, 255));"><p class="palette__name" _msthash="684288" _msttexthash="76258">Friday</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(72, 85, 99), rgb(41, 50, 60));"><p class="palette__name" _msthash="684289" _msttexthash="133198">ServQuick</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(82, 194, 52), rgb(6, 23, 0));"><p class="palette__name" _msthash="684290" _msttexthash="93665">Behongo</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(254, 140, 0), rgb(248, 54, 0));"><p class="palette__name" _msthash="684291" _msttexthash="153907">SoundCloud</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 198, 255), rgb(0, 114, 255));"><p class="palette__name" _msthash="684292" _msttexthash="343720">Facebook Messenger</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(112, 225, 245), rgb(255, 209, 148));"><p class="palette__name" _msthash="684293" _msttexthash="60619">Shore</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(85, 98, 112), rgb(255, 107, 107));"><p class="palette__name" _msthash="684294" _msttexthash="213096">Cheer Up Emo Kid</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(157, 80, 187), rgb(110, 72, 170));"><p class="palette__name" _msthash="684295" _msttexthash="118443">Amethyst</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(120, 2, 6), rgb(6, 17, 97));"><p class="palette__name" _msthash="684296" _msttexthash="152074">Man of Steel</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(179, 255, 171), rgb(18, 255, 247));"><p class="palette__name" _msthash="684297" _msttexthash="107757">Neon Life</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(170, 255, 169), rgb(17, 255, 189));"><p class="palette__name" _msthash="684298" _msttexthash="110045">Teal Love</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 0, 0), rgb(231, 76, 60));"><p class="palette__name" _msthash="684299" _msttexthash="92235">Red Mist</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(240, 194, 123), rgb(75, 18, 72));"><p class="palette__name" _msthash="684300" _msttexthash="113412">Starfall</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 78, 80), rgb(249, 212, 35));"><p class="palette__name" _msthash="684301" _msttexthash="221468">Dance To Forget</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(173, 209, 0), rgb(123, 146, 10));"><p class="palette__name" _msthash="684302" _msttexthash="112060">Parklife</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(251, 211, 233), rgb(187, 55, 125));"><p class="palette__name" _msthash="684303" _msttexthash="268723">Cherryblossoms</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(96, 108, 136), rgb(63, 76, 107));"><p class="palette__name" _msthash="684304" _msttexthash="30043">Ash</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(201, 255, 191), rgb(255, 175, 189));"><p class="palette__name" _msthash="684305" _msttexthash="77649">Virgin</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(100, 145, 115), rgb(219, 213, 164));"><p class="palette__name" _msthash="684306" _msttexthash="96954">Earthly</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(185, 147, 214), rgb(140, 166, 219));"><p class="palette__name" _msthash="684307" _msttexthash="111254">Dirty Fog</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(135, 0, 0), rgb(25, 10, 5));"><p class="palette__name" _msthash="684308" _msttexthash="132392">The Strain</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 210, 255), rgb(58, 123, 213));"><p class="palette__name" _msthash="684309" _msttexthash="43043">Reef</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(211, 149, 155), rgb(191, 230, 186));"><p class="palette__name" _msthash="684310" _msttexthash="59358">Candy</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(218, 210, 153), rgb(176, 218, 185));"><p class="palette__name" _msthash="684311" _msttexthash="79612">Autumn</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(242, 112, 156), rgb(255, 148, 114));"><p class="palette__name" _msthash="684312" _msttexthash="78221">Nelson</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(230, 218, 218), rgb(39, 64, 70));"><p class="palette__name" _msthash="684313" _msttexthash="79014">Winter</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(93, 65, 87), rgb(168, 202, 186));"><p class="palette__name" _msthash="684314" _msttexthash="181311">Forever Lost</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(221, 214, 243), rgb(250, 172, 168));"><p class="palette__name" _msthash="684315" _msttexthash="78871">Almost</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(97, 97, 97), rgb(155, 197, 195));"><p class="palette__name" _msthash="684316" _msttexthash="46358">Moor</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(80, 201, 195), rgb(150, 222, 218));"><p class="palette__name" _msthash="684317" _msttexthash="183027">Aqualicious</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(33, 95, 0), rgb(228, 228, 217));"><p class="palette__name" _msthash="684318" _msttexthash="179387">Misty Meadow</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(194, 21, 0), rgb(255, 197, 0));"><p class="palette__name" _msthash="684319" _msttexthash="63349">Kyoto</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(239, 239, 187), rgb(212, 211, 221));"><p class="palette__name" _msthash="684320" _msttexthash="176345">Sirius Tamed</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 238, 238), rgb(221, 239, 187));"><p class="palette__name" _msthash="684321" _msttexthash="97201">Jonquil</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(102, 102, 0), rgb(153, 153, 102));"><p class="palette__name" _msthash="684322" _msttexthash="136643">Petrichor</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(222, 98, 98), rgb(255, 184, 140));"><p class="palette__name" _msthash="684323" _msttexthash="179361">A Lost Memory</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(233, 211, 98), rgb(51, 51, 51));"><p class="palette__name" _msthash="684324" _msttexthash="79339">Vasily</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(213, 51, 105), rgb(203, 173, 109));"><p class="palette__name" _msthash="684325" _msttexthash="172952">Blurry Beach</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(167, 55, 55), rgb(122, 40, 40));"><p class="palette__name" _msthash="684326" _msttexthash="44239">Namn</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(248, 87, 166), rgb(255, 88, 88));"><p class="palette__name" _msthash="684327" _msttexthash="156754">Day Tripper</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(75, 108, 183), rgb(24, 40, 72));"><p class="palette__name" _msthash="684328" _msttexthash="134355">Pinot Noir</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(252, 53, 76), rgb(10, 191, 188));"><p class="palette__name" _msthash="684329" _msttexthash="57057">Miaka</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(65, 77, 11), rgb(114, 122, 23));"><p class="palette__name" _msthash="684330" _msttexthash="46254">Army</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(228, 58, 21), rgb(230, 82, 69));"><p class="palette__name" _msthash="684331" _msttexthash="98865">Shrimpy</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(192, 72, 72), rgb(72, 0, 72));"><p class="palette__name" _msthash="684332" _msttexthash="136253">Influenza</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(95, 44, 130), rgb(73, 160, 157));"><p class="palette__name" _msthash="684333" _msttexthash="128050">Calm Darya</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(236, 111, 102), rgb(243, 161, 131));"><p class="palette__name" _msthash="684334" _msttexthash="95979">Bourbon</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(116, 116, 191), rgb(52, 138, 199));"><p class="palette__name" _msthash="684335" _msttexthash="95316">Stellar</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(236, 233, 230), rgb(255, 255, 255));"><p class="palette__name" _msthash="684336" _msttexthash="77766">Clouds</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(218, 226, 248), rgb(214, 164, 164));"><p class="palette__name" _msthash="684337" _msttexthash="116337">Moonrise</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(237, 66, 100), rgb(255, 237, 188));"><p class="palette__name" _msthash="684338" _msttexthash="56875">Peach</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(220, 36, 36), rgb(74, 86, 157));"><p class="palette__name" _msthash="684339" _msttexthash="92235">Dracula</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(36, 198, 220), rgb(81, 74, 157));"><p class="palette__name" _msthash="684340" _msttexthash="76245">Mantle</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(40, 48, 72), rgb(133, 147, 152));"><p class="palette__name" _msthash="684341" _msttexthash="116467">Titanium</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(61, 126, 170), rgb(255, 228, 122));"><p class="palette__name" _msthash="684342" _msttexthash="30186">Opa</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(28, 216, 210), rgb(147, 237, 199));"><p class="palette__name" _msthash="684343" _msttexthash="112632">Sea Blizz</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(35, 37, 38), rgb(65, 67, 69));"><p class="palette__name" _msthash="684344" _msttexthash="202553">Midnight City</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(117, 127, 154), rgb(215, 221, 232));"><p class="palette__name" _msthash="684345" _msttexthash="78585">Mystic</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(92, 37, 141), rgb(67, 137, 162));"><p class="palette__name" _msthash="684346" _msttexthash="153634">Shroom Haze</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(19, 78, 94), rgb(113, 178, 128));"><p class="palette__name" _msthash="684347" _msttexthash="46956">Moss</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(43, 192, 228), rgb(234, 236, 198));"><p class="palette__name" _msthash="684348" _msttexthash="107172">Bora Bora</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(8, 80, 120), rgb(133, 216, 206));"><p class="palette__name" _msthash="684349" _msttexthash="149396">Venice Blue</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(71, 118, 230), rgb(142, 84, 233));"><p class="palette__name" _msthash="684350" _msttexthash="255853">Electric Violet</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(97, 67, 133), rgb(81, 99, 149));"><p class="palette__name" _msthash="684351" _msttexthash="95121">Kashmir</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(31, 28, 44), rgb(146, 141, 171));"><p class="palette__name" _msthash="684352" _msttexthash="131599">Steel Gray</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(22, 34, 42), rgb(58, 96, 115));"><p class="palette__name" _msthash="684353" _msttexthash="74360">Mirage</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 128, 8), rgb(255, 200, 55));"><p class="palette__name" _msthash="684354" _msttexthash="175799">Juicy Orange</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(29, 151, 108), rgb(147, 249, 185));"><p class="palette__name" _msthash="684355" _msttexthash="78507">Mojito</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(235, 51, 73), rgb(244, 92, 67));"><p class="palette__name" _msthash="684356" _msttexthash="78728">Cherry</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(221, 94, 137), rgb(247, 187, 151));"><p class="palette__name" _msthash="684357" _msttexthash="62283">Pinky</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(76, 184, 196), rgb(60, 211, 173));"><p class="palette__name" _msthash="684358" _msttexthash="87815">Sea Weed</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(31, 162, 255), rgb(18, 216, 250), rgb(166, 255, 203));"><p class="palette__name" _msthash="684359" _msttexthash="78377">Stripe</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(29, 43, 100), rgb(248, 205, 218));"><p class="palette__name" _msthash="684360" _msttexthash="253461">Purple Paradise</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 81, 47), rgb(240, 152, 25));"><p class="palette__name" _msthash="684361" _msttexthash="97435">Sunrise</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(26, 41, 128), rgb(38, 208, 206));"><p class="palette__name" _msthash="684362" _msttexthash="150943">Aqua Marine</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(170, 7, 107), rgb(97, 4, 95));"><p class="palette__name" _msthash="684363" _msttexthash="132509">Aubergine</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 81, 47), rgb(221, 36, 118));"><p class="palette__name" _msthash="684364" _msttexthash="155896">Bloody Mary</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(240, 152, 25), rgb(237, 222, 93));"><p class="palette__name" _msthash="684365" _msttexthash="132977">Mango Pulp</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(64, 59, 74), rgb(231, 233, 187));"><p class="palette__name" _msthash="684366" _msttexthash="78676">Frozen</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(229, 93, 135), rgb(95, 195, 228));"><p class="palette__name" _msthash="684367" _msttexthash="133380">Rose Water</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 57, 115), rgb(229, 229, 190));"><p class="palette__name" _msthash="684368" _msttexthash="98436">Horizon</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(204, 149, 192), rgb(219, 212, 180), rgb(122, 161, 210));"><p class="palette__name" _msthash="684369" _msttexthash="152685">Monte Carlo</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(60, 165, 92), rgb(181, 172, 73));"><p class="palette__name" _msthash="684370" _msttexthash="159211">Lemon Twist</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(52, 143, 80), rgb(86, 180, 211));"><p class="palette__name" _msthash="684371" _msttexthash="199745">Emerald Water</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(218, 34, 255), rgb(151, 51, 238));"><p class="palette__name" _msthash="684372" _msttexthash="291382">Intuitive Purple</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(2, 170, 176), rgb(0, 205, 172));"><p class="palette__name" _msthash="684373" _msttexthash="144950">Green Beach</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(237, 229, 116), rgb(225, 245, 196));"><p class="palette__name" _msthash="684374" _msttexthash="135642">Sunny Days</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(211, 16, 39), rgb(234, 56, 77));"><p class="palette__name" _msthash="684375" _msttexthash="285948">Playing with Reds</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(22, 160, 133), rgb(244, 208, 63));"><p class="palette__name" _msthash="684376" _msttexthash="255177">Harmonic Energy</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(96, 56, 19), rgb(178, 159, 148));"><p class="palette__name" _msthash="684377" _msttexthash="133757">Cool Brown</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(229, 45, 39), rgb(179, 18, 23));"><p class="palette__name" _msthash="684378" _msttexthash="93340">YouTube</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 110, 127), rgb(191, 233, 255));"><p class="palette__name" _msthash="684379" _msttexthash="157300">Noon to Dusk</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(119, 161, 211), rgb(121, 203, 202), rgb(230, 132, 174));"><p class="palette__name" _msthash="684380" _msttexthash="59488">Hazel</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(49, 71, 85), rgb(38, 160, 218));"><p class="palette__name" _msthash="684381" _msttexthash="96161">Nimvelo</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(43, 88, 118), rgb(78, 67, 118));"><p class="palette__name" _msthash="684382" _msttexthash="88751">Sea Blue</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(230, 92, 0), rgb(249, 212, 35));"><p class="palette__name" _msthash="684383" _msttexthash="113438">Blooker20</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(33, 147, 176), rgb(109, 213, 237));"><p class="palette__name" _msthash="684384" _msttexthash="112268">Sexy Blue</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(204, 43, 94), rgb(117, 58, 136));"><p class="palette__name" _msthash="684385" _msttexthash="155610">Purple Love</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(236, 0, 140), rgb(252, 103, 103));"><p class="palette__name" _msthash="684386" _msttexthash="54756">DIMIGO</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(20, 136, 204), rgb(43, 50, 178));"><p class="palette__name" _msthash="684387" _msttexthash="96122">Skyline</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 70, 127), rgb(165, 204, 130));"><p class="palette__name" _msthash="684388" _msttexthash="30693">Sel</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(7, 101, 133), rgb(255, 255, 255));"><p class="palette__name" _msthash="684389" _msttexthash="32838">Sky</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(187, 210, 197), rgb(83, 105, 118));"><p class="palette__name" _msthash="684390" _msttexthash="78897">Petrol</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(151, 150, 240), rgb(251, 199, 212));"><p class="palette__name" _msthash="684391" _msttexthash="134303">Anamnisar</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(183, 152, 145), rgb(148, 113, 107));"><p class="palette__name" _msthash="684392" _msttexthash="77532">Copper</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(187, 210, 197), rgb(83, 105, 118), rgb(41, 46, 73));"><p class="palette__name" _msthash="684393" _msttexthash="300495">Royal Blue + Petrol</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(83, 105, 118), rgb(41, 46, 73));"><p class="palette__name" _msthash="684394" _msttexthash="130754">Royal Blue</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(172, 182, 229), rgb(134, 253, 232));"><p class="palette__name" _msthash="684395" _msttexthash="62010">Windy</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 224, 0), rgb(121, 159, 12));"><p class="palette__name" _msthash="684396" _msttexthash="29315">Rea</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 65, 106), rgb(228, 229, 230));"><p class="palette__name" _msthash="684397" _msttexthash="44408">Bupe</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 226, 89), rgb(255, 167, 81));"><p class="palette__name" _msthash="684398" _msttexthash="59228">Mango</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(121, 159, 12), rgb(172, 187, 120));"><p class="palette__name" _msthash="684399" _msttexthash="75868">Reaqua</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(84, 51, 255), rgb(32, 189, 255), rgb(165, 254, 203));"><p class="palette__name" _msthash="684400" _msttexthash="73996">Lunada</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 82, 212), rgb(67, 100, 247), rgb(111, 177, 252));"><p class="palette__name" _msthash="684401" _msttexthash="133887">Bluelagoo</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(51, 77, 80), rgb(203, 202, 165));"><p class="palette__name" _msthash="684402" _msttexthash="60190">Anwar</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 65, 106), rgb(121, 159, 12), rgb(255, 224, 0));"><p class="palette__name" _msthash="684403" _msttexthash="58149">Combi</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(247, 248, 248), rgb(172, 187, 120));"><p class="palette__name" _msthash="684404" _msttexthash="107029">Ver Black</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(255, 224, 0), rgb(121, 159, 12));"><p class="palette__name" _msthash="684405" _msttexthash="31668">Ver</p></div></li><li class="palette__item"><div class="palette__gradient" style="background: linear-gradient(to right, rgb(0, 65, 106), rgb(228, 229, 230));"><p class="palette__name" _msthash="684406" _msttexthash="30927">Blu</p></div></li></ul></div> <main class="display" style="background: linear-gradient(to right, rgb(35, 7, 77), rgb(204, 83, 51));"><div class="display__gradientname"><p class="noselect" _msthash="382174" _msttexthash="10448685">塔兰·塔多</p></div> <ul id="nav" class="nav"><li id="nav--prev" class="nav__item"><a href="https://uigradients.com/#"><svg xmlns="http://www.w3.org/2000/svg" width="26" height="32" viewBox="0 0 26 32" class="nav__arrow nav__arrow--left"><path fill="inherit" d="M16.75 0L2.875 13.875.75 16l2.125 2.125L16.75 32h8.5l-16-16 16-16z"></path></svg></a></li> <li id="nav--next" class="nav__item"><a href="https://uigradients.com/#"><svg xmlns="http://www.w3.org/2000/svg" width="26" height="32" viewBox="0 0 26 32" class="nav__arrow nav__arrow--right"><path fill="inherit" d="M.75 0l16 16-16 16h8.5l13.875-13.875L25.25 16l-2.125-2.125L9.25 0z"></path></svg></a></li></ul> <div class="display__footer"><p class="display__byline noselect" _msthash="382564" _msttexthash="34673041"><a href="https://twitter.com/_ighosh" target="_blank" _istranslated="1">由@_ighosh</a>为社区打造</p></div></main> <div class="modal__mask" _msthidden="3" style="display: none;"><div class="modal__container" _msthidden="3"><h3 class="modal__title" _msthash="374816" _msttexthash="247793" _msthidden="1">
      Add New Gradient
    </h3> <p class="modal__text" _msthash="350389" _msttexthash="17598854" _msthidden="1">
      Adding a gradient is easy. All gradients are read from a gradients.json file which is available in this project's repo. Simply add your gradient details to it and submit a pull request.
    </p> <a href="https://github.com/Ghosh/uiGradients#contributing" target="_blank" class="btn" _msthash="349142" _msttexthash="158028" _msthidden="1">
        Tell me more
    </a></div></div> <div class="modal__mask" style="display: none;" _mstvisible="0"><div class="modal__container" _mstvisible="1"><h3 class="modal__title" _msthash="375102" _msttexthash="12636078" _mstvisible="2">复制 CSS 代码</h3> <div class="modal__content" _mstvisible="2"><pre class="codeblock"><code>
  <span class="codeblock__property">background</span>: <span class="codeblock__spec">#23074d</span>; <span class="codeblock__comment">/* fallback for old browsers */</span>
  <span class="codeblock__property">background</span>: -webkit-linear-gradient(to right, <span class="codeblock__spec">#23074d, #cc5333</span>); <span class="codeblock__comment">/* Chrome 10-25, Safari 5.1-6 */</span>
  <span class="codeblock__property">background</span>: linear-gradient(to right, <span class="codeblock__spec">#23074d, #cc5333</span>); <span class="codeblock__comment">/* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */</span>
</code></pre></div> <button id="js-copy" class="btn" _msthash="93535" _msttexthash="10211864" _mstvisible="2">点击复制</button></div></div></main><script type="text/javascript" src="./ui渐变 - 美丽的彩色渐变_files/app.53b91acd33d920dc4ee4.js.下载"></script><iframe name="_hjRemoteVarsFrame" title="_hjRemoteVarsFrame" tabindex="-1" aria-hidden="true" id="_hjRemoteVarsFrame" src="./ui渐变 - 美丽的彩色渐变_files/box-********************************.html" style="display: none !important; width: 1px !important; height: 1px !important; opacity: 0 !important; pointer-events: none !important;"></iframe><iframe id="_hjSafeContext_85957554" title="_hjSafeContext" tabindex="-1" aria-hidden="true" src="./ui渐变 - 美丽的彩色渐变_files/saved_resource.html" style="display: none !important; width: 1px !important; height: 1px !important; opacity: 0 !important; pointer-events: none !important;"></iframe></body></html>