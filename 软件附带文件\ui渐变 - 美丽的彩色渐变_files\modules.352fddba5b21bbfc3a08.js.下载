!function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=233)}([,,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"f",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"k",(function(){return a})),n.d(t,"e",(function(){return s})),n.d(t,"b",(function(){return c})),n.d(t,"i",(function(){return u})),n.d(t,"h",(function(){return l})),n.d(t,"g",(function(){return h})),n.d(t,"j",(function(){return d})),n.d(t,"c",(function(){return p}));var r=Object.freeze({LAST_RECORDING_ACTIVITY_STORE_DEBOUNCE:5e3,MAX_TIME_SINCE_LAST_RECORDING_ACTIVITY_IN_SESSION:12e4}),i=window.hjLazyModules,o={SCRIPT:"js",STYLESHEET:"css"},a="SNAPSHOT_UNLIMITED",s="https://hotjar.com",c=Object.freeze({id:null,selector_version:2}),u=60,l=60*u,h=24*l,d=365*h,p=u/2},,function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var r=n(23),i=n.n(r),o=n(4),a=n(21),s=n(8),c=function(e,t){if(!e)return null;var n=new s.a((new s.a).getTime()+1e3*e);if(t){var r=new s.a;r.setHours(23),r.setMinutes(59),r.setSeconds(59),r.setMilliseconds(999),n.setTime(Math.min(n,r))}return n};function u(e){var t=this,n=e.key,r=e.supportSubdomains,s=void 0!==r&&r,c=e.ttlSeconds,u=void 0===c?o.j:c,l=e.shouldSync,h=void 0===l||l,d=e.keepAliveSeconds,p=void 0===d?0:d,f=e.shouldExtendExpiryOnActivity,g=void 0!==f&&f,m=e.shouldExpireAtMidnight,v=void 0!==m&&m;this.key=n,this.ttlSeconds=u,this.shouldSync=h,this.keepAliveSeconds=p,this.shouldExpireAtMidnight=v,this.isSessionOnly=0===this.ttlSeconds,this.supportSubdomains=s,this.ttlSeconds>0&&(this.activeRefreshTimerId=null,this.keepAliveSeconds>0&&setInterval((function(){return t.refreshExpiryWithThrottling()}),1e3*p),g&&(document.addEventListener("click",(function(){return t.refreshExpiryWithThrottling()}),!1),document.addEventListener("mousemove",(function(){return t.refreshExpiryWithThrottling()}),!1),document.addEventListener("keypress",(function(){return t.refreshExpiryWithThrottling()}),!1),document.addEventListener("scroll",(function(){return t.refreshExpiryWithThrottling()}),!1),document.addEventListener("visibilityChange",(function(){return t.refreshExpiryWithThrottling()}),!1))),this.cookie=i.a.withAttributes(function(e){var t={sameSite:"Lax",secure:!0};if(e){var n=window.location.hostname;t.domain=Object(a.getMidLevelDomain)(n)}return t}(s))}function l(e){u.call(this,e)}u.prototype.getKey=function(){return this.key},u.prototype.get=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.resetExpiry,r=void 0!==n&&n,i=null!==(e=this.cookie.get(this.key))&&void 0!==e?e:null;if(!this.isSessionOnly&&this.shouldSync&&(i=this.sync(i)),r&&this.ttlSeconds&&i){var o=c(this.ttlSeconds,this.shouldExpireAtMidnight);this.cookie.set(this.key,i,{expires:o})}return i},u.prototype._set=function(e){var t=c(this.ttlSeconds,this.shouldExpireAtMidnight);this.cookie.set(this.key,e,{expires:t})},u.prototype.set=function(e,t){this._set(e),!t&&g.canUseLocalStorage()&&this.shouldSync&&(this.isSessionOnly||window.localStorage.setItem(this.key,e))},u.prototype.setEncoded=function(e,t){e!==decodeURIComponent(e)&&(e=decodeURIComponent(e)),this._set(e);var n=encodeURIComponent(e);!t&&g.canUseLocalStorage()&&this.shouldSync&&(this.isSessionOnly||window.localStorage.setItem(this.key,n))},u.prototype.clear=function(){this.cookie.remove(this.key),g.canUseLocalStorage()&&!this.isSessionOnly&&window.localStorage.removeItem(this.key)},u.prototype.sync=function(e){if(!g.canUseLocalStorage()||!this.shouldSync)return e;var t=window.localStorage.getItem(this.key),n=e;return e?window.localStorage.setItem(this.key,e):t&&!e&&(this.set(t,!0),n=t),n},u.prototype.refreshExpiryWithThrottling=function(){var e=this;this.activeRefreshTimerId||(this.activeRefreshTimerId=setTimeout((function(){e.get({resetExpiry:!0}),e.activeRefreshTimerId=null}),1e3*o.c))},l.prototype=Object.create(u.prototype),l.prototype.constructor=l,l.prototype.exists=function(e){var t=this.get();t=t?t.split(","):[];for(var n=0;n<t.length;n++)if(e.toString()===t[n])return!0;return!1},l.prototype.add=function(e){var t=this.get();(t=t?t.split(","):[]).push(e),this.setEncoded(t.join(","))},l.prototype.remove=function(e){var t=this.get(),n=(t=t?t.split(","):[]).filter((function(t){return t!==e.toString()}));this.setEncoded(n.join(","))},l.prototype.sync=function(e){if(!g.canUseLocalStorage()||!this.shouldSync)return e;var t=window.localStorage.getItem(this.key)||"";e=e?decodeURIComponent(e).split(","):[],t=t?decodeURIComponent(t).split(","):[];var n=e.concat(t),r=n.filter((function(e,t){return n.indexOf(e)===t})).join();return r&&this.setEncoded(r),r};var h=null,d=null,p=null,f=hj.tryCatch((function(){return!!navigator.cookieEnabled&&(Object.keys(i.a.get()).length>0||(g.items.COOKIE_TEST.set("1"),"1"===g.items.COOKIE_TEST.get()?(g.items.COOKIE_TEST.clear(),!0):void 0))}),"storage._cookieAvailabilityTest"),g={items:{ABSOLUTE_SESSION_IN_PROGRESS:new u({key:"_hjAbsoluteSessionInProgress",supportSubdomains:!0,ttlSeconds:o.h/2,shouldSync:!1,shouldExtendExpiryOnActivity:!0,shouldExpireAtMidnight:!0}),CACHED_USER_ATTRIBUTES:new u({key:"_hjCachedUserAttributes",ttlSeconds:0}),COOKIE_TEST:new u({key:"_hjCookieTest",ttlSeconds:0}),DEBUG_FLAG:new u({key:"hjDebug",ttlSeconds:0}),FEEDBACK_SHOW_MESSAGE:new u({key:"_hjShownFeedbackMessage",supportSubdomains:!1,ttlSeconds:o.g}),HJ_ID:new u({key:"_hjid",supportSubdomains:!0}),HJ_SESSION_USER:new u({key:"_hjSessionUser_".concat(hjSiteSettings.site_id),supportSubdomains:!0,shouldSync:!1}),HJ_SESSION:new u({key:"_hjSession_".concat(hjSiteSettings.site_id),supportSubdomains:!0,shouldSync:!1,ttlSeconds:o.h/2,shouldExtendExpiryOnActivity:!0}),FIRST_SEEN:new u({key:"_hjFirstSeen",supportSubdomains:!0,shouldSync:!1,ttlSeconds:o.h/2,shouldExtendExpiryOnActivity:!0}),HUBSPOT_UTK:new u({key:"hubspotutk"}),INCLUDE_IN_PAGEVIEW_SAMPLE:new u({key:"_hjIncludedInPageviewSample",supportSubdomains:!1,shouldSync:!1,ttlSeconds:2*o.i,keepAliveSeconds:o.i/2}),INCLUDE_IN_SESSION_SAMPLE:new u({key:"_hjIncludedInSessionSample",supportSubdomains:!1,shouldSync:!1,ttlSeconds:2*o.i,keepAliveSeconds:o.i/2}),POLL_DONE:new l({key:"_hjDonePolls",supportSubdomains:!0}),POLL_MINIMIZED:new l({key:"_hjMinimizedPolls",supportSubdomains:!0}),SESSION_RESUMED:new u({key:"_hjSessionResumed",ttlSeconds:0}),SESSION_TOO_LARGE:new u({key:"_hjSessionTooLarge",ttlSeconds:0}),SURVEY_INVITES_CLOSED:new l({key:"_hjClosedSurveyInvites"}),USER_ATTRIBUTES_HASH:new u({key:"_hjUserAttributesHash",supportSubdomains:!1,shouldSync:!1,ttlSeconds:2*o.i,keepAliveSeconds:o.i/2})},areCookiesSupported:function(){return h},setCookiesSupported:function(e){h=e},canUseCookies:function(){return null===this.areCookiesSupported()&&this.setCookiesSupported(f()),this.areCookiesSupported()},canUseLocalStorage:hj.tryCatch((function(){if(null!==d)return d;try{localStorage.setItem("_hjLocalStorageTest",1),localStorage.removeItem("_hjLocalStorageTest"),d=!0}catch(e){d=!1}return d}),"storage.canUseLocalStorage"),canUseSessionStorage:hj.tryCatch((function(){if(null!==p)return p;try{sessionStorage.setItem("_hjSessionStorageTest",1),sessionStorage.removeItem("_hjSessionStorageTest"),p=!0}catch(e){p=!1}return p}),"storage.canUseSessionStorage"),moveToSession:hj.tryCatch((function(e){if(localStorage.getItem(e.getKey())){var t=e.get();e.clear(),localStorage.removeItem(e.getKey()),e.set(t)}}),"storage.moveToSession")};hj.storage=g},,function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));var r=n(21);function i(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return o(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var a,s,c,u="🐛",l=n(30),h=function(e){return function(e){for(var t,n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return(t=console).debug.apply(t,[u,e].concat(i(r||[])))}("[safeNative] ".concat(e))},d=function(){if(document.body){var e=document.createElement("iframe");return e.id="_hjSafeContext_".concat(function(){arguments.length>0&&void 0!==arguments[0]||Object(l.c)(45887);return Math.floor(1e8*Math.random())}()),e.title="_hjSafeContext",e.tabIndex=-1,e.setAttribute("aria-hidden","true"),e.src="about:blank",e.style.setProperty("display","none","important"),e.style.setProperty("width","1px","important"),e.style.setProperty("height","1px","important"),e.style.setProperty("opacity","0","important"),e.style.setProperty("pointer-events","none","important"),document.body.appendChild(e),e}},p=Boolean(Object(r.getParameter)("hjUseSafeNativeWrapper")),f=(null===(s=window.hjSiteSettings.features||[])||void 0===s?void 0:s.indexOf("client_script.safe_date"))>=0;c=f||p?function(e,t){try{if(!a){var n=d();if(!n)return h("Unable to access an IFrame context, using default constructor."),e;a=n}var r=e.name||t;if(!r)return h("Unable to name property or missing fallbackConstructorName"),e;if(!a.contentWindow)return h("Unable to access contentWindow property"),e;var i=a.contentWindow[r];return i||(h("Unable to access constructor with name [".concat(r,"] from an IFrame context")),e)}catch(t){return h("An unexpected error occurred".concat(t instanceof Error?": ".concat(t.message):"",". Using default constructor")),e}}(Date,"Date"):Date},,function(e,t,n){"use strict";n.d(t,"c",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"j",(function(){return a})),n.d(t,"b",(function(){return s})),n.d(t,"f",(function(){return c})),n.d(t,"k",(function(){return u})),n.d(t,"h",(function(){return l})),n.d(t,"e",(function(){return h})),n.d(t,"a",(function(){return d})),n.d(t,"i",(function(){return p})),n.d(t,"g",(function(){return f}));var r=n(26),i=(n(21),function(){return Object(r.a)()}),o=function(e){return Object(r.b)(e,"ded6f544-7265-46bb-ab52-fefac2598466")},a=function(e){return JSON.stringify(e)},s=function(e){return JSON.parse(e)},c=function(e){var t=null;return function(){return null!==t?t:t=e()}},u=function(e){return 1e3*e},l=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return Math.floor(Math.random()*e)},h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return function(t){Math.max(0,e--)>0&&t(e)}},d=function(){var e=0;return function(t){t(e++)}},p=function(e){return function(t){var n=e();setTimeout((function(){t(n)}),n)}},f=function(e){var t=!1;return function(){if(!t)return t=!0,e()}}},,function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));var r=n(6),i=!1,o=!1,a=2,s=!1,c={isRecordingEnabled:function(){return i},setRecordingEnabled:function(e){i=e},isHeatmapEnabled:function(){return o},setHeatmapEnabled:function(e){o=e,hj.heatmapsEnabled=!0},getSelectorVersion:function(){return a},setSelectorVersion:function(e){a=e},isAnythingEnabled:function(){return this.isHeatmapEnabled()||this.isRecordingEnabled()},trackSessionResumed:function(){s=!0},isResumedSession:function(){return s},isFirstSeen:function(){return"1"===r.a.items.FIRST_SEEN.get()},setFirstSeen:function(){return r.a.items.FIRST_SEEN.set("1")}}},,function(e,t,n){"use strict";n.d(t,"f",(function(){return r})),n.d(t,"d",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"g",(function(){return a})),n.d(t,"b",(function(){return s})),n.d(t,"c",(function(){return c})),n.d(t,"e",(function(){return u}));var r=Object.freeze({HJ_HEATMAP_ID:"hj_heatmapId",HJ_SITE_ID:"hj_siteId",HJ_USER_ID:"hj_userId",HJ_MATCH_TYPE:"hj_matchType"}),i="_hj_heatmap_container",o="_hj_click_count",a=124,s="_hj_Ambit",c="_hj_Roboto",u={HEATMAP_GENERATION_FAILURE:"heatmap_generation_failure",NO_HEATMAP_DATA_FOUND:"no_heatmap_data_found"}},,,function(e,t,n){"use strict";n.d(t,"d",(function(){return a})),n.d(t,"e",(function(){return s})),n.d(t,"h",(function(){return c})),n.d(t,"j",(function(){return u})),n.d(t,"a",(function(){return l})),n.d(t,"c",(function(){return h})),n.d(t,"i",(function(){return d})),n.d(t,"g",(function(){return p})),n.d(t,"f",(function(){return f})),n.d(t,"b",(function(){return g}));var r,i=n(61),o=n(38),a=hj.tryCatch((function(e){var t=e||navigator.userAgent;return t.indexOf("MSIE ")>0?document.all&&!document.compatMode?5:document.all&&!window.XMLHttpRequest?6:document.all&&!document.querySelector?7:document.all&&!document.addEventListener?8:document.all&&!window.atob?9:10:-1!==t.indexOf("Trident/")?11:-1!==t.indexOf("Edge/")?12:"notIE"}),"utils"),s=(hj.tryCatch((function(e){return(e=e||navigator.userAgent).indexOf("Firefox")>-1}),"utils"),hj.tryCatch((function(e){return(e=e||navigator.userAgent).indexOf("Safari")>-1&&-1===e.indexOf("Chrome")}),"utils")),c=hj.tryCatch((function(e){var t,n,r;for(t=e.length-1;t>0;t-=1)n=Math.floor(Math.random()*(t+1)),r=e[t],e[t]=e[n],e[n]=r;return e}),"utils"),u=hj.tryCatch((function(e){return/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(e)}),"utils"),l=hj.tryCatch((function(){return hj.userDeviceType||(hj.userDeviceType=Object(i.a)(),"mobile"===hj.userDeviceType&&(hj.userDeviceType="phone")),hj.userDeviceType}),"utils"),h=hj.tryCatch((function(){if(!_hjSettings.wsHost){var e=Object(o.a)().id;_hjSettings.wsHost="ws".concat(function(e){return parseInt(e.slice(-10),16)%48+1}(e),".hotjar.com")}return _hjSettings.wsHost}),"utils"),d=function(e,t){return Object.keys(t).reduce((function(e,n){return function(e,t,n){var r=e.indexOf("#"),i=-1===r?"":e.substr(r);e=-1===r?e:e.substr(0,r);var o=new RegExp("([?&])"+t+"=.*?(&|$)","i"),a=-1!==e.indexOf("?")?"&":"?";return n?e=e.match(o)?e.replace(o,"$1"+t+"="+n+"$2"):e+a+t+"="+n:("?"===(e=e.replace(new RegExp("([?&]?)"+t+"=[^&]*","i"),"")).slice(-1)&&(e=e.slice(0,-1)),-1===e.indexOf("?")&&(e=e.replace(/&/,"?"))),e+i}(e,n,t[n])}),e)},p=function(e){var t={};return function(n){if(!t[n]){t[n]=!0;for(var r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];return e.apply(null,i)}}},f=function(e,t){var n={},r={};return[e,t].forEach((function(e){if(e)for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&"length"!==t&&(n[t]=e[t])})),Object.keys(n).sort().forEach((function(e){r[e]=n[e]})),r},g=(r=1,function(){return r++})},,,,function(e,t,n){"use strict";n.r(t),n.d(t,"getParameter",(function(){return a})),n.d(t,"tryDecodeURIComponent",(function(){return s})),n.d(t,"getUrlFromString",(function(){return c})),n.d(t,"getMidLevelDomain",(function(){return l}));var r=n(23),i=n.n(r),o=n(34);function a(e){var t,n,r=[];for(t=new RegExp("[^?&]?"+e.replace(/\[/,"\\[").replace(/]/,"\\]")+"=([^&]+)","g");n=t.exec(location.search);)r.push(s(n[1]));switch(r.length){case 0:return"";case 1:return r[0];default:return r}}function s(e){try{return decodeURIComponent(e)}catch(t){return e}}function c(e){return Object(o.a)(e,"http")||(Object(o.a)(e,"/")||(e="/"+e),e=location.protocol+"//"+location.hostname+(""!=location.port?":"+location.port:"")+e),e}var u={};function l(e){if(!u[e]){var t,n=e.lastIndexOf(".");t=function e(t,n){n=n?n-1:t.length;var r,i=t.lastIndexOf(".",n-1);i>-1&&(function(e){try{var t={domain:e};h.set("_hjTLDTest",e,t);var n=h.get("_hjTLDTest");return n&&h.remove("_hjTLDTest",t),n}catch(e){return!1}}(r=t.substring(i))||(r=e(t,i)));return r}(e,n),u[e]=t||e}return u[e]}var h=i.a.withAttributes({sameSite:"lax",secure:!0})},,function(e,t,n){var r,i,o;function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}
/*! js-cookie v3.0.1 | MIT */o=function(){"use strict";function e(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)e[r]=n[r]}return e}return function t(n,r){function i(t,i,o){if("undefined"!=typeof document){"number"==typeof(o=e({},r,o)).expires&&(o.expires=new Date(Date.now()+864e5*o.expires)),o.expires&&(o.expires=o.expires.toUTCString()),t=encodeURIComponent(t).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var a="";for(var s in o)o[s]&&(a+="; "+s,!0!==o[s]&&(a+="="+o[s].split(";")[0]));return document.cookie=t+"="+n.write(i,t)+a}}return Object.create({set:i,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var t=document.cookie?document.cookie.split("; "):[],r={},i=0;i<t.length;i++){var o=t[i].split("="),a=o.slice(1).join("=");try{var s=decodeURIComponent(o[0]);if(r[s]=n.read(a,s),e===s)break}catch(e){}}return e?r[e]:r}},remove:function(t,n){i(t,"",e({},n,{expires:-1}))},withAttributes:function(n){return t(this.converter,e({},this.attributes,n))},withConverter:function(n){return t(e({},this.converter,n),this.attributes)}},{attributes:{value:Object.freeze(r)},converter:{value:Object.freeze(n)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"})},"object"===a(t)&&void 0!==e?e.exports=o():void 0===(i="function"==typeof(r=o)?r.call(t,n,t,e):r)||(e.exports=i)},function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var r=n(12),i=n(4),o=n(8),a=0,s=function(e,t,n){if(r.a.isRecordingEnabled()){var s=o.a.now();s-a>i.a.LAST_RECORDING_ACTIVITY_STORE_DEBOUNCE&&(sessionStorage.setItem("_hjRecordingLastActivity",s),a=s)}return hj.eventStream.write(e,t,n)}},function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r=Object.freeze({MODAL:"_hj-modal",FOOTER:"_hj-footer",HEATMAP_VIEWER:"_hj-heatmap-viewer",SURVEY_INVITES:"_hj_survey_invite_container",HEATMAP_RETAKER:"_hj-heatmap-retaker",ADMIN_WIDGET:"_hj_admin_widget",INCOMING_FEEDBACK:"_hj_feedback_container",NOTICATION:"_hj-notification"}),i=Object.freeze({RETAKER:"_hjRetakerTrsToken",TARGETING:"_hjRetakerTargeting",HEATMAP_VIEWER:"_hjHeatmapViewer"})},function(e,t,n){"use strict";n.d(t,"a",(function(){return j})),n.d(t,"b",(function(){return b}));var r=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;for(var i=function(e){return"string"==typeof e&&r.test(e)},o=[],a=0;a<256;++a)o.push((a+256).toString(16).substr(1));var s=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase();if(!i(n))throw TypeError("Stringified UUID is invalid");return n};var c=function(e){if(!i(e))throw TypeError("Invalid UUID");var t,n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n};function u(e,t,n,r){switch(e){case 0:return t&n^~t&r;case 1:return t^n^r;case 2:return t&n^t&r^n&r;case 3:return t^n^r}}function l(e,t){return e<<t|e>>>32-t}var h,d=function(e,t,n){function r(e,r,i,o){if("string"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));for(var t=[],n=0;n<e.length;++n)t.push(e.charCodeAt(n));return t}(e)),"string"==typeof r&&(r=c(r)),16!==r.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var a=new Uint8Array(16+e.length);if(a.set(r),a.set(e,r.length),(a=n(a))[6]=15&a[6]|t,a[8]=63&a[8]|128,i){o=o||0;for(var u=0;u<16;++u)i[o+u]=a[u];return i}return s(a)}try{r.name=e}catch(e){}return r.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",r.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",r}("v5",80,(function(e){var t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"==typeof e){var r=unescape(encodeURIComponent(e));e=[];for(var i=0;i<r.length;++i)e.push(r.charCodeAt(i))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var o=e.length/4+2,a=Math.ceil(o/16),s=new Array(a),c=0;c<a;++c){for(var h=new Uint32Array(16),d=0;d<16;++d)h[d]=e[64*c+4*d]<<24|e[64*c+4*d+1]<<16|e[64*c+4*d+2]<<8|e[64*c+4*d+3];s[c]=h}s[a-1][14]=8*(e.length-1)/Math.pow(2,32),s[a-1][14]=Math.floor(s[a-1][14]),s[a-1][15]=8*(e.length-1)&4294967295;for(var p=0;p<a;++p){for(var f=new Uint32Array(80),g=0;g<16;++g)f[g]=s[p][g];for(var m=16;m<80;++m)f[m]=l(f[m-3]^f[m-8]^f[m-14]^f[m-16],1);for(var v=n[0],y=n[1],b=n[2],j=n[3],_=n[4],w=0;w<80;++w){var S=Math.floor(w/20),E=l(v,5)+u(S,y,b,j)+_+t[S]+f[w]>>>0;_=j,j=b,b=l(y,30)>>>0,y=v,v=E}n[0]=n[0]+v>>>0,n[1]=n[1]+y>>>0,n[2]=n[2]+b>>>0,n[3]=n[3]+j>>>0,n[4]=n[4]+_>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]})),p=new Uint8Array(16);function f(){if(!h&&!(h="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return h(p)}var g=function(e,t,n){var r=(e=e||{}).random||(e.rng||f)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var i=0;i<16;++i)t[n+i]=r[i];return t}return s(r)},m="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof window.msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto),v=new Array(16),y=m?void 0:function(){for(var e,t=0;t<16;t++)0==(3&t)&&(e=4294967296*Math.random()),v[t]=e>>>((3&t)<<3)&255;return v},b=d,j=function(e,t,n){return y&&((e=e||{}).rng=y),g(e,t,n)}},,function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=Object.freeze({POPOVER:"popover",FULL_SCREEN:"full_screen",EXTERNAL:"external_link"})},function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"e",(function(){return o})),n.d(t,"h",(function(){return a})),n.d(t,"i",(function(){return s})),n.d(t,"d",(function(){return c})),n.d(t,"c",(function(){return u})),n.d(t,"j",(function(){return l})),n.d(t,"f",(function(){return h})),n.d(t,"g",(function(){return d}));var r="_hj_hm-retaker",i=340,o={htmlError:"retaker_html_error",ready:"retaker_ready",sendHTML:"retaker_send_html",start:"retaker_start"},a="k",s=[-1!=navigator.userAgent.indexOf("Mac")?"metaKey":"ctrlKey"],c="https://help.hotjar.com/hc/en-us/articles/5215291845143-How-to-Update-a-Heatmap-Screenshot",u="_hjRetakerHtmlDebug",l={button:"button",shortcut:"shortcut"},h={basicScreenshot:"basicScreenshot",dynamicElements:"dynamicElements",cookiesBanners:"cookiesBanners"},d="_hjRetakerMode"},function(e,t,n){"use strict";n.d(t,"d",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return a})),n.d(t,"e",(function(){return s})),n.d(t,"b",(function(){return c}));var r=n(10),i=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:2,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return function(){return Math.pow(e,t++)}},o=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return function(){return e+=t}},a=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:60,t=arguments.length>1?arguments[1]:void 0;return function(){return Math.min(t(),e)}},s=function(e){return function(){return Object(r.k)(e())}},c=function(e){var t=e.exponentialTicker,n=e.jitterTicker,o=e.cliffValue,a=Object(r.g)((function(){return o}));return function(e){var t=e.exponentialTicker,n=void 0===t?i():t,o=e.jitterTicker,a=void 0===o?function(){return Object(r.h)(100)}:o;return function(){return a()+n()}}({exponentialTicker:function(){return a()||t()},jitterTicker:n})}},,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e,t){return e.substring(0,t.length)===t}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return o}));var r="_hjHeatmapId",i={component:"url",match_operation:"regex",negate:!1,pattern:".*"},o=Object.freeze({LOADED:"LOADED",PROCESSING:"PROCESSING",SUCCESS:"SUCCESS",ERROR:"ERROR"})},,,function(e,t,n){"use strict";n.d(t,"b",(function(){return d})),n.d(t,"a",(function(){return f}));var r=n(6),i=n(8),o=n(67),a=n(10);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var l=Object(o.a)({storageAccessor:r.a.items.HJ_SESSION_USER,serializer:a.j,deserializer:a.b}),h=function(e){var t,n,r,o=null!==(t=null==e?void 0:e.id)&&void 0!==t?t:Object(a.d)(Object(a.c)()),s=null!==(n=null==e?void 0:e.created)&&void 0!==n?n:i.a.now(),c=null!==(r=null==e?void 0:e.existing)&&void 0!==r&&r;return Object.freeze({id:o,created:s,existing:c})},d=function(e){l.set(e)},p=function(){var e=r.a.items.HJ_ID.get();return e?{id:Object(a.d)(hj.settings.site_id+e),existing:!0}:null},f=Object(a.f)((function(){var e=l.get();return h(null===e?c({},p()):c(c(c({},e),p()),{},{existing:!0}))}))},,function(e,t,n){"use strict";n.d(t,"b",(function(){return c})),n.d(t,"c",(function(){return h})),n.d(t,"d",(function(){return d})),n.d(t,"e",(function(){return m})),n.d(t,"f",(function(){return k})),n.d(t,"g",(function(){return S})),n.d(t,"h",(function(){return C})),n.d(t,"i",(function(){return O})),n.d(t,"j",(function(){return N})),n.d(t,"k",(function(){return R})),n.d(t,"l",(function(){return A})),n.d(t,"m",(function(){return x})),n.d(t,"a",(function(){return P}));var r,i,o=n(8),a=n(64),s=n(24),c={setup:!1,listen:hj.tryCatch((function(e){c.setup?e&&c.send(a.a.INSERTED_RULE,hj.insertedRules.getCurrentInsertedRules()):(hj.insertedRules.init(),hj.insertedRules.register(c.send.bind(c,a.a.INSERTED_RULE),!0),hj.cssBlobs.register(c.send.bind(c,a.a.INSERTED_RULE)),hj.deletedRules.init(),hj.deletedRules.register(c.send.bind(c,a.a.DELETED_RULE)),c.setup=!0)}),"behavior-data.cssRules.listen"),send:hj.tryCatch((function(e,t){t.length&&setTimeout(hj.tryCatch((function(){var n={time:hj.time.getNow(),timestamp:o.a.now(),rules:t};hj.debug.emit(e,n),Object(s.a)(e,n,!1)}),"behavior-data.cssRules"))}),"behavior-data.cssRules.send")},u=n(12),l=hj.tryCatch((function(e,t){var n=hj.hq(e),r=hj.selector(u.a.getSelectorVersion()).get(n);if(r&&t){var i={offset_x:t.pageX,offset_y:t.pageY,selector:r};u.a.isRecordingEnabled()&&(i.time=hj.time.getNow(),i.timestamp=o.a.now()),Object(s.a)(a.a.MOUSE_CLICK,i,!1).flush()}}),"behavior-data.frameMouseClicks.frameClick"),h={setup:!1,listen:hj.tryCatch((function(){h.setup||(hj.log.debug("Setting up frame mouse click listeners.","events"),h.send(),h.setup=!0)}),"behavior-data.frameMouseClicks.listen"),send:hj.tryCatch((function(){if(u.a.isAnythingEnabled()){var e=location.origin,t=hj.hq("iframe");t.length&&[].forEach.call(t,(function(t){if(t.src&&-1!==t.src.indexOf(e))try{var n=t.contentWindow;hj.hq(n).on("mousedown",(function(e){l(t,e)}))}catch(n){var r=t.src.split("?")[0];hj.Sentry&&hj.Sentry.captureMessage("SecurityError: Blocked a frame with origin ".concat(e," from accessing\n                                a frame with origin ").concat(r))}}))}}),"behavior-data.frameMouseClicks.send")},d={setup:!1,listen:hj.tryCatch((function(){d.setup||(hj.log.debug("Setting up input choice change listeners.","events"),hj.hq(document).on("change","input[type=checkbox], input[type=radio]",d.send),d.setup=!0)}),"behavior-data.inputChoiceChange.listen"),send:hj.tryCatch((function(e){if(u.a.isRecordingEnabled()){var t=hj.hq(e.target),n=hj.selector().get(t),r=t.is(":checked");Object(s.a)(a.a.INPUT_CHOICE_CHANGE,{value:r,selector:n,time:hj.time.getNow(),timestamp:o.a.now()},!0).flush()}}),"behavior-data.inputChoiceChange.send")},p=!1,f=!1,g=[],m={setup:!1,listen:hj.tryCatch((function(){m.setup||(hj.log.debug("Setting up key press listeners.","events"),hj.hq(document).on("input","input",m.update),hj.hq(document).on("blur","input",m.send),hj.hq(document).on("input","textarea",m.update),hj.hq(document).on("blur","textarea",m.send),m.setup=!0)}),"behavior-data.keyPress.listen"),update:hj.tryCatch((function(e){var t=hj.hq(e.target),n=t.val();f=f||hj.privacy.isRiskyNotAllowlistedOrSuppressedElement(e.target),g.push({time:hj.time.getNow(),timestamp:o.a.now(),selector:hj.selector().get(t),text:n,type:e.target.type,suppression:f?"full":"none"}),p=!0}),"behavior-data.keyPress.update"),send:hj.tryCatch((function(){if(u.a.isRecordingEnabled()&&p){if(f){var e=g[0],t=g[g.length-1],n=hj.privacy.getSuppressedText(e.type,t.text),r=Math.floor((t.time-e.time)/Math.max(n.length,1));g=[];for(var i=0;i<Math.min(n.length-1,500);i++)g.push({time:e.time+r*i,timestamp:e.timestamp+r*i,selector:e.selector,text:n.substring(0,i+1),type:e.type,suppression:"full"});t.text=n,g.push(t)}hj.hq.each(g,(function(e,t){delete t.type})),Object(s.a)(a.a.KEY_PRESS,g,!0).flush(),p=!1,f=!1,g=[]}}),"behavior-data.keyPress.send")},v=0,y=0,b=!1,j=0,_=0,w=null,S={setup:!1,listen:hj.tryCatch((function(){S.setup||(hj.log.debug("Setting up mouse move listeners.","events"),hj.hq(document).on("mousemove",S.update),setInterval(S.send,100),S.setup=!0)}),"behavior-data.mouseMove.listen"),update:hj.tryCatch((function(e){v=e.clientX,y=e.clientY;var t=hj.hq(document.elementFromPoint(v,y));if(t[0]){var n=t.offset();n&&(j=e.pageX-parseInt(n.left,10),_=e.pageY-parseInt(n.top,10),w=hj.selector(u.a.getSelectorVersion()).get(t),b=!0)}}),"behavior-data.mouseMove.update"),send:hj.tryCatch((function(){u.a.isAnythingEnabled()&&b&&(u.a.isRecordingEnabled()&&Object(s.a)(a.a.MOUSE_MOVE,{time:hj.time.getNow(),timestamp:o.a.now(),x:v,y:y}),u.a.isHeatmapEnabled()&&Object(s.a)(a.a.RELATIVE_MOUSE_MOVE,{offset_x:j,offset_y:_,selector:w}),b=!1)}),"behavior-data.mouseMove.send")},E=!1,k={setup:!1,listen:hj.tryCatch((function(){k.setup||(hj.log.debug("Setting up mouse click listeners.","events"),hj.hq(document).on("mousedown",k.send),k.setup=!0)}),"behavior-data.mouseClick.listen"),send:hj.tryCatch((function(e){if(u.a.isAnythingEnabled()){E&&(S.update(e),E=!1);var t=hj.selector(u.a.getSelectorVersion()).get(hj.hq(e.target)),n=[];if("target"in e&&"pageX"in e&&"pageY"in e&&void 0!==t){var r=hj.hq(e.target).offset();n.left=e.pageX-r.left,n.top=e.pageY-r.top;var i={offset_x:n.left,offset_y:n.top,selector:t,page_x:e.pageX,page_y:e.pageY,text:null},c=0,l=!1,h=e.target;do{if("BUTTON"===h.tagName||"A"===h.tagName){l=!0;break}c++,h=h.parentNode}while(h&&(c<=2||l));if(l){var d=hj.privacy.getSuppressedTextNode(e.target),p=d.content;!d.shouldSuppressNode&&p&&(i.text=p.trim().substring(0,100))}u.a.isRecordingEnabled()&&(i.time=hj.time.getNow(),i.timestamp=o.a.now()),Object(s.a)(a.a.MOUSE_CLICK,i,!1).flush()}}}),"behavior-data.mouseClick.send"),trackCoordinatesOnNextClick:function(){E=!0}},C=hj.tryCatch((function(e){var t={},n=!1;return t.listen=hj.tryCatch((function(){n||(hj.log.debug("Setting up "+e+" event listener.","events"),hj.hq(document).on(e,t.send),n=!0)}),"behavior-data."+e+".listen"),t.send=hj.tryCatch((function(){u.a.isRecordingEnabled()&&Object(s.a)(a.a.CLIPBOARD,{time:hj.time.getNow(),timestamp:o.a.now(),action:e},!0).flush()}),"behavior-data."+e+".send"),t}),"behavior-data.newClipboardEventListener"),O={listen:hj.tryCatch((function(){document.addEventListener("visibilitychange",(function(){return O.send(!document.hidden)}),!1)}),"behavior-data.pageVisibility.listen"),send:hj.tryCatch((function(e){var t={time:hj.time.getNow(),timestamp:o.a.now(),page_visible:e};hj.debug.emit("page_visibility",t),Object(s.a)(a.a.PAGE_VISIBILITY,t,!1).flush()}),"behavior-data.pageVisibility.send")},I=!1,N={setup:!1,listen:hj.tryCatch((function(){N.setup||(hj.log.debug("Setting up scroll listeners.","events"),window.addEventListener("scroll",N.update,!0),setInterval(N.send,100),0!==hj.ui.getScrollPosition().top&&N.update(),N.setup=!0)}),"behavior-data.scroll.listen"),update:hj.tryCatch((function(e){r=e?e.target===window.document?window:e.target:window,I=!0}),"behavior-data.scroll.update"),send:hj.tryCatch((function(){if(u.a.isRecordingEnabled()&&I){var e=(r=r||window)===window?"window":hj.selector(u.a.getSelectorVersion()).get(hj.hq(r))||"window",t=hj.ui.getScrollPosition(r);Object(s.a)(a.a.SCROLL,{elem:e,time:hj.time.getNow(),timestamp:o.a.now(),x:t.left,y:t.top}),I=!1}}),"behavior-data.scroll.send")},T=0,R={setup:!1,listen:hj.tryCatch((function(){R.setup||(hj.log.debug("Setting up scroll reach listeners.","events"),hj.hq(window).on("scroll resize",R.send,!0),R.setup=!0)}),"behavior-data.scrollReach.listen"),send:hj.tryCatch((function(){if(u.a.isHeatmapEnabled()){var e=hj.ui.getBottomAsPercentage();e>T&&(T=e,Object(s.a)(a.a.SCROLL_REACH,{max_bottom:T},!0))}}),"behavior-data.scrollReach.send")},A={setup:!1,listen:hj.tryCatch((function(){A.setup||(hj.log.debug("Setting up select change listeners.","events"),hj.hq(document).on("change","select",A.send),A.setup=!0)}),"behavior-data.selectChange.listen"),send:hj.tryCatch((function(e){if(u.a.isRecordingEnabled()){var t=hj.hq(e.target),n=hj.selector().get(t),r=t.val();Object(s.a)(a.a.SELECT_CHANGE,{value:r,selector:n,time:hj.time.getNow(),timestamp:o.a.now()},!0).flush()}}),"behavior-data.selectChange.send")},x={setup:!1,listen:hj.tryCatch((function(){x.setup||(hj.log.debug("Setting up screen size change listeners.","events"),i=hj.ui.getWindowSize(),setInterval(x.checkResize,1e3),x.checkResize(),x.setup=!0)}),"behavior-data.viewportResize.listen"),checkResize:hj.tryCatch((function(){var e=hj.ui.getWindowSize();e.width===i.width&&e.height===i.height||(i=e,x.send())}),"behavior-data.viewportResize.checkResize"),send:hj.tryCatch((function(){u.a.isRecordingEnabled()&&Object(s.a)(a.a.VIEWPORT_RESIZE,{time:hj.time.getNow(),timestamp:o.a.now(),w:i.width,h:i.height}).flush()}),"behavior-data.viewportResize.send")},P={setup:!1,listen:hj.tryCatch((function(){P.setup||(hj.adoptedStyleSheets.init(),hj.adoptedStyleSheets.register(P.send.bind(P,"adopted_style_sheets"),!0),P.setup=!0)}),"behavior-data.adoptedStyleSheets.listen"),send:hj.tryCatch((function(e,t){t&&setTimeout(hj.tryCatch((function(){var n={time:hj.time.getNow(),timestamp:o.a.now(),parentSelector:t.parentSelector,sheets:t.sheets,nodeId:t.nodeId};hj.debug.emit(e,n),Object(s.a)(e,n,!1)}),"behavior-data.adoptedStyleSheets"))}),"behavior-data.adoptedStyleSheets.send")}},,,function(e,t,n){"use strict";function r(e){return unescape(encodeURIComponent(e))}function i(e){return decodeURIComponent(escape(e))}function o(e){return btoa(r(e))}function a(e){return i(atob(e))}n.d(t,"d",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"b",(function(){return o})),n.d(t,"a",(function(){return a}))},function(e,t,n){"use strict";var r;function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return c})),n.d(t,"c",(function(){return y}));var o,a=Object.freeze({LIVE:"LIVE",REVIEW_WEBAPP:"REVIEW_WEBAPP",REVIEW_WEBAPP_NEW:"REVIEW_WEBAPP_NEW",REVIEW:"REVIEW",REVIEW_NEW:"REVIEW_NEW",STAGING:"STAGING",DEV:"DEV",DEV_OLD:"DEV_OLD"}),s=(o=document.location.host.match(/^(insights-webapp|surveys-webapp|insights|surveys)-(.+)\..+\.eks\.hotjar\.com$/))&&o[2],c=Object.freeze((i(r={},a.LIVE,{INSIGHTS:"insights.hotjar.com",SURVEYS:"surveys.hotjar.com"}),i(r,a.REVIEW,{INSIGHTS:s&&"insights-".concat(s,".live.eks.hotjar.com"),SURVEYS:s&&"surveys-".concat(s,".live.eks.hotjar.com")}),i(r,a.REVIEW_NEW,{INSIGHTS:s&&"insights-".concat(s,".review.eks.hotjar.com"),SURVEYS:s&&"surveys-".concat(s,".review.eks.hotjar.com")}),i(r,a.REVIEW_WEBAPP,{INSIGHTS:s&&"insights-webapp-".concat(s,".live.eks.hotjar.com"),SURVEYS:s&&"surveys-webapp-".concat(s,".live.eks.hotjar.com")}),i(r,a.REVIEW_WEBAPP_NEW,{INSIGHTS:s&&"insights-webapp-".concat(s,".review.eks.hotjar.com"),SURVEYS:s&&"surveys-webapp-".concat(s,".review.eks.hotjar.com")}),i(r,a.STAGING,{INSIGHTS:"insights-staging.hotjar.com",SURVEYS:"surveys-staging.hotjar.com"}),i(r,a.DEV,{INSIGHTS:"local.hotjar.com:8443",SURVEYS:"surveys.local.hotjar.com:8443"}),i(r,a.DEV_OLD,{INSIGHTS:"local.hotjar.com",SURVEYS:"surveys.local.hotjar.com"}),r)),u=function(e){return function(t,n){return t===c[e][n]}},l=u(a.DEV),h=u(a.DEV_OLD),d=u(a.LIVE),p=u(a.REVIEW_WEBAPP),f=u(a.REVIEW_WEBAPP_NEW),g=u(a.REVIEW),m=u(a.REVIEW_NEW),v=u(a.STAGING),y=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"INSIGHTS",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document.location.host;return d(t,e)?a.LIVE:l(t,e)?a.DEV:h(t,e)?a.DEV_OLD:p(t,e)||f(t,e)?a.REVIEW_WEBAPP:g(t,e)||m(t,e)?a.REVIEW:v(t,e)?a.STAGING:a.LIVE}},,,,,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"d",(function(){return s}));var r="_hj-widget-container",i="_hj-widget-iframe";function o(e,t){for(var n=e.querySelectorAll(t),r=0;r<n.length;r++){var i=n[r];i&&i.parentElement&&i.parentElement.removeChild(i)}}function a(e){o(e,".".concat(r))}function s(e){o(e,".".concat(i))}},,,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(4),i=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s,r=0,i=!1;function c(){0!==--r||i||t.bind(this)()}function u(){i=!0,n.bind(this)()}Object.keys(e).forEach((function(t){var n=e[t];"string"==typeof n&&(n=[n]),r+=n.length,n.forEach((function(e){o(e,t,c,u)}))}))},o=function(e,t,n,i){var o;t===r.d.SCRIPT?(o=document.createElement("script")).src="".concat(hj.scriptDomain).concat(e):t===r.d.STYLESHEET&&((o=document.createElement("link")).href="".concat(hj.scriptDomain).concat(e),o.rel="stylesheet"),o.onload=n,o.onerror=i,document.getElementsByTagName("head")[0].appendChild(o)};function a(){}function s(){var e=this.src||this.href;hj.exceptions.log("Failed to load module: ".concat(e,"."),"loader")}},function(e,t,n){"use strict";n.d(t,"b",(function(){return l})),n.d(t,"a",(function(){return s}));var r,i,o=n(113),a=n(6),s=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"unknown";if(void 0!==r)return hj.log.debug("[".concat(e,"] Included in pageview/snapshot sample (already set)? ").concat(r),"sampling",{r:hj.settings.r,user_id:o.a.getAsNumber()}),r;var t=a.a.items.INCLUDE_IN_PAGEVIEW_SAMPLE.get({resetExpiry:!0}),n="1"==t;if(hj.log.debug("[".concat(e,"] Included in pageview/snapshot sample (from cookie)? ").concat(n),"sampling",{cookie_value:t}),t)return r=n;var i=hj.url.getParameter("hjIncludeInSample")||hj.url.getParameter("hjIncludeInPageviewSample");switch(i){case"0":return r=!1,hj.log.debug("You have set includedInPageviewSample to false.","sampling"),a.a.items.INCLUDE_IN_PAGEVIEW_SAMPLE.set("0"),r;case"1":return r=!0,a.a.items.INCLUDE_IN_PAGEVIEW_SAMPLE.set("1"),hj.log.debug("You have set includedInPageviewSample to true.","sampling"),r}return(r=void 0===hj.settings.r||null===hj.settings.r?o.a.compareRatio(1):o.a.compareRatio(hj.settings.r))&&a.a.items.INCLUDE_IN_PAGEVIEW_SAMPLE.set("1"),hj.log.debug("[".concat(e,"] Included in pageview/snapshot sample? ").concat(r),"sampling",{r:hj.settings.r,user_id:o.a.getAsNumber()}),r},c=Math.random(),u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"unknown";if(void 0!==i)return hj.log.debug("[".concat(e,"] Included in session/CC sample (already set)? ").concat(i),"sampling",{rec_value:hj.settings.rec_value,includedInSessionSample:i,mathRandomResult:c}),i;if(0===hj.settings.rec_value)return a.a.items.INCLUDE_IN_SESSION_SAMPLE.set("0"),hj.log.debug("[".concat(e,"] rec_value has gone to 0, removing from sample."),"sampling",{rec_value:hj.settings.rec_value,includedInSessionSample:i}),i=!1;if(!hj.settings.continuous_capture_enabled)return hj.log.debug("[".concat(e,"] Included in session/CC sample (setting check)? ").concat(i),"sampling",{"hj.settings.continuous_capture_enabled":hj.settings.continuous_capture_enabled}),i=!1;var t=a.a.items.INCLUDE_IN_SESSION_SAMPLE.get({resetExpiry:!0}),n="1"==t;if(hj.log.debug("[".concat(e,"] Included in session/CC sample (from cookie)? ").concat(n),"sampling",{cookie_value:t}),t)return i=n;var r=hj.url.getParameter("hjIncludeInSessionSample");switch(r){case"0":return i=!1,hj.log.debug("You have set includedInSessionSample to false.","sampling"),i;case"1":return i=!0,a.a.items.INCLUDE_IN_SESSION_SAMPLE.set(i?"1":"0"),hj.log.debug("You have set includedInSessionSample to true.","sampling"),i}return(i=1===hj.settings.rec_value||void 0!==hj.settings.rec_value&&null!==hj.settings.rec_value&&(c||1)<=hj.settings.rec_value)?a.a.items.INCLUDE_IN_SESSION_SAMPLE.set("1"):a.a.items.INCLUDE_IN_SESSION_SAMPLE.set("0"),hj.log.debug("[".concat(e,"] Included in session/CC sample? ").concat(i),"sampling",{rec_value:hj.settings.rec_value,mathRandomResult:c}),i},l=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"unknown";return hj.settings.continuous_capture_enabled?u(e):s(e)}},,,function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"e",(function(){return s})),n.d(t,"d",(function(){return u})),n.d(t,"b",(function(){return l})),n.d(t,"c",(function(){return h})),n.d(t,"f",(function(){return d}));var r=n(17);var i="tracking.performance.telemetry.if.time_to_open",o=["tracking.performance.telemetry.if.time_to_render","tracking.performance.telemetry.if.full_time_to_render",i],a={IF_TIME_TO_RENDER:"tracking.performance.telemetry.if.time_to_render",IF_FULL_TIME_TO_RENDER:"tracking.performance.telemetry.if.full_time_to_render",IF_TIME_TO_OPEN:i};function s(){return"performance"in window&&"now"in window.performance&&"getEntriesByType"in window.performance&&"find"in Array.prototype&&"lastIndexOf"in String.prototype}function c(e,t){var n;if(!s())return 0;var r=performance.getEntriesByType("resource").find((function(t){return!!t.name&&(e instanceof RegExp?e.test(t.name):-1!==t.name.lastIndexOf(e))}));return r?Math.round(null!==(n=r[t])&&void 0!==n?n:0):0}function u(){return s()?Math.round(performance.now()):0}function l(){return c("hotjar-".concat(hj.settings.site_id,".js"),"startTime")}function h(){return c(/preact-incoming-feedback\.[\w]+\.js$/,"startTime")}function d(e){var t=Object.keys(e).reduce((function(t,n){return e[n]>0&&o.indexOf(n)>-1&&t.push({metric:{name:n,type:"distribution",value:e[n]},tags:[{type:"device",value:Object(r.a)()}]}),t}),[]);!hj.isPreview&&hj.features.hasFeature("feedback.widget_telemetry")&&Math.random()<.001&&t.length>0&&hj.ajax.post("".concat(hj.apiUrlBase,"/tracking/metrics"),{metrics:t})}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window,t=null!=e&&e==e.window,n=!t,r=(n||document.documentElement,"Tv Desktop Tablet Mobile".split(" ")),i=function(e){var t=function(t){return e.match(t)};return t(/GoogleTV|SmartTV|Internet.TV|NetCast|NETTV|AppleTV|boxee|Kylo|Roku|DLNADOC|CE\-HTML/i)||t(/Xbox|PLAYSTATION.3|Wii/i)?"tv":t(/iPad/i)||t(/tablet/i)&&!t(/RX-34/i)||t(/FOLIO/i)||t(/Linux/i)&&t(/Android/i)&&!t(/Fennec|mobi|HTC.Magic|HTCX06HT|Nexus.One|SC-02B|fone.945|Chromebook/i)||t(/Kindle/i)||t(/Mac.OS/i)&&t(/Silk/i)||t(/GT-P10|SC-01C|SHW-M180S|SGH-T849|SCH-I800|SHW-M180L|SPH-P100|SGH-I987|zt180|HTC(.Flyer|\_Flyer)|Sprint.ATP51|ViewPad7|pandigital(sprnova|nova)|Ideos.S7|Dell.Streak.7|Advent.Vega|A101IT|A70BHT|MID7015|Next2|nook/i)||t(/MB511/i)&&t(/RUTEM/i)?"tablet":t(/BOLT|Fennec|Iris|Maemo|Minimo|Mobi|mowser|NetFront|Novarra|Prism|RX-34|Skyfire|Tear|XV6875|XV6975|Google.Wireless.Transcoder/i)||t(/Opera/i)&&t(/Windows.NT.5/i)&&t(/HTC|Xda|Mini|Vario|SAMSUNG\-GT\-i8000|SAMSUNG\-SGH\-i9/i)?"mobile":t(/Windows.(NT|XP|ME|9)/)&&!t(/Phone/i)||t(/Win(9|.9|NT)/i)||t(/Macintosh|PowerPC/i)&&!t(/Silk/i)||t(/Linux/i)&&t(/X11/i)||t(/Solaris|SunOS|BSD/i)||t(/Bot|Crawler|Spider|Yahoo|ia_archiver|Covario-IDS|findlinks|DataparkSearch|larbin|Mediapartners-Google|NG-Search|Snappy|Teoma|Jeeves|TinEye/i)&&!t(/Mobile/i)||t(/\b(CrOS|Chromebook)\b/i)?"desktop":"mobile"},o=i(e.navigator?e.navigator.userAgent:e.request?e.request.headers["user-agent"]:"No User-Agent Provided"),a=function(e){return o===e},s=function(){var e=[].slice.call(arguments,0);return 2===e.length&&o===e[0]?(o=e[1],u()):1===e.length&&"string"==typeof e[0]&&(o=e[0],u()),o};function c(){for(var e=r.length;e--;)s["is"+r[e]]=a(r[e].toLowerCase())}function u(){c()}return s.is=a,s.test=i,u(),s}()},,,function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=Object.freeze({IDENTIFY_USER:"identify_user",AUTOTAG_RECORDING:"autotag_recording",TAG_RECORDING:"tag_recording",HEATMAP_HELO:"heatmap_helo",RECORDING_HELO:"recording_helo",REPORT_USER_ID:"report_user_id",MUTATION:"mutation",MOUSE_CLICK:"mouse_click",INPUT_CHOICE_CHANGE:"input_choice_change",KEY_PRESS:"key_press",MOUSE_MOVE:"mouse_move",RELATIVE_MOUSE_MOVE:"relative_mouse_move",CLIPBOARD:"clipboard",PAGE_VISIBILITY:"page_visibility",SCROLL_REACH:"scroll_reach",SCROLL:"scroll",SELECT_CHANGE:"select_change",VIEWPORT_RESIZE:"viewport_resize",SCRIPT_PERFORMANCE:"script_performance",REPORT_CONTENT:"report_content",INSERTED_RULE:"inserted_rule",DELETED_RULE:"deleted_rule"})},,,function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(43),i=function(e){var t=e.storageAccessor,n=e.serializer,i=e.deserializer;return{get:function(){var e=t.get();if(!e)return null;var n=function(e){try{return{result:e()}}catch(e){return e instanceof Error?{error:e}:"string"==typeof e?{error:new Error(e)}:{error:new Error("<error>")}}}((function(){return i(Object(r.a)(e))})).result;return void 0===n?null:n},set:function(e){t.set(Object(r.b)(n(e)))}}}},function(e,t,n){"use strict";n.r(t),n.d(t,"userAttributes",(function(){return l}));var r=n(64),i=n(24),o=n(12),a=n(8),s=n(38),c=n(6),u=n(17),l={id:void 0,attributes:{},init:hj.tryCatch((function(){if(hj.settings.user_attributes_enabled){var e=f();e&&(l.id=e.userId,l.attributes=e.attributes)}}),"userAttributes.init"),reset:hj.tryCatch((function(){l.id=void 0,l.attributes={},g()}),"userAttributes.reset"),set:hj.tryCatch((function(e,t){hj.settings.user_attributes_enabled?(void 0!==l.id&&null!==l.id&&l.id!==e&&(hj.log.debug("User ID changed, resetting all attributes before continuing.","userAttributes"),l.reset()),l.id=e,l.attributes=Object(u.f)(l.attributes,t),p({attributes:l.attributes,userId:l.id}),hj.event.signal("user-attributes-set"),o.a.isRecordingEnabled()?l.flush():hj.log.debug("No recording in progress. Not sending.","userAttributes")):hj.log.debug("User attributes not enabled. Doing nothing.","userAttributes")}),"userAttributes.set"),flush:hj.tryCatch((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:hj.hq.noop;if(void 0!==l.id||Object.keys(l.attributes).length){var t=c.a.items.USER_ATTRIBUTES_HASH.get({resetExpiry:!0}),n=hj.md5(JSON.stringify({allAttributes:l.attributes,userId:l.id}));if(t===n)return e(null,l.id,l.attributes),void hj.log.debug("No changed user attributes. Not sending.","userAttributes");c.a.items.USER_ATTRIBUTES_HASH.set(n),hj.debug.isOn()&&h(l.id,l.attributes),d(l.id,l.attributes),e(null,l.id,l.attributes)}else e(Error("no_user"))}),"userAttributes.flush"),get:hj.tryCatch((function(e){return"user_id"===e?l.id:l.attributes[e]}),"userAttributes.get")},h=function(e,t){hj.ajax.post("".concat(hj.identifyEndpoint,"/sites/").concat(hj.settings.site_id,"/users/").concat(Object(s.a)().id,"/validate"),{user_id:e,attributes:t},hj.tryCatch((function(e){e.errors&&Object.keys(e.errors).length>0?hj.log.debug("User validation API call PARTIALLY successful (some errors).","userAttributes",e):hj.log.debug("User validation API call successful.","userAttributes",e)}),"userAttributes"),hj.tryCatch((function(e){hj.log.debug("User Attributes validation API call failed.","userAttributes",e.responseText&&JSON.parse(e.responseText)||"unknown_failure")}),"userAttributes"))},d=function(e,t){if(o.a.isRecordingEnabled()){var n={hj_id:Object(s.a)().id,user_id:e,attributes:t,time:hj.time.getNow(),timestamp:a.a.now()};Object(i.a)(r.a.IDENTIFY_USER,n,!0).flush(),hj.log.debug("User attributes sent up websocket.","userAttributes",n)}},p=function(e){var t=hj.b64EncodeUnicode(JSON.stringify(e));c.a.items.CACHED_USER_ATTRIBUTES.set(t,!0)},f=function(){var e=c.a.items.CACHED_USER_ATTRIBUTES.get();if(e)try{var t=hj.isParsableJSON(e)?e:hj.b64DecodeUnicode(e);return JSON.parse(t)}catch(e){return}},g=function(){c.a.items.CACHED_USER_ATTRIBUTES.clear()}},,,function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var r=n(64),i=n(12),o=n(24),a=n(112),s=n(40),c=n(138),u=n(68),l=n(8),h=n(17),d=n(6);var p=!1,f=Object(h.g)((function(e,t,n,i,s){var u,l,h;Object(o.a)((u={},l=r.a.RECORDING_HELO,h=function(){hj.metrics.timeEnd("time-to-first-event");var r=hj.visitData.getPageVisitInfo(e,t,hj.settings.site_id);return Object(c.b)(r,n,i)},l in u?Object.defineProperty(u,l,{value:h,enumerable:!0,configurable:!0,writable:!0}):u[l]=h,u)).flush(),hj.eventStream.reportPageContent(e.key),s?a.a.enableRecording():e.ready("pageInfo",(function(e){g.initializeTreeMirror(e),a.a.enableRecording(d.a.items.SESSION_RESUMED.get())}))})),g={autoTagsToProcess:[],tagsToProcess:[],active:!1,isTreeMirrorInitialized:function(){return p},start:hj.tryCatch((function(){i.a.setRecordingEnabled(!0),sessionStorage.setItem("_hjRecordingEnabled",!0),sessionStorage.setItem("_hjRecordingLastActivity",l.a.now()),hj.visitData.lazyTrack.execute();var e=hj.visitData.property,t=hj.ui.getWindowSize(),n=hj.time.getNow(),a=l.a.now();f(e.key,e,t,n,a,this.isTreeMirrorInitialized()),g.active=!0,g.tagsToProcess.length&&(Object(o.a)(r.a.TAG_RECORDING,g.tagsToProcess,!0).flush(),g.tagsToProcess=[]),g.autoTagsToProcess.length&&(Object(o.a)(r.a.AUTOTAG_RECORDING,g.autoTagsToProcess,!0).flush(),g.autoTagsToProcess=[]),hj.settings.user_attributes_enabled&&u.userAttributes.flush()}),"behavior-data.recording.start"),setAndSendPageContent:function(e,t){var n=e.get("pageInfo").urlMD5;hj.globals.ready("userId",(function(){return hj.eventStream.storePageContent(t,n)}))},reset:function(){hj.globals.set("pageInfo",void 0),hj.globals.set("userId",void 0),hj.eventStream.clearPageContent(),hj.treeMirror.resetMutationListeners(),p=!1},initializeTreeMirror:hj.tryCatch((function(e){hj.treeMirror.mutationObserverAvailable&&(hj.metrics.timeStart("task-execution-time",{task:"dom-serialization"}),hj.treeMirror.getTree((function(t,n){m(e,t,n)}),v))}),"behavior-data.initializeTreeMirror"),resume:function(){s.j.update(),s.f.trackCoordinatesOnNextClick()}},m=function(e,t,n){g.setAndSendPageContent(e,JSON.stringify({docType:hj.html.getPageDoctype(),rootId:t,children:n})),p=!0,hj.metrics.timeEnd("task-execution-time",{task:"dom-serialization"})},v=function(e,t,n,i){var a={};(e.length||t.length||n.length||i.length)&&(a.time=hj.time.getNow(),a.timestamp=l.a.now(),e.length&&(a.a=e),t.length&&(a.b=t),n.length&&(a.c=n),i.length&&(a.d=i),hj.debug.emit("mutation",a),Object(o.a)(r.a.MUTATION,a,!1))}},,,,,,,,,,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(56),i=n(4),o=!1,a=function(e){var t=e.title,n=e.message,a=e.status;o?hj.widget.renderNotificationWidget({title:t,message:n,status:a}):Object(r.a)(i.f.NOTIFICATION,(function(){o=!0,hj.widget.renderNotificationWidget({title:t,message:n,status:a})}))}},,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";function r(e,t,n){t=t||hj.hq.noop,n=n||hj.hq.noop,hj.hq.ajax({url:e,success:hj.tryCatch(t,"Data"),error:hj.tryCatch(n,"Data")})}function i(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"json";return n=n||hj.hq.noop,r=r||hj.hq.noop,hj.hq.ajax({url:e,type:"POST",data:"json"===i?hj.hq.stringify(t):t,contentType:"text/plain; charset=UTF-8",success:hj.tryCatch(n,"Data"),error:hj.tryCatch(r,"Data")})}function o(e,t,n,r){n=n||hj.hq.noop,r=r||hj.hq.noop,hj.hq.ajax({url:e,type:"PUT",data:hj.hq.stringify(t),contentType:"text/plain; charset=UTF-8",success:hj.tryCatch(n,"Data"),error:hj.tryCatch(r,"Data")})}n.r(t),n.d(t,"get",(function(){return r})),n.d(t,"post",(function(){return i})),n.d(t,"putAsJSON",(function(){return o})),hj.ajax={get:r,post:i,putAsJSON:o}},function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var r=n(12),i=n(40),o=Object(i.h)("copy"),a=Object(i.h)("cut"),s=Object(i.h)("paste"),c=[i.j,i.e,i.g,i.f,i.l,i.m,i.c,i.d,o,a,i.i,s,i.b,i.a],u=[i.g,i.f,i.k,i.c],l={enableRecording:hj.tryCatch((function(e){r.a.setRecordingEnabled(!0),c.forEach((function(t){t.listen(e)})),hj.autotag.start()}),"behavior-data.events.enableRecording"),enableHeatmap:hj.tryCatch((function(e){r.a.setHeatmapEnabled(!0),u.forEach((function(e){e.listen()})),r.a.setSelectorVersion(e)}),"behavior-data.events.enableHeatmap"),disableHeatmap:hj.tryCatch((function(){r.a.setHeatmapEnabled(!1)}),"behavior-data.events.disableHeatmap")}},function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(38),i={getAsNumber:function(){var e=Object(r.a)().id;return(parseInt(e.slice(-10),16)+1)/Math.pow(2,40)},compareRatio:hj.tryCatch((function(e,t){return i.getAsNumber()*(t?100:1)<=e}),"identifier.compareRatio")}},,,,,function(e,t,n){"use strict";n.r(t),n.d(t,"create",(function(){return o}));var r=n(26);function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var o=hj.tryCatch((function(){var e={key:Object(r.a)()},t={},n={};return e.ready=hj.tryCatch((function(r,i){i=hj.tryCatch(i,"Data"),n[r]?(hj.log.debug("Property "+r+" is ready. Calling callback() now.","property",i),i(e)):(hj.log.debug("Property "+r+" is not ready. Saving callback().","property",i),t[r]?t[r].push({callback:i,executed:!1}):t[r]=[{callback:i,executed:!1}])}),"data"),e.set=hj.tryCatch((function(r,o){hj.log.debug("Setting properties."+r+" to "+o,"property"),n[r]=o,"object"===i(t[r])&&hj.hq.each(t[r],(function(t,n){n.executed||(hj.log.debug("Calling "+r+" callback.","property"),n.callback(e),n.executed=!0)}))}),"data"),e.get=hj.tryCatch((function(e){return n[e]}),"data"),e}),"data");hj.property=hj.tryCatch((function(){var e={};return e.create=o,hj.globals=e.create(),e}),"data")()},,function(e,t,n){"use strict";n.r(t),n.d(t,"Resource",(function(){return a}));var r,i=n(111),o=new RegExp("<.+:(.+)>");function a(e,t,n){var r={},i=function(){for(var t=e.split("/"),n=[],r=0;r<t.length;r+=1){var i=o.exec(t[r]);i?n.push({label:i[1],isDynamic:!0}):n.push({label:t[r],isDynamic:!1})}return n}();return function(){for(var e in t)if(Object.prototype.hasOwnProperty.call(t,e)){var o=t[e][0],a=t[e][1];r[e]=s({urlPathTokens:i,method:o,requiredArgs:a,getHost:n})}}(),r}function s(e){var t=e.urlPathTokens,n=e.method,r=e.requiredArgs,o=e.getHost;function a(e){if(!r)return e;for(var t={},n=0;n<r.length;n+=1)if(t[r[n]]=e[r[n]],void 0===t[r[n]])throw new Error('Argument "'+r[n]+'" is missing.');return t}return function(e,r,s,c){var u,l="https://".concat(null!==(u=null==o?void 0:o())&&void 0!==u?u:hj.host).concat(function(e,t){for(var n=[],r=0;r<e.length;r+=1){var i=e[r];if(i.isDynamic){var o=t[i.label];if(void 0===o)throw new Error('Argument "'+i.label+'" is missing.');n.push(o)}else n.push(i.label)}return n.join("/")}(t,e));switch(n){case"GET":i.get(l,r,s);break;case"POST":i.post(l,a(e),r,s,c);break;case"PUT":i.putAsJSON(l,a(e),r,s);break;default:throw new Error('HTTP method "'+n+'" is not supported.')}}}hj.resource=((r={}).TokenRestrictedStorage=a("/api/v1/trs/<string:token>",{put:["PUT",["token","data"]]}),r)},,,,,,function(e,t,n){"use strict";n.r(t);var r=n(83),i=n(26),o=n(43),a=n(6),s=n(21),c=n(68),u=n(17);function l(e){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}hj.tryCatch((function(){if(void 0!==hj.scriptLoaded)return window.console=window.console||{warn:function(){}},console.warn("Hotjar Tracking Warning: Multiple Hotjar tracking codes were detected on this page. Tracking will not work as expected."),hj.verifyInstall?void Object(r.a)("Hotjar installation invalid.","It appears you have more than one Hotjar tracking code set up on this page. Hotjar cannot work properly if multiple Hotjar scripts are loaded concurrently. Please make sure you only install the one tracking code provided for this site.","bad"):void 0;var e,t,n;function h(e,t){var n=p(n=e[0],o=e[1],i=e[2],r=e[3],t[0],7,-680876936),r=p(r,n,o,i,t[1],12,-389564586),i=p(i,r,n,o,t[2],17,606105819),o=p(o,i,r,n,t[3],22,-1044525330);n=p(n,o,i,r,t[4],7,-176418897),r=p(r,n,o,i,t[5],12,1200080426),i=p(i,r,n,o,t[6],17,-1473231341),o=p(o,i,r,n,t[7],22,-45705983),n=p(n,o,i,r,t[8],7,1770035416),r=p(r,n,o,i,t[9],12,-1958414417),i=p(i,r,n,o,t[10],17,-42063),o=p(o,i,r,n,t[11],22,-1990404162),n=p(n,o,i,r,t[12],7,1804603682),r=p(r,n,o,i,t[13],12,-40341101),i=p(i,r,n,o,t[14],17,-1502002290),n=f(n,o=p(o,i,r,n,t[15],22,1236535329),i,r,t[1],5,-165796510),r=f(r,n,o,i,t[6],9,-1069501632),i=f(i,r,n,o,t[11],14,643717713),o=f(o,i,r,n,t[0],20,-373897302),n=f(n,o,i,r,t[5],5,-701558691),r=f(r,n,o,i,t[10],9,38016083),i=f(i,r,n,o,t[15],14,-660478335),o=f(o,i,r,n,t[4],20,-405537848),n=f(n,o,i,r,t[9],5,568446438),r=f(r,n,o,i,t[14],9,-1019803690),i=f(i,r,n,o,t[3],14,-187363961),o=f(o,i,r,n,t[8],20,1163531501),n=f(n,o,i,r,t[13],5,-1444681467),r=f(r,n,o,i,t[2],9,-51403784),i=f(i,r,n,o,t[7],14,1735328473),n=g(n,o=f(o,i,r,n,t[12],20,-1926607734),i,r,t[5],4,-378558),r=g(r,n,o,i,t[8],11,-2022574463),i=g(i,r,n,o,t[11],16,1839030562),o=g(o,i,r,n,t[14],23,-35309556),n=g(n,o,i,r,t[1],4,-1530992060),r=g(r,n,o,i,t[4],11,1272893353),i=g(i,r,n,o,t[7],16,-155497632),o=g(o,i,r,n,t[10],23,-1094730640),n=g(n,o,i,r,t[13],4,681279174),r=g(r,n,o,i,t[0],11,-358537222),i=g(i,r,n,o,t[3],16,-722521979),o=g(o,i,r,n,t[6],23,76029189),n=g(n,o,i,r,t[9],4,-640364487),r=g(r,n,o,i,t[12],11,-421815835),i=g(i,r,n,o,t[15],16,530742520),n=m(n,o=g(o,i,r,n,t[2],23,-995338651),i,r,t[0],6,-198630844),r=m(r,n,o,i,t[7],10,1126891415),i=m(i,r,n,o,t[14],15,-1416354905),o=m(o,i,r,n,t[5],21,-57434055),n=m(n,o,i,r,t[12],6,1700485571),r=m(r,n,o,i,t[3],10,-1894986606),i=m(i,r,n,o,t[10],15,-1051523),o=m(o,i,r,n,t[1],21,-2054922799),n=m(n,o,i,r,t[8],6,1873313359),r=m(r,n,o,i,t[15],10,-30611744),i=m(i,r,n,o,t[6],15,-1560198380),o=m(o,i,r,n,t[13],21,1309151649),n=m(n,o,i,r,t[4],6,-145523070),r=m(r,n,o,i,t[11],10,-1120210379),i=m(i,r,n,o,t[2],15,718787259),o=m(o,i,r,n,t[9],21,-343485551);e[0]=j(n,e[0]),e[1]=j(o,e[1]),e[2]=j(i,e[2]),e[3]=j(r,e[3])}function d(e,t,n,r,i,o){return t=j(j(t,e),j(r,o)),j(t<<i|t>>>32-i,n)}function p(e,t,n,r,i,o,a){return d(t&n|~t&r,e,t,i,o,a)}function f(e,t,n,r,i,o,a){return d(t&r|n&~r,e,t,i,o,a)}function g(e,t,n,r,i,o,a){return d(t^n^r,e,t,i,o,a)}function m(e,t,n,r,i,o,a){return d(n^(t|~r),e,t,i,o,a)}function v(e){var t,n=[];for(t=0;64>t;t+=4)n[t>>2]=e.charCodeAt(t)+(e.charCodeAt(t+1)<<8)+(e.charCodeAt(t+2)<<16)+(e.charCodeAt(t+3)<<24);return n}window.hj=window.hj||function(){(window.hj.q=window.hj.q||[]).push(arguments)},window.hj.q=window.hj.q||[],hj.hostname=hj.host.split(":")[0],hj.port=443,hj.apiUrlBase="https://"+hj.host+"/api/v1",hj.apiUrlBaseV2="https://"+hj.host+"/api/v2",hj.isPreview=Boolean(_hjSettings.preview),hj.userDeviceType=null,hj.optOut=!1,hj.windowSize=null,hj.maxRecordingTagLength=250,hj.settings=hj.isPreview?{}:window.hjSiteSettings,hj.locationListener=(n="manual",(t={}).setMode=hj.tryCatch((function(t){n=t,e&&clearInterval(e),"automatic_with_fragments"===n?e=setInterval((function(){var e=""===location.hash&&location.href.indexOf("#")>-1?"#":location.hash,t="".concat(location.origin).concat(location.pathname).concat(location.search).concat(e);hj.currentUrl&&hj.currentUrl!=t&&hj._init.reinit(t)}),200):"automatic"===n&&(e=setInterval((function(){var e="".concat(location.origin).concat(location.pathname).concat(location.search);hj.currentUrl&&hj.currentUrl.split("#")[0]!=e&&hj._init.reinit(e)}),200))})),t);var y="0123456789abcdef".split("");function b(e){for(var t="",n=0;4>n;n++)t+=y[e>>8*n+4&15]+y[e>>8*n&15];return t}function j(e,t){return e+t&4294967295}hj.encodeAsUtf8=o.d,hj.decodeAsUtf8=o.c,hj.md5=function(e,t){var n="";try{n=function(e){for(var t=0;t<e.length;t++)e[t]=b(e[t]);return e.join("")}(function(e){var t,n=e.length,r=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=e.length;t+=64)h(r,v(e.substring(t-64,t)));e=e.substring(t-64);var i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<e.length;t++)i[t>>2]|=e.charCodeAt(t)<<(t%4<<3);if(i[t>>2]|=128<<(t%4<<3),55<t)for(h(r,i),t=0;16>t;t++)i[t]=0;return i[14]=8*n,h(r,i),r}(hj.encodeAsUtf8(e)))}catch(e){t?n="":hj.exceptions.log(e,"md5")}return n},hj.b64EncodeUnicode=o.b,hj.b64DecodeUnicode=o.a,hj.isParsableJSON=function(e){try{var t=JSON.parse(e);if(t&&"object"===l(t))return!0}catch(e){}return!1},"5d41402abc4b2a76b9719d911017c592"!=hj.md5("hello")&&(j=function(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}),hj.time=function(){var e=(new Date).getTime(),t={reset:function(){e=(new Date).getTime()},getNow:function(){return(new Date).getTime()-e}};return t}(),hj.debug=function(){var e={isOn:function(){return _hjSettings.hjdebug},on:function(e){_hjSettings.hjdebug=!0,e&&a.a.items.DEBUG_FLAG.set(!0)},off:function(){_hjSettings.hjdebug=!1,a.a.items.DEBUG_FLAG.clear()},emit:function(e,t){"undefined"!=typeof _hjEmitters&&_hjEmitters.includes&&_hjEmitters.includes(e)&&window.postMessage({data:t,message:a.a.items.DEBUG_FLAG.getKey(),type:e},"*")}};return e}(),hj.url=s,hj.log=function(){var e=!1,t="",n={},r={autotag:"#ff0099",command:"#0079a4",data:"#009bd2",dataqueue:"#00445c",deferredpagecontent:"#7c7c7c",event:"#ff7000",events:"#ffc000",exception:"#e63946",heatmap:"#700000",init:"#6600cc",integration:"#2a9072",poll:"#00a000",property:"#ff33cc",recording:"#dd0000",rendering:"#bd00ea",sampling:"#efb0a1",survey:"#007000",targeting:"#00ee00",visitdata:"#00668a",websocket:"#0dc0ff"};return n.init=function(){void 0===window.console&&(window.console={debug:function(){},trace:function(){},log:function(){},info:function(){},warn:function(){},error:function(){}})},n.debug=function(i,o,a){var s=o&&r[o.toLowerCase()]||"#2a9d8f";t!=i&&e&&(console.groupEnd(),e=!1),t=i,hj.debug.isOn()&&("object"===l(i)?hj.hq.each(i,(function(e,t){n.debug(e+": "+t,o,null)})):(i=o&&"string"==typeof i?o.toUpperCase()+": "+i:i,i="%c"+(new Date).toTimeString().replace(/.*(\d{2}:\d{2}:\d{2}).*/,"$1")+":%c "+i,a?(e||(console.groupCollapsed(i+":","color: #999;","color: "+s+";"),e=!0),console.log(a)):console.log(i,"color: #999;","color: "+s+";")))},n.info=function(e){console.log("%c"+e,"color: #006EFF")},n.warn=function(e){console.log("%c"+e,"color: #E8910C")},n.error=function(e){console.error("Hotjar error: "+e)},n.warnIfEmpty=function(e,t){if(Array.isArray(e)?0===e.length:hj.hq.isNullOrUndefined(e)){var n=null===e?"null":void 0===e?"undefined":"no value";hj.log.debug("WARNING: [".concat(t,"] a value was expected but ").concat(n," was found!"))}},n}(),hj.loader=function(){var e={},t=[];return e.getModules=hj.tryCatch((function(){return t}),"common"),e.registerModule=hj.tryCatch((function(e,n,r){t.push({name:e,module:n,nonTracking:void 0!==r&&r})}),"common"),e}(),hj.targeting=function(){var e={};function t(t,n){var r;if(!e.matchOperations[t.match_operation])return hj.exceptions.log({message:'Targeting error - "'+t.match_operation+'" match operation does not exist.',stacktrace:""},"common.targeting.matchPatternWithRule"),!1;t.rule_type=t.rule_type||t.component;var i=function(e,t){return"date"!==e.rule_type||"less_than"!==e.match_operation&&"greater_than"!==e.match_operation?t:(new Date(t).valueOf()/1e3).toFixed(0)}(t,n);return(r=e.matchOperations[t.match_operation](t,i)).error?(hj.exceptions.log({message:'Targeting error - "'+t.match_operation+" - "+r.error,stacktrace:""},"common.targeting.matchPatternWithRule"),!1):("url"!==t.component&&t.negate&&(r=!r),function(e,t,n){var r=n?"Match ":"No Match ";!n||"url"!==e.component&&"device"!==e.component||(r+=e.component+"|"+e.match_operation+"|"+e.pattern+(e.negate?" [NEGATE]":""),hj.log.debug(r,"targeting"));"attribute"!==e.component&&"event"!==e.component||(r+=e.component+"|"+e.name+" ("+e.rule_type+")|"+e.match_operation+"|"+e.pattern+"|compared with: "+t+(e.negate?" [NEGATE]":""),hj.log.debug(r,"targeting"))}(t,n,r),r)}function n(e){var n=!1,r=Object(u.a)();return(n=0===e.length||3===e.length||e.some((function(e){return t(e,r)})))?hj.log.debug("Device match found","targeting"):hj.log.debug("No device match found","targeting"),n}function r(e,n){var r,i=!1,o=!1,a=!1;if(0===e.length)return hj.log.debug("No URL targeting rules set","targeting"),!1;for(var s=0;s<e.length;s+=1)if((i=t(r=e[s],n))&&!r.negate&&(a=!0),i&&r.negate){o=!0;break}return(i=a&&!o)?hj.log.debug("URL match found without any negate rules","targeting"):o?hj.log.debug("Negate URL rule match found","targeting"):hj.log.debug("No URL match found","targeting"),i}var i=function(e){return e.toLowerCase().split("#")[0].split("?")[0].replace("http://www.","").replace("https://www.","").replace("http://","").replace("https://","").replace(/\/$/,"")};var o={};e.matchRules=hj.tryCatch((function(e,i,a){for(var s={device:{rules:[],isMatch:null,doMatch:n},url:{rules:[],isMatch:null,doMatch:r},attribute:{rules:[]},trigger:{rules:[]}},u=0;u<e.length;u+=1)s[e[u].component]?s[e[u].component].rules.push(e[u]):hj.exceptions.log({message:'Targeting error - "'+s[e[u].component]+'" targeting component is not supported.',stacktrace:""},"common.targeting.matchRules");hj.log.debug("-- Matching rules for new item --");var l=function(e,t){"__proto__"!==i&&"constructor"!==i&&(void 0===o[i]&&(o[i]=[]),o[i].push({eventName:e,callback:t}),hj.event.listen(e,t))};if(Object.keys(o).forEach((function(e){e!==i&&(o[e].forEach((function(e){var t=e.eventName,n=e.callback;hj.event.removeListener(t,n)})),delete o[e])})),!s.device.doMatch(s.device.rules))return!1;var h=function(){return function(e){var n,r=e.every((function(e){return t(e,c.userAttributes.get(e.name))}));return n=r?0===e.length?"No specific user attribute targeting rules set.":"All user attributes matched.":"User attributes set do not match current visitor.",hj.log.debug(n,"targeting"),r}(s.attribute.rules)},d=s.trigger.rules;if(d.length){hj.log.debug("Setting up targeting listeners for trigger rules","targeting");var p=function(){h()&&(null==a||a(!0))};d.forEach((function(e){var t="trigger:"+e.pattern;l(t,p)}))}var f=s.url.doMatch(s.url.rules,i);if(0===s.attribute.rules.length)return!!f&&(null==a||a(),!0);hj.log.debug("Setting up targeting listeners for attribute rules","targeting");return l("user-attributes-set",(function(){f&&h()&&(null==a||a(!0))})),!1}),"common.targeting.matchRules");var a=function(e,t,n){return function(){return e.apply(null,arguments)?t.apply(null,arguments):n.apply(null,arguments)}},s=function(e,t){return void 0!==e&&void 0!==t&&null!==e&&null!==t},l=function(e,t){return!isNaN(e.pattern)&&!isNaN(t)&&""!==t&&"boolean"!=typeof t},h=function(e,t){if(!t||t.toString()===parseInt(t,10).toString())return!1;var n=new Date(t);return"Invalid Date"!==n&&!isNaN(n)},d=function(e){return a(s,e,(function(){return!1}))},p=function(e){return d(a(l,e,(function(e,t){return{error:"Cannot compare non-numeric values (rule: { name: '".concat(e.name,"' },\n                    pattern: '").concat(t,"').")}})))},f=function(e){return d(a(h,e,(function(e,t){return{error:"Cannot compare dates. Given pattern is not a date (rule: { name: '".concat(e.name,"' }, pattern: '").concat(t,"').")}})))};return e.matchOperations={exact:function(e,t){return"string"===e.rule_type?d((function(e,t){return t.toLowerCase()===e.pattern.toLowerCase()}))(e,t):"boolean"===e.rule_type?(e.pattern="true"===String(e.pattern),t===e.pattern):"number"===e.rule_type?null!=t&&Number(t)===Number(e.pattern):t===e.pattern},starts_with:d((function(e,t){return 0===(t=t||"").indexOf(e.pattern)})),ends_with:d((function(e,t){var n=t.length-e.pattern.length==-1?0:t.length-e.pattern.length;return t.substring(n,t.length)===e.pattern})),contains:d((function(e,t){return-1!==(t=t||"").indexOf(e.pattern)})),regex:d((function(e,t){try{return new RegExp(e.pattern).test(t)}catch(e){return hj.log.error("The regular expression used for page targeting was invalid. Please provide a valid regular\n                        expression.\n\nRead more here: https://help.hotjar.com/hc/en-us/articles/115012727628"),!1}})),simple:d((function(e,t){return i(t)===i(e.pattern)})),greater_than:p((function(e,t){return Number(t)>Number(e.pattern)})),less_than:p((function(e,t){return Number(t)<Number(e.pattern)})),unknown:function(e,t){return 0!==(t="string"==typeof t?t.trim():t)&&!1!==t&&!t},exact_date:f((function(e,t){var n=new Date(1e3*e.pattern);return(t=new Date(t)).toDateString()===n.toDateString()})),exact_days_ago:f((function(e,t){var n,r=new Date;return n=r.setDate(r.getDate()-Number(e.pattern)),n=new Date(n),(t=new Date(t)).toDateString()===n.toDateString()})),more_than_days_ago:f((function(e,t){var n=Number(e.pattern)+1,r=(new Date).getTime()/1e3;return r-=86400*n,new Date(t).getTime()/1e3<=r})),less_than_days_ago:f((function(t,n){if(0===Number(t.pattern))return e.matchOperations.exact_days_ago(t,n);var r=(new Date).getTime()/1e3;return r-=86400*Number(t.pattern),new Date(n).getTime()/1e3>=r}))},e}(),hj.rendering=function(){var e={},t=[];function n(e,t){hj.tryCatch(t,"Rendering")(e)}function r(e,t){hj.widgetDelay.set((function(){hj.tryCatch(t,"Rendering")(e)}),1e3*e.display_delay)}function i(e,n,r){var i=hj.hq(document),o=hj.hq(window),a=[];function s(){hj.tryCatch(n,"Rendering")(e),i.off("mousemove."+r),i.off("mouseout."+r)}t.push(r),i.off("mousemove."+r),i.off("mouseout."+r),i.on("mousemove."+r,hj.tryCatch((function(e){a.push({x:e.clientX,y:e.clientY}),a.length>2&&(a[1].x===a[2].x&&a[1].y===a[2].y?a.pop():a.shift())}),"Rendering")),i.on("mouseout."+r,hj.tryCatch((function(e){e.relatedTarget&&(e.relatedTarget===this||this.compareDocumentPosition(e.relatedTarget)&Node.DOCUMENT_POSITION_CONTAINED_BY)||function(e){var t=a[1],n=a[0];if(e|=0,void 0!==t&&!(t.y>=n.y||e>0)){n.x===t.x&&s();var r=(n.y-t.y)/(n.x-t.x),i=-(n.y-r*n.x)/r;i>0&&i<o.width()&&s()}}(e.clientY)}),"Rendering"))}function o(e,t,n){var r=hj.hq(document),i=hj.hq(window);i.on("scroll."+n,hj.tryCatch((function(){var o=r.height();(r.scrollTop()+hj.ui.getWindowSize().height)/o>=.5&&(i.off("scroll."+n),t(e))}),"Rendering"))}return e.clearAllAbandonEvents=hj.tryCatch((function(){t.forEach((function(e){hj.log.debug("Removing abandon events for "+e,"rendering"),hj.hq(document).off("mousemove."+e),hj.hq(document).off("mouseout."+e)})),t=[]}),"common"),e.addToDom=hj.tryCatch((function(e,t){if(!e)throw Error("container id not defined");if(!t)throw Error("cannot append html to container #"+e+", html not defined.");return hj.hq("#"+e).remove(),hj.hq("body").append(t),hj.hq("#"+e+">div")}),"common.addToDom"),e.TrustedString=function(e){this.value=e},e.callAccordingToCondition=hj.tryCatch((function(e,t,a){var s={immediate:n,delay:r,abandon:i,scroll:o}[e.display_condition];if(hj.log.debug("Calling active item ("+t+"): "+e.display_condition,"rendering"),!s)throw new Error('Unhandled display condition: "'+e.display_condition+'"');hj.tryCatch(s,"Rendering")(e,a,t)}),"common"),e}(),hj.survey=hj.tryCatch((function(){var e={ctrl:void 0,data:void 0,model:{},activeLanguageDirection:"ltr"};return e}),"common")(),hj.ui=function(){var e={};return e.getWindowSize=hj.tryCatch((function(){var e=function(){try{return window.self!==window.top}catch(e){return!0}}(),t={width:!e&&window.screen?window.screen.width:document.body.clientWidth,height:!e&&window.screen?window.screen.height:document.body.clientHeight};return{width:window.innerWidth||document.documentElement.clientWidth||t.width,height:window.innerHeight||document.documentElement.clientHeight||t.height}}),"common"),e.getDocumentSize=hj.tryCatch((function(){var t,n;if(document&&document.documentElement&&document.documentElement.clientWidth)t=document.documentElement.clientWidth,n=document.documentElement.clientHeight;else{var r=e.getWindowSize();t=r.width,n=r.height}return{width:t,height:n}}),"common"),e.getScrollPosition=hj.tryCatch((function(e){return e=e||window,{left:hj.hq(e).scrollLeft(),top:hj.hq(e).scrollTop()}}),"common"),e.getBottomAsPercentage=hj.tryCatch((function(){var e=parseInt(1e3*(hj.hq(window).scrollTop()+hj.ui.getWindowSize().height)/hj.hq(document).height(),10);return Math.min(1e3,e)}),"common"),e.disableScrolling=hj.tryCatch((function(e){var t=hj.ui.getScrollPosition();hj.hq(document).on("scroll.hotjarDisable resize.hotjarDisable mousewheel.hotjarDisable DOMMouseScroll.hotjarDisable touchmove.hotjarDisable",hj.tryCatch((function(n){n.preventDefault(),window.scrollTo(t.left,t.top),e&&e()}),"common"))}),"common"),e.enableScrolling=hj.tryCatch((function(){hj.hq(document).off("scroll.hotjarDisable"),hj.hq(document).off("resize.hotjarDisable"),hj.hq(document).off("mousewheel.hotjarDisable"),hj.hq(document).off("DOMMouseScroll.hotjarDisable"),hj.hq(document).off("touchmove.hotjarDisable")}),"common"),e}(),hj.dom=function(){var e={getCSSURLs:function(){var e=[],t=document.styleSheets;return hj.log.debug("Getting CSS","dpc","Starting"),t&&t.length>0&&hj.hq.each(t,(function(t,n){n.href&&0===n.href.indexOf("http")&&(hj.log.debug("Getting CSS","dpc",n.href),e.push(n.href))})),e}};return e}(),hj.html=function(){var e={};return e.getPageContent=hj.tryCatch((function(e,t){function n(e,t){t=t||document;var n=parseInt(e.split(":")[0]),r=e.substring(e.indexOf(":")+1);return t.querySelectorAll(r)[n]}t=t||[],hj.treeMirror.getMirroredDocument((function(r){var i=hj.insertedRules.getCurrentInsertedRules(),o={};i.forEach((function(e){var i=n(e.parentSelector,r);if(o[e.parentSelector]||(o[e.parentSelector]=""),"LINK"===i.tagName){var a=i.attributes,s=a.href.value.split(location.hostname).pop().replace("/",""),c=r.querySelector(".blob-hash-"+s);if(!c){c=document.createElement("style"),o[e.parentSelector]=o[e.parentSelector]+"/* Originally: <link ";for(var u=0,l=a.length;u<l;u++)"id"!==a[u].name&&"class"!==a[u].name||c.setAttribute(a[u].name,a[u].value),o[e.parentSelector]+=a[u].name+'="'+a[u].value+'" ';o[e.parentSelector]+="> */",c.classList.add("blob-hash-"+s),i.parentNode.insertBefore(c,i.nextSibling)}i=c}o[e.parentSelector]=o[e.parentSelector]+"\n"+e.rule,-1===t.indexOf('link[href*="blob:"]')&&t.push('link[href*="blob:"]')})),Object.keys(o).forEach((function(e){n(e,r).textContent=o[e]})),function(e,t,n){var r,i=e.querySelectorAll("head iframe"),o=0==document.getElementsByTagName("base").length?location.origin:document.getElementsByTagName("base")[0].href;[].forEach.call(i,(function(e){e.parentNode.removeChild(e)})),n&&n.forEach((function(t){r=e.querySelectorAll(t),hj.hq.each(r,(function(e,t){t.parentNode.removeChild(t)}))})),Array.prototype.slice.call(e.getElementsByTagName("img")).forEach((function(e){if(e.srcset){if(""===e.src){var t=e.srcset.match(/(?:([^"'\s,]+)(\s*(?:\s+\d+[wx]))?(?:,\s*)?)/g)[0];if(t){var n=t.indexOf(" ");n>0&&(t=t.substring(0,n)),0!==(t=t.replace(",","")).indexOf("http")&&(t=o+t),e.src=t}}e.removeAttribute("srcset")}})),Array.prototype.slice.call(e.getElementsByTagName("source")).forEach((function(e){e.attributes.srcset&&e.removeAttribute("srcset")})),[].forEach.call(e.querySelectorAll("script"),(function(e){e.parentNode.removeChild(e)})),t(hj.html.getPageDoctype()+e.documentElement.outerHTML)}(r,e,t)}))}),"common"),e.getPageDoctype=hj.tryCatch((function(){return null===document.doctype?"":"<!DOCTYPE "+(document.doctype.name||"html")+(document.doctype.publicId?' PUBLIC "'+document.doctype.publicId+'"':"")+(!document.doctype.publicId&&document.doctype.systemId?" SYSTEM":"")+(document.doctype.systemId?' "'+document.doctype.systemId+'"':"")+">\n"}),"common"),e}(),hj.features=function(){var e={};return e.getFeatures=hj.tryCatch((function(){return hj.settings.features||[]}),"hj.features.getFeatures"),e.hasFeature=hj.tryCatch((function(t){var n;try{var r=window.localStorage.getItem("HJ_OVERRIDE_FEATURE:".concat(t));n="true"===r||"1"===r}catch(e){n=!1}return e.getFeatures().indexOf(t)>-1||n}),"hj.features.hasFeature"),e}(),hj.cssBlobs=function(){var e={},t=[];return e.register=function(e){t.push(e)},e.handleBlobStyles=hj.tryCatch((function(e){var n,r=[];if("link"===e.tagName.toLowerCase()&&"rel"in e.attributes&&"stylesheet"===e.attributes.rel.value&&"href"in e.attributes&&0===e.attributes.href.value.indexOf("blob:")){n=hj.selector().get(hj.hq(e));for(var i=0,o=e.sheet.cssRules.length;i<o;i++)r.push({parentSelector:n,rule:e.sheet.cssRules[i].cssText,index:i});t.forEach((function(e){e(r)}))}}),"hj.cssBlobs.apply"),e}(),hj.viewport=hj.tryCatch((function(){var e=null,t={};return t.getId=hj.tryCatch((function(){return null===e&&null===(e=sessionStorage.getItem("hjViewportId"))&&(e=Object(i.a)(),sessionStorage.setItem("hjViewportId",e)),e}),"hj.viewport.getId"),t}),"common")()}),"common")()},,,,,,,function(e,t,n){"use strict";n.r(t),n.d(t,"sessionEvents",(function(){return r}));var r={storage:{},set:hj.tryCatch((function(e){r.storage.events=e}),"sessionEvents.set"),get:hj.tryCatch((function(){return r.storage.events}),"sessionEvents.get")}},,,function(e,t){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}try{!function(e,t){var r=function(e){return new i(e)};r.isValidSelector=function(e){try{return hj.hq(e),!0}catch(e){return!1}},r.isEmptyObject=function(e){return!Object.keys(e).length},r.isFunction=function(e){return"function"==typeof e},r.isWindow=function(t){return t===e},r.isDocument=function(e,n){return e===(n||t)},r.noop=function(){},r.stringify=function(e){if(void 0===Array.prototype.toJSON)return JSON.stringify(e);var t=Array.prototype.toJSON;delete Array.prototype.toJSON;try{return JSON.stringify(e)}finally{Array.prototype.toJSON=t}},r.each=function(e,t){var r,i,o;if("object"===n(e)&&"[object Array]"!==Object.prototype.toString.call(e)){if((i=e[Object.keys(e)[0]])&&void 0!==i.nodeName){for(r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&"length"!==r&&!1===t(r,e[r],e))break}else for(r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&!1===t(r,e[r],e))break}else if(void 0!==e)for(o=0;o<e.length&&!1!==t(o,e[o],e);o+=1);},r.trim=function(e){return"string"==typeof e?e.replace(/^\s+|\s+$/gm,""):""},r.inArray=function(e,t){var n=t.indexOf(e);return void 0!==n&&-1!==n},r.values=function(e){return Object.keys(e).map((function(t){return e[t]}))},r.isUndefined=function(e){return void 0===e},r.isNullOrUndefined=function(e){return null===e||r.isUndefined(e)},r.indexOf=function(e,t){if("object"===n(t)){var r=t.indexOf(e);return void 0!==r?r:-1}return-1},r.ajax=function(e){var t=new XMLHttpRequest;e.type=e.type||"GET",e.timeout_ms=e.timeout_ms||15e3,e.withCredentials&&(t.withCredentials=!0),t.open(e.type,e.url,!0),t.timeout=e.timeout_ms,"POST"!==e.type&&"PUT"!==e.type||!e.contentType||t.setRequestHeader("Content-Type",e.contentType),t.onload=function(){t.status>=200&&t.status<400?r.isFunction(e.success)&&e.success(t.responseText&&JSON.parse(t.responseText),t):r.isFunction(e.error)&&e.error(t)},t.onerror=function(){r.isFunction(e.error)&&e.error(t)},t.ontimeout=function(){r.isFunction(e.timeout)&&e.timeout(t)},r.isFunction(e.requestAnnotator)&&e.requestAnnotator(t),"POST"!==e.type&&"PUT"!==e.type||!e.data?t.send():t.send(e.data)},r.get=function(e,t){var n=new XMLHttpRequest;n.open("GET",e,!0),n.timeout=15e3,n.onload=function(){n.status>=200&&n.status<400&&t&&t(n.responseText)},n.send()},r.eventHandlers={},r.selector="";var i=function(e){var i,o,a,s=window._hjDocument||t;if(r.selector=e,r.isWindow(e))return this[0]=window,this.length=1,this;if(r.isDocument(e,s))return this[0]=s,this.length=1,this;if("object"===n(e))return this[0]=e,this.length=1,this;if("string"==typeof e&&"<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3)return(i=s.createElement("div")).innerHTML=e,this[0]=i.childNodes[0],this.length=1,this;if("string"==typeof e){isNaN(e.charAt(1))||"."!==e.charAt(0)&&"#"!==e.charAt(0)||(e=e.charAt(0)+"\\3"+e.charAt(1)+" "+e.slice(2));try{o=s.querySelectorAll(e)}catch(e){return this.length=0,this}for(a=0;a<o.length;a+=1)this[a]=o[a];return this.length=o.length,this}return this};i.prototype.val=function(e){return void 0!==e&&this.length>0&&(this[0].value=e),void 0===this[0]?void 0:this[0]?this[0].value:""},i.prototype.text=function(e){return void 0===e?this[0].textContent:this[0].textContent=e},i.prototype.each=function(e,t){Array.prototype.forEach.call(this,(function(e,n,r){t(n,e,r)}))},i.prototype.append=function(e){var i;"object"===n(e)?"body"===r.selector?t.body.appendChild(e.get(0)):this[0].appendChild(e.get(0)):"body"===r.selector?((i=t.createElement("div")).innerHTML=e,t.body.appendChild(i)):((i=t.createElement("div")).innerHTML=e,this[0].appendChild(i))},i.prototype.hasClass=function(e){return this[0].classList?this[0].classList.contains(e):new RegExp("(^| )"+e+"( |$)","gi").test(this[0].className)},i.prototype.addClass=function(e){var t;for(t=0;t<this.length;t+=1)this[t].classList?this[t].classList.add(e):this[t].className+=" "+e;return this},i.prototype.removeClass=function(e){var t;for(t=0;t<this.length;t+=1)this[t].classList?this[t].classList.remove(e):this[t].className=this[t].className.replace(new RegExp("(^|\\b)"+e.split(" ").join("|")+"(\\b|$)","gi")," ");return this},i.prototype.toggleClass=function(e){var t;for(t=0;t<this.length;t+=1)this[t].classList?this[t].classList.contains(e)?this[t].classList.remove(e):this[t].classList.add(e):new RegExp("(^| )"+e+"( |$)","gi").test(this[0].className)?this[t].className=this[t].className.replace(new RegExp("(^|\\b)"+e.split(" ").join("|")+"(\\b|$)","gi")," "):this[t].className+=" "+e;return this},i.prototype.is=function(e){return function(e,t){var n=e.matchesSelector||e.msMatchesSelector||e.mozMatchesSelector||e.webkitMatchesSelector||e.oMatchesSelector;if(n)return n.call(e,t);for(var r=e.parentNode.querySelectorAll(t),i=r.length;i>=0;i-=1)if(r[i]===e)return!0;return!1}(this[0],e)},i.prototype.remove=function(){var e;for(e=0;e<this.length;e+=1)this[e].parentNode.removeChild(this[e])},i.prototype.click=function(e){var n,r;for(n=0;n<this.length;n+=1)(r=t.createEvent("HTMLEvents")).initEvent("click",!0,!1),this[n].dispatchEvent(r),e&&e()},i.prototype.trigger=function(e){var n,r,i,o=e.split(" ");for(n=0;n<this.length;n+=1)for(r=0;r<o.length;r+=1)(i=t.createEvent("HTMLEvents")).initEvent(o[r],!0,!1),this[n].dispatchEvent(i)},i.prototype.on=function(e,i,o,a){var s,c,u,l,h,d,p,f,g=e.split(" ");if(a=!!a,r.isDocument(this[0])&&"string"==typeof i)for(c=0;c<g.length;c+=1)"string"==typeof i?("boolean"==typeof o&&!1===o&&(o=function(e){return e.preventDefault(),!1}),u=i+"."+g[c],l=function(e){if(h=t.querySelectorAll(i)){for(d=e.target,p=-1;d&&-1===(p=Array.prototype.indexOf.call(h,d));)d=d.parentElement;p>-1&&o.call(d,e)}},Array.isArray(r.eventHandlers[u])||(r.eventHandlers[u]=[]),r.eventHandlers[u].push(l),t.addEventListener(g[c].split(".")[0],l,!0)):("boolean"==typeof i&&!1===i&&(i=function(e){return e.preventDefault(),!1}),Array.isArray(r.eventHandlers.document)||(r.eventHandlers.document=[]),r.eventHandlers.document.push(i),this[0].addEventListener(g[c].split(".")[0],i,a));else if(r.isDocument(this[0]))for(c=0;c<g.length;c+=1)"boolean"==typeof i&&!1===i&&(i=function(e){return e.preventDefault(),!1}),u="document."+g[c],Array.isArray(r.eventHandlers[u])||(r.eventHandlers[u]=[]),r.eventHandlers[u].push(i),t.addEventListener(g[c].split(".")[0],i,a);else if(r.isWindow(this[0]))for(c=0;c<g.length;c+=1)"boolean"==typeof i&&!1===i&&(i=function(e){return e.preventDefault(),!1}),u="window."+g[c],Array.isArray(r.eventHandlers[u])||(r.eventHandlers[u]=[]),r.eventHandlers[u].push(i),window.addEventListener(g[c].split(".")[0],i,a);else for(s=0;s<this.length;s+=1)for(c=0;c<g.length;c+=1)"object"===n(i)?(f=i,i=function(e){e.data=f,o.call(this[s],e)}):"boolean"==typeof i&&!1===i&&(i=function(e){return e.preventDefault(),!1}),u=r.selector+"."+g[c],Array.isArray(r.eventHandlers[u])||(r.eventHandlers[u]=[]),r.eventHandlers[u].push(i),this[s].addEventListener(g[c].split(".")[0],i,a);return this},i.prototype.off=function(e,i,o,a){var s,c,u,l=e.split(" ");for(a=!!a,s=0;s<this.length;s+=1)for(c=0;c<l.length;c+=1)if(r.isDocument(this[s])&&"string"==typeof i)if(void 0===o){if("object"===n(r.eventHandlers[i+"."+l[c]])){for(u=0;u<r.eventHandlers[i+"."+l[c]].length;u+=1)t.removeEventListener(l[c].split(".")[0],r.eventHandlers[i+"."+l[c]][u],!0);delete r.eventHandlers[i+"."+l[c]]}}else t.removeEventListener(l[c].split(".")[0],o,a);else if(r.isDocument(this[s]))if(void 0===i){if("object"===n(r.eventHandlers["document."+l[c]])){for(u=0;u<r.eventHandlers["document."+l[c]].length;u+=1)t.removeEventListener(l[c].split(".")[0],r.eventHandlers["document."+l[c]][u],a);delete r.eventHandlers["document."+l[c]]}}else t.removeEventListener(l[c].split(".")[0],i,a);else if(r.isWindow(this[s]))if(void 0===i){if("object"===n(r.eventHandlers["window."+l[c]])){for(u=0;u<r.eventHandlers["window."+l[c]].length;u+=1)window.removeEventListener(l[c].split(".")[0],r.eventHandlers["window."+l[c]][u],a);delete r.eventHandlers["window."+l[c]]}}else window.removeEventListener(l[c].split(".")[0],i,a);else if(void 0===i){if("object"===n(r.eventHandlers[r.selector+"."+l[c]])){for(u=0;u<r.eventHandlers[r.selector+"."+l[c]].length;u+=1)this[s].removeEventListener(l[c].split(".")[0],r.eventHandlers[r.selector+"."+l[c]][u],a);delete r.eventHandlers[r.selector+"."+l[c]]}}else this[s].removeEventListener(l[c].split(".")[0],i,a);return this},i.prototype.scrollTop=function(){return r.isWindow(this[0])||r.isDocument(this[0])?window.document.body.scrollTop||window.document.documentElement.scrollTop:this[0].scrollTop},i.prototype.scrollLeft=function(){return r.isWindow(this[0])||r.isDocument(this[0])?t.body.scrollLeft||t.documentElement.scrollLeft:this[0].scrollLeft},i.prototype.height=function(){var e;return r.isWindow(this[0])?t.documentElement.clientHeight:9===this[0].nodeType?(e=this[0].documentElement,Math.max(this[0].body.scrollHeight,e.scrollHeight,this[0].body.offsetHeight,e.offsetHeight,e.clientHeight)):Math.max(this[0].scrollHeight,this[0].offsetHeight)},i.prototype.width=function(){var e;return r.isWindow(this[0])?t.documentElement.clientWidth:9===this[0].nodeType?(e=this[0].documentElement,Math.max(this[0].body.scrollWidth,e.scrollWidth,this[0].body.offsetWidth,e.offsetWidth,e.clientWidth)):Math.max(this[0].scrollWidth,this[0].offsetWidth)},i.prototype.outerHeight=function(){return this[0].offsetHeight},i.prototype.offset=function(){var e=(this[0]&&this[0].ownerDocument).documentElement;return{top:this[0].getBoundingClientRect().top+window.pageYOffset-e.clientTop,left:this[0].getBoundingClientRect().left+window.pageXOffset-e.clientLeft}},i.prototype.attr=function(e,t){var r;if(t||""===t){for(r=0;r<this.length;r+=1)this[r].setAttribute(e,t);return this}return this[0]&&"object"===n(this[0])&&null!==this[0].getAttribute(e)?this[0].getAttribute(e):void 0},i.prototype.ready=function(e){r.isDocument(this[0])&&("interactive"===t.readyState||"complete"===t.readyState||"loaded"===t.readyState?e():t.addEventListener("DOMContentLoaded",e,!1))},i.prototype.parent=function(){var e;return"#document-fragment"===(null===(e=this[0].parentNode)||void 0===e?void 0:e.nodeName)?r(this[0].parentNode.host):r(this[0].parentNode)},i.prototype.get=function(e){return this[e]},i.prototype.show=function(){var e;for(e=0;e<this.length;e+=1)this[e].style.display="";return this},i.prototype.hide=function(){var e;for(e=0;e<this.length;e+=1)this[e].style.display="none";return this},i.prototype.focus=function(){var e;for(e=0;e<this.length;e+=1)this[e].focus();return this},i.prototype.blur=function(){var e;for(e=0;e<this.length;e+=1)this[e].blur();return this},i.prototype.clone=function(){return this[0].cloneNode(!0)},i.prototype.removeAttr=function(e){var t;for(t=0;t<this.length;t+=1)this[t].removeAttribute(e);return this},i.prototype.find=function(e){var t,n,i=r();try{t=this[0].querySelectorAll(e)}catch(e){return this.length=0,this}for(n=0;n<t.length;n+=1)i[n]=t[n];return i.length=t.length,i},i.prototype.is=function(e){var t,i=!1;if(!e||"object"!==n(this[0]))return!1;if("object"===n(e))return r(this[0]).get(0)===e.get(0);if("string"==typeof e){if(":visible"===e)return!(this[0].offsetWidth<=0&&this[0].offsetHeight<=0);if(":hidden"===e)return this[0].offsetWidth<=0&&this[0].offsetHeight<=0;if(":checked"===e)return this[0].checked;if(!(e.indexOf("[")>-1))return r(this[0]).get(0).nodeName.toLowerCase()===e;if((t=/([A-Za-z]+)\[([A-Za-z-]+)=([A-Za-z]+)]/g.exec(e)).length)return r.each(r(this[0]).get(0).attributes,(function(e,n){n.name===t[2]&&n.value===t[3]&&(i=!0)})),r(this[0]).get(0).nodeName.toLowerCase()===t[1]&&i}},i.prototype.css=function(e,t){var r,i;for(i=0;i<this.length;i+=1)if("object"===n(e))for(r in e)this[i].style[r]=e[r];else{if("number"!=typeof t&&"string"!=typeof t)return getComputedStyle(this[i])[e];if("__proto__"===e||"constructor"===e)return;this[i].style[e]=t}return this},i.prototype.animate=function(e,t){var n,i=this;for(void 0===t&&(t=400),n=0;n<i.length;n+=1)r.each(e,(function(e,r){r=r.toString();var o,a,s=getComputedStyle(i[n])[e].replace(/[0-9.-]/g,""),c=parseFloat(r),u=r.replace(/[0-9.-]/g,""),l=s||u,h=c-g,d=parseFloat(t/10),p=h/d,f=[],g=parseFloat(getComputedStyle(i[n])[e])||0;for(o=0;o<d;o+=1)g+=p,f.push({attribute:e,value:l?parseInt(g)+l:parseFloat(g).toFixed(1)});f.pop(),f.push({attribute:e,value:c+l}),f.length&&function e(t,n){var r=n[0].attribute;if("__proto__"===r||"constructor"===r)return;t.style[r]=n[0].value,n.shift(),n.length?a=setTimeout((function(){e(t,n)}),10):clearTimeout(a)}(i[n],f)}));return this},i.prototype.filter=function(e){return Array.prototype.filter.call(t.querySelectorAll(r.selector),(function(t,n){e(n,t)}))},e.hj=e.hj||{},e.hj.hq=e.hj.hq||r}(window,document)}catch(e){hj.exceptions.log(e,"hquery")}},,function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o}));var r=n(12),i=function(e,t){return{heatmap_id:t,max_bottom:hj.ui.getBottomAsPercentage(),page_visit_info:e,resumed:r.a.isResumedSession()}},o=function(e,t,n){return{playback_version:3,script_context_id:hj.scriptContextId,start_time:t,start_timestamp:n,page_visit_info:e,resumed:r.a.isResumedSession(),first_seen:r.a.isFirstSeen()}}},,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o}));var r=n(44),i=function(e,t){if(t)return"https://".concat(t,"/").concat(e);var n=Object(r.c)(),i=r.b[n].SURVEYS;return"https://".concat(i,"/").concat(e)},o=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.location.href,t=[r.b[r.a.LIVE].SURVEYS,r.b[r.a.REVIEW_WEBAPP].SURVEYS,r.b[r.a.REVIEW_WEBAPP_NEW].SURVEYS,r.b[r.a.REVIEW].SURVEYS,r.b[r.a.REVIEW_NEW].SURVEYS,r.b[r.a.STAGING].SURVEYS,r.b[r.a.DEV].SURVEYS,r.b[r.a.DEV_OLD].SURVEYS],n=document.createElement("a");return n.href=e,t.indexOf(n.hostname)>=0}},,,,,,,,,,,,,,,,function(e,t,n){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}e.exports=function(e,t){t||(t={}),"function"==typeof t&&(t={cmp:t});var n,i="boolean"==typeof t.cycles&&t.cycles,o=t.cmp&&(n=t.cmp,function(e){return function(t,r){var i={key:t,value:e[t]},o={key:r,value:e[r]};return n(i,o)}}),a=[];return function e(t){if(t&&t.toJSON&&"function"==typeof t.toJSON&&(t=t.toJSON()),void 0!==t){if("number"==typeof t)return isFinite(t)?""+t:"null";if("object"!==r(t))return JSON.stringify(t);var n,s;if(Array.isArray(t)){for(s="[",n=0;n<t.length;n++)n&&(s+=","),s+=e(t[n])||"null";return s+"]"}if(null===t)return"null";if(-1!==a.indexOf(t)){if(i)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}var c=a.push(t)-1,u=Object.keys(t).sort(o&&o(t));for(s="",n=0;n<u.length;n++){var l=u[n],h=e(t[l]);h&&(s&&(s+=","),s+=JSON.stringify(l)+":"+h)}return a.splice(c,1),"{"+s+"}"}}(e)}},function(e,t,n){var r,i,o;!function(a,s){"use strict";i=[n(256)],void 0===(o="function"==typeof(r=function(e){var t=/(^|@)\S+:\d+/,n=/^\s*at .*(\S+:\d+|\(native\))/m,r=/^(eval@)?(\[native code])?$/;return{parse:function(e){if(void 0!==e.stacktrace||void 0!==e["opera#sourceloc"])return this.parseOpera(e);if(e.stack&&e.stack.match(n))return this.parseV8OrIE(e);if(e.stack)return this.parseFFOrSafari(e);throw new Error("Cannot parse given Error object")},extractLocation:function(e){if(-1===e.indexOf(":"))return[e];var t=/(.+?)(?::(\d+))?(?::(\d+))?$/.exec(e.replace(/[()]/g,""));return[t[1],t[2]||void 0,t[3]||void 0]},parseV8OrIE:function(t){return t.stack.split("\n").filter((function(e){return!!e.match(n)}),this).map((function(t){t.indexOf("(eval ")>-1&&(t=t.replace(/eval code/g,"eval").replace(/(\(eval at [^()]*)|(,.*$)/g,""));var n=t.replace(/^\s+/,"").replace(/\(eval code/g,"(").replace(/^.*?\s+/,""),r=n.match(/ (\(.+\)$)/);n=r?n.replace(r[0],""):n;var i=this.extractLocation(r?r[1]:n),o=r&&n||void 0,a=["eval","<anonymous>"].indexOf(i[0])>-1?void 0:i[0];return new e({functionName:o,fileName:a,lineNumber:i[1],columnNumber:i[2],source:t})}),this)},parseFFOrSafari:function(t){return t.stack.split("\n").filter((function(e){return!e.match(r)}),this).map((function(t){if(t.indexOf(" > eval")>-1&&(t=t.replace(/ line (\d+)(?: > eval line \d+)* > eval:\d+:\d+/g,":$1")),-1===t.indexOf("@")&&-1===t.indexOf(":"))return new e({functionName:t});var n=/((.*".+"[^@]*)?[^@]*)(?:@)/,r=t.match(n),i=r&&r[1]?r[1]:void 0,o=this.extractLocation(t.replace(n,""));return new e({functionName:i,fileName:o[0],lineNumber:o[1],columnNumber:o[2],source:t})}),this)},parseOpera:function(e){return!e.stacktrace||e.message.indexOf("\n")>-1&&e.message.split("\n").length>e.stacktrace.split("\n").length?this.parseOpera9(e):e.stack?this.parseOpera11(e):this.parseOpera10(e)},parseOpera9:function(t){for(var n=/Line (\d+).*script (?:in )?(\S+)/i,r=t.message.split("\n"),i=[],o=2,a=r.length;o<a;o+=2){var s=n.exec(r[o]);s&&i.push(new e({fileName:s[2],lineNumber:s[1],source:r[o]}))}return i},parseOpera10:function(t){for(var n=/Line (\d+).*script (?:in )?(\S+)(?:: In function (\S+))?$/i,r=t.stacktrace.split("\n"),i=[],o=0,a=r.length;o<a;o+=2){var s=n.exec(r[o]);s&&i.push(new e({functionName:s[3]||void 0,fileName:s[2],lineNumber:s[1],source:r[o]}))}return i},parseOpera11:function(n){return n.stack.split("\n").filter((function(e){return!!e.match(t)&&!e.match(/^Error created at/)}),this).map((function(t){var n,r=t.split("@"),i=this.extractLocation(r.pop()),o=r.shift()||"",a=o.replace(/<anonymous function(: (\w+))?>/,"$2").replace(/\([^)]*\)/g,"")||void 0;o.match(/\(([^)]*)\)/)&&(n=o.replace(/^[^(]+\(([^)]*)\)$/,"$1"));var s=void 0===n||"[arguments not available]"===n?void 0:n.split(",");return new e({functionName:a,args:s,fileName:i[0],lineNumber:i[1],columnNumber:i[2],source:t})}),this)}}})?r.apply(t,i):r)||(e.exports=o)}()},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){n.p=hj.scriptDomain,(0,n(234).initErrorLogging)(),n(136),n(235),n(271),n(126),n(236),n(237),n(241),n(264),n(68),n(133),n(242),n(243),n(244),n(245),n(272),n(246),n(251),n(252),n(253),n(254),n(255),n(266)},function(e,t,n){"use strict";n.r(t),n.d(t,"exceptionLogger",(function(){return u})),n.d(t,"initErrorLogging",(function(){return l}));var r=n(4);function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e){if(!window.navigator&&!window.location&&!window.document)return e;var t=e.request&&e.request.url||window.location&&window.location.href,n=(window.document||{}).referrer,r=(window.navigator||{}).userAgent,i=a(a(a({},e.request&&e.request.headers),n&&{Referer:n}),r&&{"User-Agent":r}),o=a(a({},t&&{url:t}),{},{headers:i});return a(a({},e),{},{request:o})}var u=function(e){var t;function n(e,t,n){(void 0!==hj.log?hj.log.debug:function(){})(e,t,n)}var o,a=null,s=0,u=[],l=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*/g,h=/\d{1,3}\.\d{1,3}\.\d{1,3}\.(\d{1,3})/g,d=/\d{4,}([-\s]?\d{4,}){2,}/g,p=/password(.*)/g,f={},g=[],m=null!==hj.errorUrl,v=window.location.search.indexOf("hjErrorLoggerSamplingDisabled=1")>0?1:.002,y=(null==e?void 0:e.throttleDelay)||1e3,b=(null==e?void 0:e.maxErrors)||10,j=["event_id","stacktrace","release","useragent","User-Agent","logger","scriptversion","module","errorgroup","errormessagegroup"];function _(){return u.every((function(e){return"loaded"===e.state}))}function w(e){e.state="loaded",e.onLoad(),_()&&m&&f.startProcessing&&f.startProcessing()}function S(){u.filter((function(e){return"unloaded"===e.state})).forEach((function(e){e.check()?w(e):(function(e){var t;e.state="loading";var n=document.createElement("script");n.src=e.url,null===(t=document.getElementsByTagName("head")[0])||void 0===t||t.appendChild(n)}(e),function(e){var t=setInterval((function(){e.check()&&(clearInterval(t),w(e))}),10)}(e))}))}return u=[{name:"sentry",check:function(){return void 0!==hj.Sentry},url:"".concat(hj.scriptDomain).concat(null===(t=r.f.SENTRY)||void 0===t?void 0:t.js),state:"unloaded",onLoad:function(){var e=new hj.Sentry.BrowserClient({dsn:hj.errorUrl,environment:hj.environmentID,release:"insights-client-script-"+window.hjBootstrap.revision,sampleRate:v,defaultIntegrations:!1,integrations:[],beforeSend:function(e){if(!/.*Google.*/.test(window.navigator.userAgent))return function e(t){return hj.hq.each(t,(function(n,r){r&&"object"===i(r)?e(r):r&&"string"==typeof r&&-1==j.indexOf(n)&&(r=(r=(r=(r=r.replace(h,"<XXX>")).replace(l,"<<EMAIL>>")).replace(d,"123456789012")).replace(p,"<******>"),t[n]=r)})),t}(e)}});(o=new hj.Sentry.Hub(e)).setUser({id:_hjSettings.hjid}),o.getScope().addEventProcessor(c)}}],f.sendException=function(e){try{o.captureException(e.exception,{tags:{logger:e.module}})}catch(e){n("Failed to log exception: "+e,"Exception")}},f.tryCatch=function(e,t){return function(){try{return e.apply(this,arguments)}catch(e){if(/[?&]logErrors/.test(location.search)&&console.error(e),window.__TESTS__)throw e;f.log&&f.log(e,t||"Generic")}}},f.log=function(e,t){var r;n("Exception occurred in '"+t+"'","Exception",e),r={exception:e,module:t},g.length<b&&(_()||S(),g.push(r))},f.getQueue=function(){return g},f.startProcessing=function(){f.isProcessing&&!f.isProcessing()&&(a=setInterval((function(){var e;(e=g.shift())&&(f.sendException&&f.sendException(e),s++),s>=b&&f.stopProcessing&&f.stopProcessing()}),y))},f.isProcessing=function(){return null!==a},f.stopProcessing=function(){f.isProcessing&&f.isProcessing()&&a&&(clearInterval(a),a=null)},f};function l(){window.hj=window.hj||function(){(window.hj.q=window.hj.q||[]).push(arguments)},window._hjSettings=window._hjSettings||{},hj.defaults={host:"in.hotjar.com",environment:"live",environmentID:"live",insightsHost:"insights.hotjar.com",insightsApiHost:"insights.hotjar.com",staticHost:"static.hotjar.com",varsHost:"vars.hotjar.com",surveysHost:"surveys.hotjar.com",errorUrl:"https://<EMAIL>/1803150",identifyEndpoint:"https://identify.hotjar.com",viewCounterEndpoint:"https://vc.hotjar.io/sessions",viewCounterHitPercentage:.25,surveyImpressionsEndpoint:"https://surveystats.hotjar.io/hit"},hj.host=_hjSettings.host||hj.defaults.host,hj.insightsHost=_hjSettings.insightsHost||hj.defaults.insightsHost,hj.insightsApiHost=_hjSettings.insightsApiHost||hj.defaults.insightsApiHost,hj.staticHost=_hjSettings.staticHost||hj.defaults.staticHost,hj.varsHost=_hjSettings.varsHost||hj.defaults.varsHost,hj.surveysHost=_hjSettings.surveysHost||hj.defaults.surveysHost,hj.errorUrl=void 0!==_hjSettings.errorUrl?_hjSettings.errorUrl:hj.defaults.errorUrl,hj.environment=_hjSettings.environment||hj.defaults.environment,hj.environmentID=_hjSettings.environmentID||hj.defaults.environmentID,hj.identifyEndpoint=_hjSettings.identifyEndpoint||hj.defaults.identifyEndpoint,hj.viewCounterEndpoint=void 0!==_hjSettings.viewCounterEndpoint?_hjSettings.viewCounterEndpoint:hj.defaults.viewCounterEndpoint,hj.viewCounterHitPercentage=void 0!==_hjSettings.viewCounterHitPercentage?_hjSettings.viewCounterHitPercentage:hj.defaults.viewCounterHitPercentage,hj.surveyImpressionsEndpoint=void 0!==_hjSettings.surveyImpressionsEndpoint?_hjSettings.surveyImpressionsEndpoint:hj.defaults.surveyImpressionsEndpoint,hj.exceptions=u(),hj.tryCatch=hj.exceptions.tryCatch}},function(e,t){hj.tryCatch((function(){if(void 0!==window.MutationObserver||void 0!==window.WebKitMutationObserver||void 0!==window.MozMutationObserver){var e,t=(this||{}).__extends||function(e,t){function n(){this.constructor=e}for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r]);n.prototype=t.prototype,e.prototype=new n};if(void 0===(e="undefined"!=typeof WebKitMutationObserver?WebKitMutationObserver:MutationObserver))throw console.error("DOM Mutation Observers are required."),console.error("https://developer.mozilla.org/en-US/docs/DOM/MutationObserver"),Error("DOM Mutation Observers are required");var n,r=function(){function e(){this.nodes=[],this.values=[]}return e.prototype.isIndex=function(e){return+e==e>>>0},e.prototype.nodeId=function(t){var n=t[e.ID_PROP];return n||(n=t[e.ID_PROP]=e.nextId_++),n},e.prototype.set=function(e,t){var n=this.nodeId(e);this.nodes[n]=e,this.values[n]=t},e.prototype.get=function(e){return e=this.nodeId(e),this.values[e]},e.prototype.has=function(e){return this.nodeId(e)in this.nodes},e.prototype.deleteNode=function(e){e=this.nodeId(e),delete this.nodes[e],this.values[e]=void 0},e.prototype.keys=function(){var e,t=[];for(e in this.nodes)this.isIndex(e)&&t.push(this.nodes[e]);return t},e.prototype.getValues=function(){var e,t=[];for(e in this.values)this.isIndex(e)&&t.push(this.values[e]);return t},e.ID_PROP="__hj_mutation_summary_node_map_id__",e.nextId_=1,e}();(v=n||(n={}))[v.STAYED_OUT=0]="STAYED_OUT",v[v.ENTERED=1]="ENTERED",v[v.STAYED_IN=2]="STAYED_IN",v[v.REPARENTED=3]="REPARENTED",v[v.REORDERED=4]="REORDERED",v[v.EXITED=5]="EXITED";var i=function(){function e(e,t,n,r,i,o,a,s){void 0===t&&(t=!1),void 0===n&&(n=!1),void 0===r&&(r=!1),void 0===i&&(i=null),void 0===o&&(o=!1),void 0===a&&(a=null),void 0===s&&(s=null),this.node=e,this.childList=t,this.attributes=n,this.characterData=r,this.oldParentNode=i,this.added=o,this.attributeOldValues=a,this.characterDataOldValue=s,this.isCaseInsensitive=this.node.nodeType===Node.ELEMENT_NODE&&this.node instanceof HTMLElement&&this.node.ownerDocument instanceof HTMLDocument}return e.prototype.getAttributeOldValue=function(e){if(this.attributeOldValues)return this.isCaseInsensitive&&(e=e.toLowerCase()),this.attributeOldValues[e]},e.prototype.getAttributeNamesMutated=function(){var e=[];if(!this.attributeOldValues)return e;for(var t in this.attributeOldValues)e.push(t);return e},e.prototype.attributeMutated=function(e,t){this.attributes=!0,this.attributeOldValues=this.attributeOldValues||{},e in this.attributeOldValues||(this.attributeOldValues[e]=t)},e.prototype.characterDataMutated=function(e){this.characterData||(this.characterData=!0,this.characterDataOldValue=e)},e.prototype.removedFromParent=function(e){this.childList=!0,this.added||this.oldParentNode?this.added=!1:this.oldParentNode=e},e.prototype.insertedIntoParent=function(){this.added=this.childList=!0},e.prototype.getOldParent=function(){if(this.childList){if(this.oldParentNode)return this.oldParentNode;if(this.added)return null}return this.node.parentNode},e}(),o=function(){this.added=new r,this.removed=new r,this.maybeMoved=new r,this.oldPrevious=new r,this.moved=void 0},a=function(e){function n(t,n){e.call(this),this.rootNode=t,this.wasReachableCache=this.reachableCache=void 0,this.anyCharacterDataChanged=this.anyAttributesChanged=this.anyParentsChanged=!1;for(var r=0;r<n.length;r++){var i=n[r];switch(i.type){case"childList":this.anyParentsChanged=!0;for(var o=0;o<i.removedNodes.length;o++){var a=i.removedNodes[o];this.getChange(a).removedFromParent(i.target)}for(o=0;o<i.addedNodes.length;o++)a=i.addedNodes[o],this.getChange(a).insertedIntoParent();break;case"attributes":this.anyAttributesChanged=!0,(o=this.getChange(i.target)).attributeMutated(i.attributeName,i.oldValue);break;case"characterData":this.anyCharacterDataChanged=!0,(o=this.getChange(i.target)).characterDataMutated(i.oldValue)}}}return t(n,e),n.prototype.getChange=function(e){var t=this.get(e);return t||(t=new i(e),this.set(e,t)),t},n.prototype.getOldParent=function(e){var t=this.get(e);return t?t.getOldParent():e.parentNode},n.prototype.getIsReachable=function(e){if(e===this.rootNode)return!0;if(!e)return!1;this.reachableCache=this.reachableCache||new r;var t=this.reachableCache.get(e);return void 0===t&&(t=this.getIsReachable(e.parentNode),this.reachableCache.set(e,t)),t},n.prototype.getWasReachable=function(e){if(e===this.rootNode)return!0;if(!e)return!1;this.wasReachableCache=this.wasReachableCache||new r;var t=this.wasReachableCache.get(e);return void 0===t&&(t=this.getWasReachable(this.getOldParent(e)),this.wasReachableCache.set(e,t)),t},n.prototype.reachabilityChange=function(e){return this.getIsReachable(e)?this.getWasReachable(e)?2:1:this.getWasReachable(e)?5:0},n}(r),s=function(){function e(e,t,n,i,o){this.rootNode=e,this.mutations=t,this.selectors=n,this.calcReordered=i,this.calcOldPreviousSibling=o,this.treeChanges=new a(e,t),this.entered=[],this.exited=[],this.stayedIn=new r,this.visited=new r,this.matchCache=this.characterDataOnly=this.childListChangeMap=void 0,this.processMutations()}return e.prototype.processMutations=function(){if(this.treeChanges.anyParentsChanged||this.treeChanges.anyAttributesChanged)for(var e=this.treeChanges.keys(),t=0;t<e.length;t++)this.visitNode(e[t],void 0)},e.prototype.visitNode=function(e,t){if(!this.visited.has(e)){this.visited.set(e,!0);var n=this.treeChanges.get(e),r=t;if((n&&n.childList||null==r)&&(r=this.treeChanges.reachabilityChange(e)),0!==r){if(this.matchabilityChange(e),1===r)this.entered.push(e);else if(5===r)this.exited.push(e),this.ensureHasOldPreviousSiblingIfNeeded(e);else if(2===r){var i=2;n&&n.childList&&(n.oldParentNode!==e.parentNode?(i=3,this.ensureHasOldPreviousSiblingIfNeeded(e)):this.calcReordered&&this.wasReordered(e)&&(i=4)),this.stayedIn.set(e,i)}if(2!==r)for(n=e.firstChild;n;n=n.nextSibling)this.visitNode(n,r)}}},e.prototype.ensureHasOldPreviousSiblingIfNeeded=function(e){if(this.calcOldPreviousSibling){this.processChildlistChanges();var t=e.parentNode,n=this.treeChanges.get(e);n&&n.oldParentNode&&(t=n.oldParentNode),(n=this.childListChangeMap.get(t))||(n=new o,this.childListChangeMap.set(t,n)),n.oldPrevious.has(e)||n.oldPrevious.set(e,e.previousSibling)}},e.prototype.getChanged=function(e,t,n){for(this.selectors=t,this.characterDataOnly=n,t=0;t<this.entered.length;t++){n=this.entered[t];var r=this.matchabilityChange(n);(1===r||2===r)&&e.added.push(n)}var i=this.stayedIn.keys();for(t=0;t<i.length;t++)n=i[t],1===(r=this.matchabilityChange(n))?e.added.push(n):5===r?e.removed.push(n):2===r&&(e.reparented||e.reordered)&&(r=this.stayedIn.get(n),e.reparented&&3===r?e.reparented.push(n):e.reordered&&4===r&&e.reordered.push(n));for(t=0;t<this.exited.length;t++)n=this.exited[t],(5===(r=this.matchabilityChange(n))||2===r)&&e.removed.push(n)},e.prototype.getOldParentNode=function(e){var t=this.treeChanges.get(e);if(t&&t.childList)return t.oldParentNode?t.oldParentNode:null;if(0===(t=this.treeChanges.reachabilityChange(e))||1===t)throw Error("getOldParentNode requested on invalid node.");return e.parentNode},e.prototype.getOldPreviousSibling=function(e){var t=e.parentNode,n=this.treeChanges.get(e);if(n&&n.oldParentNode&&(t=n.oldParentNode),!(t=this.childListChangeMap.get(t)))throw Error("getOldPreviousSibling requested on invalid node.");return t.oldPrevious.get(e)},e.prototype.getOldAttribute=function(e,t){var n=this.treeChanges.get(e);if(!n||!n.attributes)throw Error("getOldAttribute requested on invalid node.");if(void 0===(n=n.getAttributeOldValue(t)))throw Error("getOldAttribute requested for unchanged attribute name.");return n},e.prototype.attributeChangedNodes=function(e){if(!this.treeChanges.anyAttributesChanged)return{};var t,n;if(e){t={},n={};for(var r=0;r<e.length;r++){t[o=e[r]]=!0,n[o.toLowerCase()]=o}}e={};var i=this.treeChanges.keys();for(r=0;r<i.length;r++){var o=i[r],a=this.treeChanges.get(o);if(a.attributes&&2===this.treeChanges.reachabilityChange(o)&&2===this.matchabilityChange(o))for(var s=o,c=a.getAttributeNamesMutated(),u=0;u<c.length;u++)o=c[u],(!t||t[o]||a.isCaseInsensitive&&n[o])&&a.getAttributeOldValue(o)!==s.getAttribute(o)&&(n&&a.isCaseInsensitive&&(o=n[o]),e[o]=e[o]||[],e[o].push(s))}return e},e.prototype.getOldCharacterData=function(e){if(!(e=this.treeChanges.get(e))||!e.characterData)throw Error("getOldCharacterData requested on invalid node.");return e.characterDataOldValue},e.prototype.getCharacterDataChanged=function(){if(!this.treeChanges.anyCharacterDataChanged)return[];for(var e=this.treeChanges.keys(),t=[],n=0;n<e.length;n++){var r=e[n];if(2===this.treeChanges.reachabilityChange(r)){var i=this.treeChanges.get(r);i.characterData&&r.textContent!=i.characterDataOldValue&&t.push(r)}}return t},e.prototype.computeMatchabilityChange=function(e,t){this.matchCache||(this.matchCache=[]),this.matchCache[e.uid]||(this.matchCache[e.uid]=new r);var n=this.matchCache[e.uid],i=n.get(t);return void 0===i&&(i=e.matchabilityChange(t,this.treeChanges.get(t)),n.set(t,i)),i},e.prototype.matchabilityChange=function(e){var t=this;if(this.characterDataOnly)switch(e.nodeType){case Node.COMMENT_NODE:case Node.TEXT_NODE:return 2;default:return 0}if(!this.selectors)return 2;if(e.nodeType!==Node.ELEMENT_NODE)return 0;for(var n=this.selectors.map((function(n){return t.computeMatchabilityChange(n,e)})),r=0,i=0;2!==r&&i<n.length;){switch(n[i]){case 2:r=2;break;case 1:r=5===r?2:1;break;case 5:r=1===r?2:5}i++}return r},e.prototype.getChildlistChange=function(e){var t=this.childListChangeMap.get(e);return t||(t=new o,this.childListChangeMap.set(e,t)),t},e.prototype.processChildlistChanges=function(){if(!this.childListChangeMap){this.childListChangeMap=new r;for(var e=0;e<this.mutations.length;e++){var t=this.mutations[e];if("childList"==t.type&&(2===this.treeChanges.reachabilityChange(t.target)||this.calcOldPreviousSibling)){for(var n=this.getChildlistChange(t.target),i=t.previousSibling,o=function(e,t){e&&!n.oldPrevious.has(e)&&!n.added.has(e)&&!n.maybeMoved.has(e)&&(!t||!n.added.has(t)&&!n.maybeMoved.has(t))&&n.oldPrevious.set(e,t)},a=0;a<t.removedNodes.length;a++){var s=t.removedNodes[a];o(s,i),n.added.has(s)?n.added.deleteNode(s):(n.removed.set(s,!0),n.maybeMoved.deleteNode(s)),i=s}for(o(t.nextSibling,i),a=0;a<t.addedNodes.length;a++)s=t.addedNodes[a],n.removed.has(s)?(n.removed.deleteNode(s),n.maybeMoved.set(s,!0)):n.added.set(s,!0)}}}},e.prototype.wasReordered=function(e){function t(e){if(!e||!o.maybeMoved.has(e))return!1;var n=o.moved.get(e);if(void 0!==n)return n;if(a.has(e))n=!0;else{if(a.set(e,!0),c.has(e))n=c.get(e);else{for(n=e.previousSibling;n&&(o.added.has(n)||t(n));)n=n.previousSibling;c.set(e,n)}n=n!==function e(n){var r=s.get(n);if(void 0!==r)return r;for(r=o.oldPrevious.get(n);r&&(o.removed.has(r)||t(r));)r=e(r);return void 0===r&&(r=n.previousSibling),s.set(n,r),r}(e)}return a.has(e)?(a.deleteNode(e),o.moved.set(e,n)):n=o.moved.get(e),n}if(!this.treeChanges.anyParentsChanged)return!1;this.processChildlistChanges();var n=e.parentNode,i=this.treeChanges.get(e);i&&i.oldParentNode&&(n=i.oldParentNode);var o=this.childListChangeMap.get(n);if(!o)return!1;if(o.moved)return o.moved.get(e);o.moved=new r;var a=new r,s=new r,c=new r;return o.maybeMoved.keys().forEach(t),o.moved.get(e)},e}(),c=function(){function e(e,t){var n=this;if(this.projection=e,this.added=[],this.removed=[],this.reparented=t.all||t.element||t.characterData?[]:void 0,this.reordered=t.all?[]:void 0,e.getChanged(this,t.elementFilter,t.characterData),t.all||t.attribute||t.attributeList){var r=e.attributeChangedNodes(t.attribute?[t.attribute]:t.attributeList);t.attribute?this.valueChanged=r[t.attribute]||[]:(this.attributeChanged=r,t.attributeList&&t.attributeList.forEach((function(e){n.attributeChanged.hasOwnProperty(e)||(n.attributeChanged[e]=[])})))}(t.all||t.characterData)&&(r=e.getCharacterDataChanged(),t.characterData?this.valueChanged=r:this.characterDataChanged=r),this.reordered&&(this.getOldPreviousSibling=e.getOldPreviousSibling.bind(e))}return e.prototype.getOldParentNode=function(e){return this.projection.getOldParentNode(e)},e.prototype.getOldAttribute=function(e,t){return this.projection.getOldAttribute(e,t)},e.prototype.getOldCharacterData=function(e){return this.projection.getOldCharacterData(e)},e.prototype.getOldPreviousSibling=function(e){return this.projection.getOldPreviousSibling(e)},e}(),u=/[a-zA-Z_]+/,l=/[a-zA-Z0-9_\-]+/;function h(e){return'"'+e.replace(/"/,'\\"')+'"'}var d=function(){function e(){}return e.prototype.matches=function(e){if(null===e)return!1;if(void 0===this.attrValue)return!0;if(!this.contains)return this.attrValue==e;e=e.split(" ");for(var t=0;t<e.length;t++)if(this.attrValue===e[t])return!0;return!1},e.prototype.toString=function(){return"class"===this.attrName&&this.contains?"."+this.attrValue:"id"!==this.attrName||this.contains?this.contains?"["+this.attrName+"~="+h(this.attrValue)+"]":"attrValue"in this?"["+this.attrName+"="+h(this.attrValue)+"]":"["+this.attrName+"]":"#"+this.attrValue},e}(),p=function(){function e(){this.uid=e.nextUid++,this.qualifiers=[]}var t;return Object.defineProperty(e.prototype,"caseInsensitiveTagName",{get:function(){return this.tagName.toUpperCase()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"selectorString",{get:function(){return this.tagName+this.qualifiers.join("")},enumerable:!0,configurable:!0}),e.prototype.isMatching=function(t){return t[e.matchesSelector](this.selectorString)},e.prototype.wasMatching=function(e,t,n){if(!t||!t.attributes)return n;if("*"!==(r=t.isCaseInsensitive?this.caseInsensitiveTagName:this.tagName)&&r!==e.tagName)return!1;for(var r=[],i=!1,o=0;o<this.qualifiers.length;o++){var a=this.qualifiers[o],s=t.getAttributeOldValue(a.attrName);r.push(s),i=i||void 0!==s}if(!i)return n;for(o=0;o<this.qualifiers.length;o++)if(a=this.qualifiers[o],void 0===(s=r[o])&&(s=e.getAttribute(a.attrName)),!a.matches(s))return!1;return!0},e.prototype.matchabilityChange=function(e,t){var n=this.isMatching(e);return n?this.wasMatching(e,t,n)?2:1:this.wasMatching(e,t,n)?5:0},e.parseSelectors=function(t){function n(){i&&(o&&(i.qualifiers.push(o),o=void 0),s.push(i)),i=new e}function r(){o&&i.qualifiers.push(o),o=new d}for(var i,o,a,s=[],c=/\s/,h=1,p=0;p<t.length;){var f=t[p++];switch(h){case 1:if(f.match(u)){n(),i.tagName=f,h=2;break}if("*"==f){n(),i.tagName="*",h=3;break}if("."==f){n(),r(),i.tagName="*",o.attrName="class",o.contains=!0,h=4;break}if("#"==f){n(),r(),i.tagName="*",o.attrName="id",h=4;break}if("["==f){n(),r(),i.tagName="*",o.attrName="",h=6;break}if(f.match(c))break;throw Error("Invalid or unsupported selector syntax.");case 2:if(f.match(l)){i.tagName+=f;break}if("."==f){r(),o.attrName="class",o.contains=!0,h=4;break}if("#"==f){r(),o.attrName="id",h=4;break}if("["==f){r(),o.attrName="",h=6;break}if(f.match(c)){h=14;break}if(","==f){h=1;break}throw Error("Invalid or unsupported selector syntax.");case 3:if("."==f){r(),o.attrName="class",o.contains=!0,h=4;break}if("#"==f){r(),o.attrName="id",h=4;break}if("["==f){r(),o.attrName="",h=6;break}if(f.match(c)){h=14;break}if(","==f){h=1;break}throw Error("Invalid or unsupported selector syntax.");case 4:if(f.match(u)){o.attrValue=f,h=5;break}throw Error("Invalid or unsupported selector syntax.");case 5:if(f.match(l)){o.attrValue+=f;break}if("."==f){r(),o.attrName="class",o.contains=!0,h=4;break}if("#"==f){r(),o.attrName="id",h=4;break}if("["==f){r(),h=6;break}if(f.match(c)){h=14;break}if(","==f){h=1;break}throw Error("Invalid or unsupported selector syntax.");case 6:if(f.match(u)){o.attrName=f,h=7;break}if(f.match(c))break;throw Error("Invalid or unsupported selector syntax.");case 7:if(f.match(l)){o.attrName+=f;break}if(f.match(c)){h=8;break}if("~"==f){o.contains=!0,h=9;break}if("="==f){o.attrValue="",h=11;break}if("]"==f){h=3;break}throw Error("Invalid or unsupported selector syntax.");case 8:if("~"==f){o.contains=!0,h=9;break}if("="==f){o.attrValue="",h=11;break}if("]"==f){h=3;break}if(f.match(c))break;throw Error("Invalid or unsupported selector syntax.");case 9:if("="==f){o.attrValue="",h=11;break}throw Error("Invalid or unsupported selector syntax.");case 10:if("]"==f){h=3;break}if(f.match(c))break;throw Error("Invalid or unsupported selector syntax.");case 11:if(f.match(c))break;if('"'==f||"'"==f){a=f,h=13;break}o.attrValue+=f,h=12;break;case 12:if(f.match(c)){h=10;break}if("]"==f){h=3;break}if("'"==f||'"'==f)throw Error("Invalid or unsupported selector syntax.");o.attrValue+=f;break;case 13:if(f==a){h=10;break}o.attrValue+=f;break;case 14:if(f.match(c))break;if(","==f){h=1;break}throw Error("Invalid or unsupported selector syntax.")}}switch(h){case 1:case 2:case 3:case 5:case 14:n();break;default:throw Error("Invalid or unsupported selector syntax.")}if(!s.length)throw Error("Invalid or unsupported selector syntax.");return s},e.nextUid=1,e.matchesSelector="function"==typeof(t=document.createElement("div")).webkitMatchesSelector?"webkitMatchesSelector":"function"==typeof t.mozMatchesSelector?"mozMatchesSelector":"function"==typeof t.msMatchesSelector?"msMatchesSelector":"matchesSelector",e}(),f=/^([a-zA-Z:_]+[a-zA-Z0-9_\-:\.]*)$/;function g(e){if("string"!=typeof e)throw Error("Invalid request option. attribute must be a non-zero length string.");if(!(e=e.trim()))throw Error("Invalid request option. attribute must be a non-zero length string.");if(!e.match(f))throw Error("Invalid request option. invalid attribute name: "+e);return e}function m(e){if(!e.trim().length)throw Error("Invalid request option: elementAttributes must contain at least one attribute.");var t={},n={};e=e.split(/\s+/);for(var r=0;r<e.length;r++){if(i=e[r]){var i,o=(i=g(i)).toLowerCase();if(t[o])throw Error("Invalid request option: observing multiple case variations of the same attribute is not supported.");n[i]=!0,t[o]=!0}}return Object.keys(n)}hj.MutationSummary=function(){function t(n){var r=this;this.connected=!1,this.options=t.validateOptions(n),this.observerOptions=t.createObserverOptions(this.options.queries),this.root=this.options.rootNode,this.callback=this.options.callback,this.elementFilter=Array.prototype.concat.apply([],this.options.queries.map((function(e){return e.elementFilter?e.elementFilter:[]}))),this.elementFilter.length||(this.elementFilter=void 0),this.calcReordered=this.options.queries.some((function(e){return e.all})),this.queryValidators=[],t.createQueryValidator&&(this.queryValidators=this.options.queries.map((function(e){return t.createQueryValidator(r.root,e)}))),this.observer=new e((function(e){r.observerCallback(e)})),this.reconnect()}return t.createObserverOptions=function(e){function t(e){r.attributes&&!n||(r.attributes=!0,r.attributeOldValue=!0,e?(n=n||{},e.forEach((function(e){n[e]=!0,n[e.toLowerCase()]=!0}))):n=void 0)}var n,r={childList:!0,subtree:!0};return e.forEach((function(e){e.characterData?(r.characterData=!0,r.characterDataOldValue=!0):e.all?(t(),r.characterData=!0,r.characterDataOldValue=!0):e.attribute?t([e.attribute.trim()]):(e=function(e){var t={};return e.forEach((function(e){e.qualifiers.forEach((function(e){t[e.attrName]=!0}))})),Object.keys(t)}(e.elementFilter).concat(e.attributeList||[])).length&&t(e)})),n&&(r.attributeFilter=Object.keys(n)),r},t.validateOptions=function(e){for(var n in e)if(!(n in t.optionKeys))throw Error("Invalid option: "+n);if("function"!=typeof e.callback)throw Error("Invalid options: callback is required and must be a function");if(!e.queries||!e.queries.length)throw Error("Invalid options: queries must contain at least one query request object.");n={callback:e.callback,rootNode:e.rootNode||document,observeOwnChanges:!!e.observeOwnChanges,oldPreviousSibling:!!e.oldPreviousSibling,queries:[]};for(var r=0;r<e.queries.length;r++){var i=e.queries[r];if(i.all){if(1<Object.keys(i).length)throw Error("Invalid request option. all has no options.");n.queries.push({all:!0})}else if("attribute"in i){if((a={attribute:g(i.attribute)}).elementFilter=p.parseSelectors("*["+a.attribute+"]"),1<Object.keys(i).length)throw Error("Invalid request option. attribute has no options.");n.queries.push(a)}else if("element"in i){var o=Object.keys(i).length,a={element:i.element,elementFilter:p.parseSelectors(i.element)};if(i.hasOwnProperty("elementAttributes")&&(a.attributeList=m(i.elementAttributes),o--),1<o)throw Error("Invalid request option. element only allows elementAttributes option.");n.queries.push(a)}else{if(!i.characterData)throw Error("Invalid request option. Unknown query request.");if(1<Object.keys(i).length)throw Error("Invalid request option. characterData has no options.");n.queries.push({characterData:!0})}}return n},t.prototype.createSummaries=function(e){if(!e||!e.length)return[];e=new s(this.root,e,this.elementFilter,this.calcReordered,this.options.oldPreviousSibling);for(var t=[],n=0;n<this.options.queries.length;n++)t.push(new c(e,this.options.queries[n]));return t},t.prototype.checkpointQueryValidators=function(){this.queryValidators.forEach((function(e){e&&e.recordPreviousState()}))},t.prototype.runQueryValidators=function(e){this.queryValidators.forEach((function(t,n){t&&t.validate(e[n])}))},t.prototype.changesToReport=function(e){return e.some((function(e){return!!("added removed reordered reparented valueChanged characterDataChanged".split(" ").some((function(t){return e[t]&&e[t].length}))||e.attributeChanged&&Object.keys(e.attributeChanged).some((function(t){return!!e.attributeChanged[t].length})))}))},t.prototype.observerCallback=function(e){this.options.observeOwnChanges||this.observer.disconnect(),e=this.createSummaries(e),this.runQueryValidators(e),this.options.observeOwnChanges&&this.checkpointQueryValidators(),this.changesToReport(e)&&this.callback(e),!this.options.observeOwnChanges&&this.connected&&(this.checkpointQueryValidators(),this.observer.observe(this.root,this.observerOptions))},t.prototype.reconnect=function(){if(this.connected)throw Error("Already connected");this.observer.observe(this.root,this.observerOptions),this.connected=!0,this.checkpointQueryValidators()},t.prototype.takeSummaries=function(){if(!this.connected)throw Error("Not connected");var e=this.createSummaries(this.observer.takeRecords());return this.changesToReport(e)?e:void 0},t.prototype.disconnect=function(){var e=this.takeSummaries();return this.observer.disconnect(),this.connected=!1,e},t.NodeMap=r,t.parseElementFilter=p.parseSelectors,t.optionKeys={callback:!0,queries:!0,rootNode:!0,oldPreviousSibling:!0,observeOwnChanges:!0},t}()}var v}),"mutation-summary")()},function(e,t){hj.tryCatch((function(){hj.xcom=hj.tryCatch((function(){var e,t={},n=[],r=1,i=2,o=10,a=r,s="https://"+hj.insightsHost+"/x",c=hj.tryCatch((function(){if(a==r){window.addEventListener?window.addEventListener("message",u,!1):window.attachEvent("onmessage",u),a=i;var t=document.createElement("iframe");t.style.position="fixed",t.style.top=-100,t.style.left=-100,t.width=1,t.height=1,t.id="_hjXcomProxyFrame",t.src=s,document.body.appendChild(t),e=document.getElementById("_hjXcomProxyFrame")}}),"data");t.send=hj.tryCatch((function(t,r){a==o?e.contentWindow.postMessage({eventId:t,data:r},"*"):(n.push({eventId:t,data:r}),c())}));var u=function(e){"knockknock"==e.data.eventId&&(a=o,n.forEach((function(e){t.send(e.eventId,e.data)})),n=[])};return t}),"xcom")()}),"xcom")()},function(e,t,n){n(238),n(239),n(120),n(111),n(265),n(118),n(240)},function(e,t,n){"use strict";n.r(t);var r=n(38),i=n(6);hj.visitData=hj.tryCatch((function(){var e={},t=hj.tryCatch((function(e){if(!hj.isPreview){var t={r_value:hj.settings.r,is_vpv:e.isVpv,session_only:e.sessionOnly,rec_value:hj.settings.rec_value},n=hj.apiUrlBaseV2;t.user_id=Object(r.a)().id,hj.log.debug("Sending visit-data request (session_only: ".concat(t.session_only,")"),"visitData",t),hj.ajax.post("".concat(n,"/client/sites/").concat(hj.settings.site_id,"/visit-data?sv=").concat(_hjSettings.hjsv||0),t,hj.tryCatch((function(e){if(e.success){var n=e.user_agent,r=t.user_id;hj.hq.isUndefined(hj.globals.get("isNotBot"))&&hj.globals.set("isNotBot",!0),hj.hq.isUndefined(hj.globals.get("userAgent"))&&(hj.globals.set("userAgent",n),hj.log.warnIfEmpty(n,"visit-data: user-agent")),hj.hq.isUndefined(hj.globals.get("userId"))&&(hj.globals.set("userId",r),hj.log.warnIfEmpty(r,"visit-data: userId"))}}),"data"))}}),"data");return e.getPageVisitInfo=hj.tryCatch((function(e,t,n){var r=e.get("pageInfo"),i=hj.globals.get("userAgent"),o={script_revision:window.hjBootstrap.revision,user_agent:i||"",page_url:r.url,url_hash:r.urlMD5,window_width:t.width,window_height:t.height,site_id:n},a=document.referrer;return""!==a&&(o.referrer=a),o}),"data"),e.trackView=hj.tryCatch((function(){if(null===i.a.items.ABSOLUTE_SESSION_IN_PROGRESS.get({resetExpiry:!0})){hj.log.debug("viewcounter: Determining if we should track this session...","visitData");var e=Math.random(),t=hj.viewCounterEndpoint&&hj.settings.site_id&&e<=hj.viewCounterHitPercentage,n="s=".concat(hj.viewCounterHitPercentage,"&r=").concat(e),r="".concat(hj.viewCounterEndpoint,"/").concat(hj.settings.site_id,"?").concat(n);t?(i.a.items.ABSOLUTE_SESSION_IN_PROGRESS.set(1,!0),hj.ajax.get(r),hj.log.debug("viewcounter: This session was tracked.","visitData",{mathRandomResult:e,viewCounterHitPercentage:hj.viewCounterHitPercentage})):(i.a.items.ABSOLUTE_SESSION_IN_PROGRESS.set(0,!0),hj.log.debug("viewcounter: This session will NOT be tracked.","visitData",{mathRandomResult:e,viewCounterHitPercentage:hj.viewCounterHitPercentage}))}else hj.log.debug("viewcounter: Session already checked. Skipping shouldTrackSession check.","visitData")}),"data"),e.track=hj.tryCatch((function(n){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];e.property=hj.property.create(),hj.eventStream.setCurrentPageVisitKey(e.property.key),n=n||document.location.href,hj.log.debug("Tracking: ".concat(n,", sessionOnly: ").concat(r),"visitData"),e.property.set("pageInfo",{url:n,urlMD5:hj.md5(hj.b64EncodeUnicode(n))}),t({isVpv:!1,sessionOnly:r})}),"data"),e.lazyTrack={calls:[],enqueue:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];hj.log.debug("Enqueued for lazy tracking: ".concat(e,", sessionOnly: ").concat(t),"visitData"),this.calls.push({url:e,sessionOnly:t})},execute:function(){var t=this.calls.shift();null!=t&&(hj.log.debug("Lazy tracking: ".concat(t.url,", sessionOnly: ").concat(t.sessionOnly),"visitData"),e.track(t.url,t.sessionOnly))}},e.trackVpv=hj.tryCatch((function(e){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];e=e||document.location.href,hj.log.debug("Tracking vpv: ".concat(e,", sessionOnly: ").concat(n),"visitData"),t({isVpv:!0,sessionOnly:n})}),"data"),e}),"data")(),hj.pageVisit=hj.visitData},function(e,t,n){"use strict";n.r(t);var r=n(68),i=n(133),o=n(38);function a(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,i=!1,o=void 0;try{for(var a,s=e[Symbol.iterator]();!(r=(a=s.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){i=!0,o=e}finally{try{r||null==s.return||s.return()}finally{if(i)throw o}}return n}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}hj.request=hj.tryCatch((function(){var e={},t={granted:null,callbacks:[],inProgress:!1},n=Object(o.a)().id,s=[],c=!1;function u(e,t,n,r){s.push([e,t,n,r]),c||function e(){if(0!==s.length){c=!0;var t=a(s.shift(),4),n=t[0],r=t[1],i=t[2],o=t[3];hj.ajax.post(n,r,(function(t){try{i&&i(t)}finally{e()}}),(function(t){try{o&&o(t)}finally{e()}}))}else c=!1}()}function l(e,t,i){var o=hj.ui.getWindowSize();t.window_width=t.window_width||o.width,t.window_height=t.window_height||o.height,t.user_id=n,t.url=document.location.href,r.userAttributes.flush((function(n,o,a){n?t.identify_user_id=r.userAttributes.get("user_id")||null:(t.identify_user_id=o,t.identify_attributes=a),u(e,t,(function(e){i&&i(e)}),null)}))}return e.getConsentGranted=hj.tryCatch((function(e){var r,i="?";if(null!==t.granted&&e)e(t.granted);else if(e&&t.callbacks.push(e),!t.inProgress){var o=hj.globals.get("userId");i+="user_id="+(o||n),t.inProgress=!0,hj.ajax.get("".concat(hj.apiUrlBase,"/sites/").concat(hj.settings.site_id,"/consent").concat(i),(function(e){for(t.granted=e.success&&-1!==e.scopes.indexOf("associate"),t.inProgress=!1;r=t.callbacks.pop();)hj.tryCatch(r,"ConsentData")(t.granted)}),(function(){for(t.granted=!1,t.inProgress=!1;r=t.callbacks.pop();)hj.tryCatch(r,"ConsentData")(t.granted)}))}})),e.grantConsent=hj.tryCatch((function(e,r){var i=hj.globals.get("userId");e.user_id=i||n,e.action="grant_for_response",u("".concat(hj.apiUrlBase,"/sites/").concat(hj.settings.site_id,"/consent/associate"),e,(function(e){t.granted=!!e.success,r&&hj.tryCatch(r,"GrantConsent")(t.granted)}),(function(){t.granted=!1}))})),e.saveFeedbackResponse=hj.tryCatch((function(e,t,n){e.action="feedback_widget_response",hj.event.signal("feedback.send",{id:t}),l("".concat(hj.apiUrlBase,"/client/sites/").concat(hj.settings.site_id,"/feedback/").concat(t),e,(function(r){if(hj.tryCatch(n,"Data")(r),Object.prototype.hasOwnProperty.call(e.response,"emotion")){var i={emotion:e.response.emotion,id:t,response_id:r.feedback_response_id};hj.event.signal("feedback.sentiment",i)}}))}),"data"),e.savePollResponse=hj.tryCatch((function(e){var t=hj.widget.pollData.id,n=hj.widget.pollResponseUUID;e.action="create_or_update_poll_response";var r=i.sessionEvents.get();r&&(e.events=hj.hq.stringify(r)),l("".concat(hj.apiUrlBaseV2,"/client/sites/").concat(hj.settings.site_id,"/poll/").concat(t,"/response/").concat(n),e,(function(e){e.is_new_response&&hj.event.signal("poll.send",{id:t,response_id:e.poll_response_id})}))}),"data"),e.completePollResponse=hj.tryCatch((function(e){var t=hj.widget.pollData.id,n=hj.widget.pollResponseUUID;u("".concat(hj.apiUrlBaseV2,"/client/sites/").concat(hj.settings.site_id,"/poll/").concat(t,"/response/").concat(n),{action:"finish_poll_response",completion_time_from_engagement_ms:e.fromEngagement,completion_time_from_render_ms:e.fromRender},null,null)}),"data"),e}),"data")()},function(e,t){hj.event=function(){var e={},t={},n={};function r(e,t,n){e[t]||(e[t]=[]),e[t].push(n)}return e.listen=hj.tryCatch((function(i,o){r(t,i,o),function(t){n[t]&&(n[t].forEach((function(n){e.signal(t,n)})),delete n[t])}(i)}),"hj.event.listen"),e.removeListener=hj.tryCatch((function(e,n){var r=t[e];if(r){var i=r.indexOf(n);-1!==i&&r.splice(i,1)}}),"hj.event.removeListener"),e.signal=hj.tryCatch((function(e,i,o){t[e]?t[e].forEach((function(e){e(i)})):o?r(n,e,i):n[e]=[i]}),"hj.event.signal"),e.clearAllListeners=hj.tryCatch((function(){t=[],n=[]}),"data"),e}()},function(e,t){hj.tryCatch((function(){hj.remoteStorage=hj.tryCatch((function(){var e={},t={},n=0,r="https://"+hj.varsHost.replace(/:$/,""),i=window.hjBootstrap.varsJar;function o(e){if((e.origin||e.originalEvent.origin)==r)try{var n=JSON.parse(e.data);(0,t[n.messageId])(n),delete t[n.messageId]}catch(e){return null}}function a(e,a){var s;window.addEventListener?window.addEventListener("message",o,!1):window.attachEvent("onmessage",o),a&&(s=n++,t[s]=a,e.messageId=s),e=JSON.stringify(e),window.hjBootstrap.varsLoaded?i.contentWindow.postMessage(e,r):hj.event.listen("varsLoaded",(function(){i.contentWindow.postMessage(e,r)}))}return e.get=hj.tryCatch((function(e,t,n){a({action:"_hjGet",key:e,siteId:n||hj.settings.site_id},(function(e){t(e.value)}))})),e.set=hj.tryCatch((function(e,t,n,r){a({action:"_hjSet",key:e,value:t,expiresMinutes:n||525600,siteId:r||hj.settings.site_id})})),e.remove=hj.tryCatch((function(e,t){a({action:"_hjRemove",key:e,siteId:t||hj.settings.site_id})})),e}),"remoteStorage")(),hj.remoteCookieJar=hj.remoteStorage}),"remoteStorage")()},function(e,t){hj.tryCatch((function(){var e=[];hj.selector=function(t){var n={2:{ignoreClassList:["over","hover","active","selected","scrolled"],ignoreBodyClasses:!0,ignoreUUIDClasses:!0,maxClassesAllowed:5,disallowedTagNameCharactersRE:/[^a-zA-Z0-9-_]/g}};return e[t=!t||t<2?2:t]||(e[t]={get:function(e){return function(e,t){var n=/(#|@|&|~|=|<|>|`|'|:|"|!|;|,|\?|%|\}|\{|\.|\*|\+|\||\[|\]|\(|\)|\/|\^|\$)/g,r=/(\s|#|@|&|~|=|<|>|`|'|:|"|!|;|,|\?|%|\}|\{|\.|\*|\+|\||\[|\]|\(|\)|\/|\^|\$)/g,i=t.ignoreUUIDClasses?/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/:{test:function(){return!1}},o=hj.tryCatch((function(e){var t,n,r,i,o,u;if(i=function(e,n){for(r=hj.hq(n),t=0;t<r.length;t++)if(r[t]===e[0])return t;return 0},void 0===e.attr("data-hj-ignore-attributes")){if(o=c(e.attr("id")),u=s(e.attr("name")),o)return"0:#"+o;if(u)return i(e,n='*[name="'+u+'"]')+":"+n}return i(e,n=a(e))+":"+n}),"common"),a=hj.tryCatch((function(e,n){var r=e.get(0);if(!r)return n;var i=void 0===e.attr("data-hj-ignore-attributes");if(void 0===n&&(n=""),e.is("html"))return"html"+n;var o=p(r.nodeName.toLowerCase());if(i){var s=c(e.attr("id"));if(s)return o+"#"+s+n;if("body"!==o||!t.ignoreBodyClasses){var u=d(e.attr("class"));u&&(o+=u)}}return a(e.parent(),">"+o+n)}),"common"),s=function(e){var r=[];return!(void 0===(e=hj.hq.trim((e||"").replace(/\s\s+/g," ")))||""===e||e.indexOf("yui_")>-1)&&((e=e.replace(n,"\\$1")).split(/\s/g).forEach((function(e){!(r.length<t.maxClassesAllowed||0===t.maxClassesAllowed)||hj.hq.inArray(e,t.ignoreClassList)||i.test(e)||""===e||r.push(e)})),r.join(" "))},c=function(e){return!(void 0===(e=hj.hq.trim((e||"").replace(/\s\s+/g," ")))||""===e||e.indexOf("yui_")>-1||h(e))&&(e=e.replace(r,"\\$1"),e=l(e),e=u(e))},u=function(e){if(!e)return e;var t=e.charAt(0);return/\d/.test(t)?"\\3"+t+" "+e.slice(1):e},l=function(e){if(!e||"-"!==e.charAt(0))return e;var t=e.charAt(0),n=e.charAt(1);return/\d/.test(n)?t+"\\3"+n+" "+e.slice(2):e},h=function(e){return 1===e.length&&"-"===e},d=function(e){var r=[];return!(void 0===(e=hj.hq.trim((e||"").replace(/\s\s+/g," ")))||""===e||e.indexOf("yui_")>-1||h(e))&&((e=e.replace(n,"\\$1")).split(/\s/g).forEach((function(e){!(r.length<t.maxClassesAllowed||0===t.maxClassesAllowed)||hj.hq.inArray(e,t.ignoreClassList)||i.test(e)||""===e||r.push(u(l(e)))})),r.length?"."+r.join("."):"")},p=function(e){return e.replace(t.disallowedTagNameCharactersRE,"")};return o(e)}(e,n[t])}}),e[t]}}))()},function(e,t,n){"use strict";n.r(t);var r=n(68),i=n(133),o=n(57);function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}hj.tryCatch((function(){hj.loader.registerModule("Command",function(){var e={},t={},n=window.hj.q,s=["ready","stateChange","trigger","session_event"],c=!1;function u(){var e=Array.prototype.slice.call(n.shift()),r=t[e[0]],i=e.slice(1);hj.log.debug("Processing command: "+e[0]+" "+i.map((function(e){return"object"!==a(e)&&"function"!=typeof e||null===e?e:JSON.stringify(e)})).join(", "),"command"),hj.hq.isFunction(r)?!function(e){return!hj.optOut||s.indexOf(e)>=0}(e[0])?hj.log.debug('Command "'+e[0]+'" blocked due to opt-out',"command"):hj.tryCatch(r,"command")(i):hj.log.debug('Unknown command: "'+e[0]+'"',"command"),n.length>0&&hj.tryCatch(u)()}function l(e){e[0]&&hj.event.signal("trigger:"+e[0])}function h(e){if(e[0]&&Array.isArray(e[0])){var t=hj.privacy.getTagsWithoutPII(e[0]);hj.behaviorData.tagRecording(t)}else hj.log.error("tagRecording was called with an invalid argument. Please provide an array of tags. For example: hj('tagRecording', ['tag1', 'tag2']).\n\nRead more here: https://help.hotjar.com/hc/en-us/articles/115011819488-How-to-Tag-your-Hotjar-Recordings")}return t.vpv=function(e){if(!hj.optOut){var t=e[0];if(t){var n=location.protocol+"//"+location.hostname+t;hj.log.debug('Sending virtual page view for URL "'.concat(n,'"'),"command"),Object(o.a)("commands.vpv")?hj.visitData.trackVpv(n,!1):Object(o.b)("commands.vpv")&&hj.visitData.trackVpv(n,!0)}hj.visitData.trackView()}},t.stateChange=function(e){var t=window.location.href;e&&e.length>=1&&e[0]&&(t=hj.url.getUrlFromString(e[0])),hj.log.debug('Changing state to URL "'+t+'"',"command"),hj.currentUrl&&hj.currentUrl!=t&&hj._init.reinit(t)},t.tagRecording=function(e){h(e)},t.trigger=function(e){l(e)},t.identify=function(e){if(e[1]){var t=e[0];t=!t&&0!==t||"null"===t||"undefined"===t?null:String(t),r.userAttributes.set(t,e[1])}else null!=e[0]&&"object"===a(e[0])&&r.userAttributes.set(null,e[0])},t.event=function(e){l(e),h([[e[0]]])},t.session_event=function(e){i.sessionEvents.set(e[0])},t.gaSetTracker=function(e){e[0]&&hj.integrations.google_analytics.sendHotjarUserId.setTracker(e[0])},t._xhr=function(){},t.ready=function(e){e.forEach((function(e){e()}))},e.run=function(){hj.command=this},e.activate=function(){c||(c=!0,n.push=function(){var e;for(e=0;e<arguments.length;e+=1)this[this.length]=arguments[e];return u(),this.length},n.length>0&&u())},hj.initialVisitDataSent&&e.activate(),e}(),!0)}),"command")()},function(e,t){hj.tryCatch((function(){var e;hj.loader.registerModule("DeferredPageContentModule",e={sendPageContent:function(e,t,n){var r="".concat(hj.apiUrlBase,"/sites/").concat(hj.settings.site_id,"/deferred-page-content/").concat(e);hj.ajax.post(r,{url:location.href,content:t},hj.tryCatch((function(e){e.success&&n?n():!1===e.success&&n&&n(e.error)})))},runIfDpcEmpty:function(e,t){var n="".concat(hj.apiUrlBase,"/sites/").concat(hj.settings.site_id,"/deferred-page-content/").concat(e);hj.globals.ready("isNotBot",hj.tryCatch((function(){hj.ajax.get("".concat(n,"/is-empty"),(function(e){e.is_empty&&(hj.log.debug("Deferred page content is empty: "+e.is_empty,"DeferredPageContent"),t())}))}),"dpc.isNotBot-callback"))},isTriggerOrAttribute:function(e){for(var t=0;t<e.length;t+=1)if("trigger"===e[t].component||"attribute"===e[t].component)return!0;return!1},run:function(t){var n=parseInt(hj.url.getParameter("hjDelay"))||1e3;hj.hq.each(hj.settings.deferred_page_contents||[],(function(r,i){hj.targeting.matchRules(i.targeting,t,(function(){var t=i.id;e.runIfDpcEmpty(t,(function(){setTimeout(hj.tryCatch((function(){hj.html.getPageContent((function(n){(hj.targeting.matchRules(i.targeting,hj.currentUrl)||e.isTriggerOrAttribute(i.targeting))&&(hj.log.warnIfEmpty(n,"sendPageContent.sendNewContent: pageContent"),e.sendPageContent(t,n))}))}),"dpc.sendNewContent"),n)}))}))}))}},!1)}),"deferredpagecontent")()},function(e,t){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}hj.tryCatch((function(){var e=function(){var t=/\S+/g,n=/\s?background[^;]+;?\s?/g;function r(){return Math.random()<.5?-1:1}function i(e,t){return e=e||16,t=t||"*",new Array(e+1).join(t)}var o=hj.tryCatch((function(e){var t={};return e.style&&e.style.width||(t.width=e.offsetWidth+"px"),e.style&&e.style.height||(t.height=e.offsetHeight+"px"),t}),"Suppresser.getSuppressedImageSize"),a=hj.tryCatch((function(e){return e&&e.length?e.replace(t,(function(e){return i(Math.max(1,e.length+r()))})):i(16+r())}),"Suppresser.textHandler"),s=hj.tryCatch((function(e){return hj.settings.anonymize_emails&&(e=e.replace(b,a)),hj.settings.anonymize_digits&&(e=e.replace(p,a)),e=e.replace(m,(function(e){return e.replace(p,a)}))}),"Suppresser.textContentHandler"),c=hj.tryCatch((function(){return i()}),"Suppresser.passwordHandler"),u=hj.tryCatch((function(){return i(16,1)}),"Suppresser.numberHandler"),l=hj.tryCatch((function(e){var t=e?16:10;return new Date(2839968e5).toISOString().substring(0,t)}),"Suppresser.getLocalDateStr"),h=hj.tryCatch((function(){return l(!1)}),"Suppresser.dateHandler"),d=hj.tryCatch((function(){return l(!0)}),"Suppresser.datetimeHandler"),f=hj.tryCatch((function(){return"00:00"}),"Suppresser.timeHandler"),g=hj.tryCatch((function(){return"1979-01"}),"Suppresser.monthHandler"),v=hj.tryCatch((function(){return"1979-W1"}),"Suppresser.weekHandler"),y=hj.tryCatch((function(e){if(e)return e.replace(n,"")}),"Suppresser.imageStyleHandler"),j={full:a,partial:s,textContent:s,password:c,number:u,date:h,datetime:d,"datetime-local":d,time:f,month:g,imgStyle:y,week:v};return{getSuppressedText:function(e,t){return j[e]?j[e](t):a(t)},getSuppressedImageNode:function(t){var n={src:"",meta:{style:o(t)}},r=e.getSuppressedText("imgStyle",t.getAttribute("style"));return r&&(n.style=r),n},textHandler:a}}(),t=function(e){return e&&e.parentNode?"#document-fragment"===e.parentNode.nodeName?e.parentNode.host:e.parentNode:null},r=["button","reset","submit"],i=hj.tryCatch((function(e){var t=e.tagName.toLowerCase(),n=e.type.toLowerCase();return"input"===t&&r.indexOf(n)>-1}),"hj.privacy._isAllowlistedInputType"),o=["default-style","content-type","refresh"],a=function(e){var t=e["http-equiv"]&&e["http-equiv"].value;return!t||o.some((function(e){return!!t.match(e)}))},s=hj.tryCatch((function(e){return hj.settings.recording_capture_keystrokes&&(e.attributes&&void 0!==e.attributes["data-hj-whitelist"]||e.className&&e.className.indexOf("data-hj-whitelist")>-1||e.attributes&&void 0!==e.attributes["data-hj-allow"]||e.className&&e.className.indexOf("data-hj-allow")>-1)}),"hj.privacy._isUserAllowlisted"),c=["password","email","hidden"],u=hj.tryCatch((function(e){var t=e.tagName.toLowerCase(),n=e.type.toLowerCase();return"input"===t&&c.indexOf(n)>-1}),"hj.privacy._isSupressedInputType"),l=["address","address1","address2","addressline1","addressline2","cell","cellphone","dateofbirth","dob","email","familyname","firstname","fullname","lastname","mobile","name","phone","postalcode","postcode","surname","tel","telephone","username","zip","zipcode","nationalinsurancenumber","nin","ppsn","security","securitynum","socialsec","socialsecuritynumber","socsec","ssn","adgangskode","authpw","contrasena","contrasenya","contraseña","contrasinal","cyfrinair","fjalëkalim","focalfaire","geslo","hasło","heslo","jelszó","kennwort","kωδικός","kωδικόςπρόσβασης","lozinka","lykilorð","lösenord","motdepasse","parakalw","parola","paroladordine","parole","parool","pasahitza","pass","passord","password","passwort","pw","pwd","pword","pwrd","salasana","sapwd","senha","sifre","slaptažodis","userpw","userpwd","wachtwoord","лозинка","парола","пароль","פּאַראָל","كلمهالسر","पासवर्ड","パスワード","密码","密碼","암호","cc","cccsc","cccvc","cccvv","ccexp","ccexpiry","ccexpmonth","ccexpyear","ccname","ccnum","ccnumber","cctype","creditcard","csc","cvc","cvv","exp","accountnum","accountnumber","bic","iban","ibanaccountnum","ibanaccountnumber","pin","pinno","pinnum","secq","secret","secretq","secretquestion","securityq","securityquestion","sortcode","swift"],h=hj.tryCatch((function(e){return[e.name,e.id].map((function(e){return e.replace(/[\s_-]+/g,"").toLocaleLowerCase()})).some((function(e){return l.indexOf(e)>-1}))}),"hj.privacy._hasSuppressedNameOrId"),d=/\d+/,p=new RegExp(d.source,"g"),f=hj.tryCatch((function(e){return d.test(e)}),"hj.privacy._hasDigitSequence"),g=/([+(]{0,2}\d[-_ ()/]{0,4}){9,}/,m=new RegExp(g.source,"g"),v=hj.tryCatch((function(e){return g.test(e)}),"hj.privacy._hasCCNumOrSSN"),y=/[^@\s]+@[^@\s]+\.[^@\s]+/,b=new RegExp(y.source,"g"),j=hj.tryCatch((function(e){return e.indexOf("@")>-1&&y.test(e)}),"hj.privacy._hasEmail"),_=/(?:\d{1,3}\.){3}\d{1,3}/,w=/(?:[A-F0-9]{1,4}:){7}[A-F0-9]{1,4}/,S=hj.tryCatch((function(e){return _.test(e)||w.test(e)}),"hj.privacy._hasIPAddress"),E=[function(e){return!!hj.settings.anonymize_digits&&f(e)},function(e){return!hj.settings.anonymize_digits&&v(e)},function(e){return!!hj.settings.anonymize_emails&&("string"==typeof e&&j(e))}],k=["style","script"],C=hj.tryCatch((function(e){if(e&&e.tagName){var t=e.tagName.toLowerCase();return k.indexOf(t)>-1}}),"hj.privacy._isAllowlistedElement"),O=hj.tryCatch((function(e,t){return(!t||!C(t))&&E.some((function(t){return t(e)}))}),"hj.privacy._shouldSuppressTextContent"),I=hj.tryCatch((function(e){return e.value||e.textContent})),N=[u,h,function(e){return v(I(e))},function(e){return j(I(e))}],T=hj.tryCatch((function(e){return N.some((function(t){return t(e)}))}),"hj.privacy._shouldSuppressInputOrTextarea"),R=hj.tryCatch((function(e){return"object"===n(e.attributes)&&(void 0!==e.attributes["data-hj-suppress"]||void 0!==e.attributes["data-hj-masked"])||"string"==typeof e.className&&e.className&&e.className.indexOf("data-hj-suppress")>-1}),"hj.privacy._isExplicitlySuppressed"),A=hj.tryCatch((function(e){var t=Object.prototype.toString.call(e);return-1!==["[object HTMLDocument]","[object Document]"].indexOf(t)}),"hj.privacy._isDocument"),x=hj.tryCatch((function(e){for(;e&&!A(e);){if(R(e))return!0;e=t(e)}return!1}),"hj.privacy._isSelfOrAncestorSuppressed");hj.privacy=hj.tryCatch((function(){var t={};return t.isRiskyNotAllowlistedOrSuppressedElement=hj.tryCatch((function(e,t){if(void 0===e||!e||void 0===e.tagName)return!1;if(hj.settings.suppress_all)return!0;if(hj.settings.suppress_text)return"IMG"!==e.tagName||!t||"src"!==t.name&&"style"!==t.name&&"srcset"!==t.name||x(e);var n="TEXTAREA"===e.tagName||"INPUT"===e.tagName&&!i(e);return n&&s(e)?T(e):n||x(e)}),"hj.privacy.isRiskyNotAllowlistedOrSuppressedElement"),t.isAttributeSuppressable=hj.tryCatch((function(e,t,n,r){var i={INPUT:{attrs:["value","placeholder"]},TEXTAREA:{attrs:["value","placeholder"]},A:{attrs:["href"],shouldSuppressAttrValueCheck:function(e){return!!!e.match(/^data:/)}},OPTION:{attrs:["label","value"]},PROGRESS:{attrs:["value"]},OPTGROUP:{attrs:["label"]},IMG:{attrs:["alt"]},DIV:{attrs:["title"]},META:function(e,t,n){switch(e){case"content":return(!n.name||"viewport"!==n.name.value)&&(!n["http-equiv"]||!a(n));case"name":return"viewport"!==t;case"http-equiv":return!a(n);case"charset":return!1;default:return!0}}}[e];return void 0!==i&&("function"==typeof i?i(t,n,r):!(i.attrs.indexOf(t)<0)&&(void 0===i.shouldSuppressAttrValueCheck||i.shouldSuppressAttrValueCheck(n)))}),"hj.privacy.isAttributeSuppressable"),t.hasPotentialPIIData=hj.tryCatch((function(e,t){return t=A(t)?null:t,O(e,t)}),"hj.privacy.hasPotentialPIIData"),t.getSuppressedText=hj.tryCatch((function(t,n){return e.getSuppressedText(t,n)}),"hj.privacy.getSuppressedText"),t.getSuppressedTextNode=hj.tryCatch((function(n,r){var i=n.parentNode||null,o=(r=r||t.isRiskyNotAllowlistedOrSuppressedElement(i))||i&&"textarea"===i.type?"full":"partial";return{content:(r=!C(i)&&(r||t.hasPotentialPIIData(n.textContent,i)))?e.getSuppressedText(o,n.textContent):n.textContent,shouldSuppressNode:r}})),t.getSuppressedNodeAttribute=hj.tryCatch((function(n,r,i){var o=r.value,a=r.name;if("data-hj-suppressed"!==a)return"IMG"!==n.tagName||"src"!==a&&"srcset"!==a&&"style"!==a||(i=t.isRiskyNotAllowlistedOrSuppressedElement(n,r),t.isRiskyNotAllowlistedOrSuppressedElement(n,r)&&(a="data-hj-suppressed",o=e.getSuppressedImageNode(n))),t.isAttributeSuppressable(n.tagName,a,o,n.attributes)&&((i=i||t.isRiskyNotAllowlistedOrSuppressedElement(n))||"META"===n.tagName?o=e.getSuppressedText("full",o):t.hasPotentialPIIData(o)&&(o=e.getSuppressedText("partial",o))),{name:a,value:o,shouldSuppressNode:i}})),t.getSuppressedNode=hj.tryCatch((function(e,n){for(var r={},i=n,o=0;o<e.attributes.length;o++){var a=e.attributes[o],s=t.getSuppressedNodeAttribute(e,a,n);s&&(r[s.name]=s.value,i=i||s.shouldSuppressNode)}return{node:{tagName:e.tagName,attributes:r||{}},shouldSuppressNode:i}})),t.getTagsWithoutPII=hj.tryCatch((function(e){return e?e.filter((function(e){return!!e&&(!function(e){var t="string"==typeof e;return!!(v(e)||t&&j(e)||S(e))}(e)||(hj.log.debug("Tag "+e+" has been removed due to possible PII information included"),!1))})):[]}),"hj.privacy.getTagsWithoutPII"),t.suppressErrorMessage=hj.tryCatch((function(t){var n=t;return hj.settings.anonymize_emails&&(n=n.replace(b,e.textHandler)),n=n.replace(m,e.textHandler)})),t}),"hj.privacy")()}),"hj.privacy")()},function(e,t,n){n(247),n(248),n(249),n(250)},function(e,t){hj.tryCatch((function(){hj.insertedRules=function(){var e,t={},n=[],r=!1,i=function(e,t,n,r){var i,o={};return o.rule=t,o.index=n,o.parentSelector=(i=e,hj.selector().get(hj.hq(i))),o.nodeId=hj.treeMirror.getNodeId(e),r&&(o.id=r),o};function o(e){n.forEach((function(t){t([e])}))}return t.init=function(){r||(e=CSSStyleSheet.prototype.insertRule,CSSStyleSheet.prototype.insertRule=function(){var t=Array.prototype.slice.call(arguments),n=e.apply(this,arguments),r=this.ownerNode||this.ownerHostNode,a=t[0],s=t[1];if(r instanceof Node&&document.contains(r)){var c=this.sheetId||null,u=i(r,a,s,c);o(u)}else{var l=this;hj.treeMirror.onTreeMirrorUpdate((function(){var e=l.ownerNode||l.ownerHostNode,t=l.sheetId||null;o(i(e,a,s,t))}))}return n},r=!0)},t.register=function(e,r){n.push(e),r&&e(t.getCurrentInsertedRules())},t.getCurrentInsertedRules=hj.tryCatch((function(e){for(var t=Array.prototype.slice,n=t.call(document.styleSheets).filter((function(n){if(void 0!==e&&!0===e&&n.href&&0===n.href.indexOf("blob:"))return!0;var r="";if(n.href&&0!==n.href.indexOf("blob:"))return!1;try{n.cssRules&&n.cssRules.length}catch(e){return!1}if(0===n.cssRules.length)return!1;n.ownerNode&&void 0!==n.ownerNode.innerText?r=n.ownerNode.innerText:n.ownerNode&&void 0!==n.ownerNode.innerHTML&&(r=n.ownerNode.innerHTML);var i=r.match(/{/g);return(i?i.length:0)<function e(n){var r=0;return t.call(n).forEach((function(t){t.cssRules?(r++,r+=e(t.cssRules)):r++})),r}(n.cssRules)})),r=[],i=function(e,i){var o=n[e],a=hj.selector().get(hj.hq(o.ownerNode)),s=t.call(o.cssRules),c=s.length;s.forEach((function(e,t){r.push({parentSelector:a,rule:e.cssText,index:t+c,nodeId:void 0})}))},o=0,a=n.length;o<a;o++)i(o);return r}),"hj.insertedRules.getCurrentInsertedRules"),t.destroy=function(){e&&(CSSStyleSheet.prototype.insertRule=e,e=null,r=!1),n=[]},t}()}),"hj.insertedRules")()},function(e,t){hj.tryCatch((function(){hj.deletedRules=function(){var e,t={},n=!1,r=[],i=function(e,t,n){var r,i={};return i.index=t,i.parentSelector=(r=e,hj.selector().get(hj.hq(r))),i.nodeId=hj.treeMirror.getNodeId(e),n&&(i.id=n),i};function o(e){r.forEach((function(t){t([e])}))}return t.init=function(){n||(e=CSSStyleSheet.prototype.deleteRule,CSSStyleSheet.prototype.deleteRule=function(){var t=Array.prototype.slice.call(arguments),n=e.apply(this,arguments),r=this.ownerNode||this.ownerHostNode,a=t[0];if(r instanceof Node&&document.contains(r)){var s=this.sheetId||null,c=i(r,a,s);o(c)}else{var u=this;hj.treeMirror.onTreeMirrorUpdate((function(){var e=u.ownerNode||u.ownerHostNode,t=u.sheetId||null;o(i(e,a,t))}))}return n},n=!0)},t.register=function(e){r.push(e)},t.destroy=function(){e&&(CSSStyleSheet.prototype.deleteRule=e,e=null,n=!1),r=[]},t}()}),"hj.deletedRules")()},function(e,t){hj.tryCatch((function(){var e,t;hj.cssBlobs=(t=[],(e={}).register=function(e){t.push(e)},e.handleBlobStyles=hj.tryCatch((function(e){var n=[];"link"===e.tagName.toLowerCase()&&"rel"in e.attributes&&"stylesheet"===e.attributes.rel.value&&"href"in e.attributes&&0===e.attributes.href.value.indexOf("blob:")&&setTimeout((function(){for(var r=hj.selector().get(hj.hq(e)),i=hj.treeMirror.getNodeId(e),o=0,a=e.sheet.cssRules.length;o<a;o++)n.push({parentSelector:r,rule:e.sheet.cssRules[o].cssText,nodeId:i,index:a+o});t.forEach((function(e){e(n)}))}),100)}),"hj.cssBlobs.apply"),e)}),"hj.cssBlobs")()},function(e,t,n){"use strict";n.r(t);var r=n(17);hj.tryCatch((function(){hj.adoptedStyleSheets=function(){var e={},t=[],n=null,i=!1;function o(e){t.forEach((function(t){t(e)}))}function a(e){return hj.selector().get(hj.hq(e))}return e.init=function(){i||(void 0!==window.ShadowRoot&&!!(n=Object.getOwnPropertyDescriptor(ShadowRoot.prototype,"adoptedStyleSheets"))&&Object.defineProperty(ShadowRoot.prototype,"adoptedStyleSheets",{set:function(){n.set.apply(this,arguments);var e=arguments[0],t=this.host,i={sheets:e.map((function(e){var n=[],i=e.cssRules.length;e.ownerHostNode||(e.ownerHostNode=t,e.sheetId=Object(r.b)());for(var o=0;o<i;o++)n.push(e.cssRules[o].cssText);return{id:e.sheetId,rules:n}}))};if(t instanceof Node&&document.contains(t))i.parentSelector=a(t),i.nodeId=hj.treeMirror.getNodeId(t),o(i);else{var s=this;hj.treeMirror.onTreeMirrorUpdate((function(){var e=s.host;i.parentSelector=a(e),i.nodeId=hj.treeMirror.getNodeId(e),o(i)}))}}}),i=!0)},e.register=function(e){t.push(e)},e.destroy=function(){n&&(Object.defineProperty(ShadowRoot.prototype,"adoptedStyleSheets",n),n=null,i=!1),t.length=0},e}()}),"hj.adoptedStyleSheets")()},function(e,t,n){"use strict";n.r(t);var r=n(61);hj.tryCatch((function(){var e,t,n,i,o,a,s;hj.loader.registerModule("UserBehavior",(e={},t=!1,n=function(){var e,t,n={},i={desktop:{time:600,distance:200,clicks:6},mobile:{time:600,distance:200,clicks:6},tablet:{time:600,distance:200,clicks:6},tv:{time:600,distance:200,clicks:6}},o={x:null,y:null,pageX:null,pageY:null},a=0;function s(){a=0,o.x=null,o.y=null,o.pageX=null,o.pageY=null}function c(n){var r,i,c,u,l;r=n.clientX,i=n.clientY,c=n.pageX,u=n.pageY,l=o.pageX&&o.pageX!==c||o.pageY&&o.pageY!==u,document.documentElement.clientWidth&&r>document.documentElement.clientWidth||document.documentElement.clientHeight&&i>document.documentElement.clientHeight||l||(a++,t&&clearInterval(t),t=setTimeout(s,e.time),function(t,n){var r=Math.abs(t-o.x),i=Math.abs(n-o.y);Math.sqrt(Math.pow(r,2)+Math.pow(i,2))>e.distance&&s()}(n.clientX,n.clientY),o.x=n.clientX,o.y=n.clientY,o.pageX=n.pageX,o.pageY=n.pageY,a===e.clicks&&hj.autotag.rageClick())}return n.listen=function(){e=i[Object(r.a)()],hj.hq(document).on("mousedown.user_behavior_rageclick",c)},n}(),i=function(){var e,t,n={},i={desktop:{time:1e3,directionChanges:4},mobile:{time:1e3,directionChanges:4},tablet:{time:1e3,directionChanges:4},tv:{time:1e3,directionChanges:4}},o=0,a=!1,s=0;function c(){o=0,a=!1}function u(){t&&clearInterval(t),t=setTimeout(c,e.time);var n=hj.hq(window).scrollTop(),r=s-n<0?"down":"up";a&&r!==a&&++o===e.directionChanges&&hj.autotag.wildScroll(),a=r,s=n}return n.listen=function(){e=i[Object(r.a)()],hj.hq(document).on("scroll.user_behavior_wildscroll",u)},n}(),o=function(){var e,t,n={},i={desktop:{time:100,distance:200,directionChanges:5},mobile:{time:100,distance:200,directionChanges:5},tablet:{time:100,distance:200,directionChanges:5},tv:{time:100,distance:200,directionChanges:5}},o=0,a={x:!1,y:!1},s={x:0,y:0},c={x:0,y:0};function u(){o=0,a.x=!1,a.y=!1}function l(n){var r={x:n.clientX,y:n.clientY},i={x:r.x<s.x?"left":r.x===s.x?a.x:"right",y:r.y<s.y?"up":r.y===s.y?a.y:"down"};(a.x&&i.x!==a.x||a.y&&i.y!==a.y)&&(function(t,n){if(c.x&&c.y){var r=Math.abs(t-c.x),i=Math.abs(n-c.y);return Math.sqrt(Math.pow(r,2)+Math.pow(i,2))<=e.distance}return!0}(r.x,r.y)?++o===e.directionChanges?hj.autotag.madMouse():(t&&clearInterval(t),t=setTimeout(u,e.time)):u(),c=r),a=i,s=r}return n.listen=function(){e=i[Object(r.a)()],hj.hq(document).on("mousemove.user_behavior_madmouse",l)},n}(),a=function(){var e,t={},n=[];function r(){hj.autotag.formSubmit()}function i(){var e=[],t=Array.prototype.filter.call(hj.hq("form"),(function(t){for(var r=0;r<n.length;r++)if(n[r]===t)return!0;return e.push(t),!0}));e.forEach((function(e){hj.log.debug("Found new form.","autotag",e),hj.hq(e).on("submit.user_behavior_formsubmit",r)})),n=t}return t.listen=function(){e=setInterval(i,2e3)},t.stop=function(){clearInterval(e)},t}(),e.listen=function(){hj.url.getParameter("hjAutogeneratedRecording")&&hj.autotag.autogenerated()},s=e,hj.autotag=hj.tryCatch((function(){var e={rageClick:r("rageclick"),wildScroll:r("wildscroll"),madMouse:r("madmouse"),formSubmit:r("formsubmit"),autogenerated:r("autogenerated")};function r(e){return function(){u([e])}}function c(e,t,n){return function(r){r&&u(t.reduce((function(t,i){var o=e;return Object.keys(i).forEach((function(e){var t=i[e],a=r[t];null==a&&(a=""),n&&(a=n(t,a)),o+="."+e+":"+a})),t.push(o),t}),[]))}}function u(e){hj.log.debug("Sending autotags","autotag",e),hj.behaviorData.tagRecording(e,!0)}function l(){var e,t;n.listen(),i.listen(),o.listen(),a.listen(),s.listen(),e=function(e,t){return t.replace(".e:",".E:").replace(".v:",".V:").replace(".c:",".C:")},t={"poll.show":c("poll.show",[{},{id:"id"}]),"poll.send":c("poll.send",[{},{id:"id",r_id:"response_id"}]),"poll.question":c("poll.q",[{t:"type"},{t:"type",a:"answer"},{t:"type",id:"id"},{qid:"questionUuid",id:"id"},{a:"answer",qid:"questionUuid",id:"id"},{t:"type",a:"answer",id:"id"}],(function(e,t){return"type"!==e?t:{"rating-scale-5":"rating5","rating-scale-7":"rating7","net-promoter-score":"nps","single-close-ended":"singleClose","multiple-close-ended":"multiClose","single-open-ended-multiple-line":"singleOpenMulti","single-open-ended-single-line":"singleOpenSingle"}[t]||t})),"feedback.show":c("feedback.show",[{},{id:"id"}]),"feedback.send":c("feedback.send",[{},{id:"id"}]),"feedback.sentiment":c("feedback.sentiment",[{e:"emotion"},{e:"emotion",id:"id",r_id:"response_id"}]),"survey.show":c("survey.show",[{},{id:"id"}]),"survey.open":c("survey.open",[{},{id:"id"}]),"exp.go":c("exp.go",[{e:"experimentId",c:"containerId"},{e:"experimentId",v:"variantId",c:"containerId"}],e),"exp.opt":c("exp.opt",[{e:"experimentId"},{e:"experimentId",v:"variantId"}],e),"int.ga":c("int.ga",[{a:"action"},{a:"action",c:"category"},{a:"action",c:"category",l:"label"},{a:"action",c:"category",l:"label",v:"value"}],(function(e,t){return"string"==typeof t?t.replace(/\.|:/g,"_"):t}))},Object.keys(t).forEach((function(e){hj.event.listen(e,t[e])}))}return e.start=hj.tryCatch((function(){hj.features.hasFeature("settings.billing_v2")&&!t&&(l(),t=!0)}),"user-behavior.autotag.start"),e}),"user-behavior.autotag")(),e.run=Function.prototype,e),!1)}),"user-behavior")()},function(e,t,n){"use strict";n.r(t);var r=n(17),i=n(113);hj.tryCatch((function(){var e,t,n,o,a,s;hj.widget=(o=["ar","fa","he"],a=[],s=!1,(n={}).ctrl=void 0,n.data=void 0,n.model={},n.activeLanguageDirection="ltr",n.widgetAttributePrefix="_hj-f5b2a1eb-9b07",n.ctaLinks={feedback:"https://www.hotjar.com/incoming-feedback?utm_source=client&utm_medium=incoming_feedback&utm_campaign=insights",polls:"https://www.hotjar.com/feedback-surveys?utm_source=client&utm_medium=poll&utm_campaign=insights",surveys:"https://www.hotjar.com/?utm_source=client&utm_medium=survey&utm_campaign=insights"},n._=n.translate=function(n){var r;if(!e)throw new Error("No active language has been set yet.");return null!==(r=e[n])&&void 0!==r?r:t.en[n]},n.addMatchingWidget=function(e,t,r,o,c,u){if(void 0===o||hj.isPreview||i.a.compareRatio(o,!0)){var l={type:e,id:t,created:r,runCallback:c,removeCallback:u};s?n.setActiveWidget(l):a.push(l)}else hj.log.debug("Session identifier not in targeting percentage. Widget will not match.","targeting")},n.clearWidget=hj.tryCatch((function(){hj.hq("#_hj_poll_container,#_hj_feedback_container,._hj-widget-container").length>0&&(hj.log.debug("Removing previously shown widget from DOM","widgets"),hj.hq("#_hj_poll_container,#_hj_feedback_container,._hj-widget-container").remove())}),"widgets"),n.disableSubmit=hj.tryCatch((function(){hj.widget.ctrl.find("#_hj-f5b2a1eb-9b07_action_submit").addClass("_hj-f5b2a1eb-9b07_btn_disabled")}),"common"),n.emptyMatchingWidgets=function(){a=[],hj.rendering.clearAllAbandonEvents(),s=!1},n.enableSubmit=hj.tryCatch((function(){hj.widget.ctrl.find("#_hj-f5b2a1eb-9b07_action_submit").removeClass("_hj-f5b2a1eb-9b07_btn_disabled")}),"common"),n.renderLegal=hj.tryCatch((function(e){var t=hj.settings.legal_name||"",r=hj.settings.privacy_policy_url||"",i="";return e&&""!==t&&""!==r&&(i=hj.rendering.renderTemplate('<div class="<%=p%>_widget_legal">                        <div class="<%=p%>_pull_left">                            <%=legalName%>                        </div>                        <div class="<%=p%>_pull_right">                            <a href="<%=privacyPolicyUrl%>" target="_blank" rel="noopener noreferrer">                                <%=hj.widget.translate("privacy_policy")%>                            </a>                        </div>                        <div class="<%=p%>_clear_both"></div>                    </div>',{p:n.widgetAttributePrefix,legalName:t,privacyPolicyUrl:new hj.rendering.TrustedString(r)})),i})),n.enterFullScreen=hj.tryCatch((function(){var e,t;n.isPhoneOrTablet()&&((e=hj.hq("body")).addClass("_hj-f5b2a1eb-9b07_position_fixed"),0===hj.hq("#hotjar-viewport-meta").length&&((t=document.createElement("meta")).id="hotjar-viewport-meta",t.name="viewport",t.content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",document.getElementsByTagName("head")[0].appendChild(t)),e.addClass("_hj-f5b2a1eb-9b07_fullscreen_page"))}),"common"),n.exitFullScreen=hj.tryCatch((function(){hj.hq("#hotjar-viewport-meta").remove(),hj.hq("body").removeClass("_hj-f5b2a1eb-9b07_fullscreen_page").removeClass("_hj-f5b2a1eb-9b07_position_fixed")}),"common"),n.isPhoneOrTablet=hj.tryCatch((function(){return hj.widget.isVeryNarrowScreen()||"phone"===Object(r.a)()||"tablet"===Object(r.a)()}),"common"),n.isVeryNarrowScreen=hj.tryCatch((function(){return hj.hq(window).width()<=450}),"common"),n.removeActiveWidget=function(e){e=e||function(){},n.activeWidget?(n.activeWidget.removeCallback&&n.activeWidget.removeCallback(e),delete n.activeWidget):e()},n.runLatestMatchingWidget=function(){var e;a.forEach((function(t){(!e||e.created<t.created)&&(e=t)})),e?n.setActiveWidget(e):n.removeActiveWidget(),s=!0},n.setActiveWidget=function(e){n.activeWidget&&e.type===n.activeWidget.type&&e.id===n.activeWidget.id||n.removeActiveWidget((function(){e.runCallback(),n.activeWidget=e}))},n.setLanguage=hj.tryCatch((function(n){if(!(n in(t={af:{lang:"af",age:"Ouderdom",change:"Verander",city:"Stad",close:"Sluit",dislike:"Sleg",email:"ePos",feedback:"Terugvoer",female:"Vroulik",full_name:"Volle naam",hate:"Haat dit",like:"Goed",love:"Uitstekend",male:"Manlik",neutral:"Neutraal",next:"Volgende",phone_number:"Telefoon nommer",please_type_here:"Tik asb. hier ...",powered_by_hotjar:"aangedryf deur Hotjar",reply:"Antwoort",send:"Stuur",sent:"Gestuur",skip:"Slaan oor.",sign_me_up:"Skryf my in!",select_the_area:"Kies 'n element op die bladsy.",tell_us_about_your_experience:"Vertel ons van jou ervaring..."},ar:{lang:"ar",age:"العمر",change:"تغيير",city:"المدينة",close:"أغلق",dislike:"غير راضي",email:"البريد الألكتروني",feedback:"رأي",female:"انثى",full_name:"الأسم الكامل",hate:"سيء",like:"أعجبني",love:"أحببت",male:"ذكر",neutral:"عادي",next:"التالى",phone_number:"رقم الهاتف",please_type_here:"الرجاء الكتابة هنا...",powered_by_hotjar:"بإدارة Hotjar",reply:"رد",send:"أرسِل",sent:"أُرسِلت",skip:"تجاوز",sign_me_up:"سجّلني!",select_the_area:"اختر عنصر من الصفحة",tell_us_about_your_experience:"أخبرنا عن تجربتك...",consent_more_information:"المزيد من المعلومات",consent:"ربط ردودك بالبيانات المتعلقة بزيارتك للموقع (ببيانات الجهاز المستخدم، بيانات الاستخدام ، ملفات تعريف الارتباط (الكوكيز)، والتفاعلات) سيساعدنا على تحسين الخدمة بشكل أسرع. هل توافق على القيام بذلك لزياراتك السابقة والمستقبلية؟",comment_for_option:"التعليق على الخيار"},bg:{lang:"bg",age:"Възраст",change:"Смени",city:"Град",close:"Затвори",dislike:"Не харесвам",email:"E-mail",feedback:"Обратна връзка",female:"Жена",full_name:"Име и фамилия",hate:"Ненавиждам",like:"Харесвам",love:"Обожавам",male:"Мъж",neutral:"Неутрален",next:"Следващия",phone_number:"Телефон",please_type_here:"Моля напишете Вашия отговор тук...",powered_by_hotjar:"Инструмент на Hotjar",reply:"Отговор",send:"Изпрати",sent:"Изпратено",skip:"Пропусни",sign_me_up:"Запиши ме!",select_the_area:"Изберете елемент на страницата.",tell_us_about_your_experience:"Разкажете своето преживяване...",consent_more_information:"Повече информация",consent:"Свързвайки вашата обратна връзка с данни свързани с посещенията на вашата страница (спрямо устройство, данни за потреблението, бизквитки, поведение и интеракции) ще ни помогне да подобрим услугата си. Съгласни ли сте да направим това за досегашните и бъдещите посещения на вашата страница?"},ca:{lang:"ca",age:"Edat",change:"Canvia",city:"Ciutat",close:"Tanca",dislike:"No m'agrada",email:"E-mail",feedback:"Comentaris",female:"Dona",full_name:"Nom complet",hate:"Odi",like:"M'agrada",love:"Amor",male:"Home",neutral:"Neutral",next:"Següent",phone_number:"Telèfon",please_type_here:"Introdueix aquí...",powered_by_hotjar:"Gràcies a Hotjar",reply:"Respondre",send:"Envia",sent:"Enviat",skip:"Omet",sign_me_up:"Apunta'm-hi!",select_the_area:"Seleccioneu un element a la pàgina.",tell_us_about_your_experience:"Explica'ns la teva experiència ...",consent_more_information:"Més informació",consent:"Connectant els teus comentaris amb les dades relacionades a les teves visites (específics del dispositiu, ús de dades, cookies, comportament i interaccions) ens ajudarà a millorar més rapidament. Podem comptar amb el teu consentiment per connectar-los, per a les teves anteriors i futures visites?"},cs:{lang:"cs",age:"Věk",change:"Změnit",city:"Město",close:"Zavřít",dislike:"Nelíbí se mi",email:"E-mail",feedback:"Zpětná vazba",female:"Žena",full_name:"Celé jméno",hate:"Nesnáším",like:"Mám rád",love:"Miluji",male:"Muž",neutral:"Neutrální",next:"Další",phone_number:"Telefon",please_type_here:" Zde prosím odpovězte...",powered_by_hotjar:"powered by Hotjar",reply:"Odpovědět",send:"Odeslat",sent:"Odesláno",skip:"Přeskočit",sign_me_up:"Zaregistruj mě!",select_the_area:"Vybrat prvky na stránce.",tell_us_about_your_experience:"Řekněte nám o vaší zkušenosti...",consent_more_information:"Další informace",consent:"Připojená zpětná vazba od vás spolu s údaji o vašich návštěvách (typ zařízení, využití dat, soubory cookies, chování a vzájemné interakce) nám pomůže se rychleji zlepšovat. Udělíte nám s tímto souhlas pro vaše předchozí a budoucí návštěvy?"},cy:{lang:"cy",age:"Oedran",change:"Newid",city:"Dinas",close:"Cau",dislike:"Ddim yn hoffi",email:"E-bost",feedback:"Adborth",female:"Benywaidd",full_name:"Enw llawn",hate:"Casáu",like:"Hoffi",love:"Caru",male:"Gwrywaidd",neutral:"Niwtral",next:"Nesaf",phone_number:"Rhif ffôn",please_type_here:"Teipiwch yma os gwelwch yn dda...",powered_by_hotjar:"powered by Hotjar",reply:"Ateb",send:"Anfon",sent:"Anfonwyd",skip:"Symyd ymlaen",sign_me_up:"Ychwanegwch fi at y rhestr!",select_the_area:"Dewiswch elfen ar y dudalen.",tell_us_about_your_experience:"Dywedwch wrthym am eich profiad..."},da:{lang:"da",age:"Alder",change:"Ændre",city:"By",close:"Luk",dislike:"Kan ikke lide",email:"Email",feedback:"Feedback",female:"Kvinde",full_name:"Navn",hate:"Hader",like:"Synes om",love:"Elsker",male:"Mand",neutral:"Neutralt",next:"Næste",phone_number:"Telefonnummer",please_type_here:"Skriv venligst her...",powered_by_hotjar:"powered by Hotjar",reply:"Svar",send:"Send",sent:"Sendt",skip:"Spring over",sign_me_up:"Deltag!",select_the_area:"Vælg et element på siden.",tell_us_about_your_experience:"Fortæl os om din oplevelse...",consent_more_information:"Flere oplysninger ",consent:"Hvis du forbinder din feedback med data som er relaterede til dine besøg (enhedsspecifikke, brugerdata, cookies, adfærd og interaktioner), kan vi hurtigere forbedre os. Giver du os tilladelse til at gøre dette for dine tidligere og kommende besøg?"},de:{lang:"de",age:"Alter",change:"Ändern",city:"Stadt",close:"Schließen",dislike:"Gefällt mir nicht",email:"E-Mail",feedback:"Feedback",female:"Weiblich",full_name:"Name",hate:"Gefällt mir gar nicht",like:"Gefällt mir",love:"Gefällt mir sehr",male:"Männlich",neutral:"Neutral",next:"Weiter",phone_number:"Telefonnummer",please_type_here:"Bitte hier schreiben...",powered_by_hotjar:"powered by Hotjar",reply:"Antworten",send:"Senden",sent:"Gesendet",skip:"Überspringen",sign_me_up:"Jetzt anmelden!",select_the_area:"Wählen Sie ein Element auf der Seite aus.",tell_us_about_your_experience:"Teilen Sie uns Ihre Erfahrungen mit...",consent_more_information_url:"https://www.hotjarconsent.com/de.html",consent_more_information:"Weitere Informationen",consent:"Durch die Verbindung Ihres Feedbacks mit Daten aus Ihren Besuchen (gerätespezifisch, Nutzungsdaten, Cookies, Verhalten und Interaktionen) können wir schneller Verbesserungen durchführen. Geben Sie uns dafür Ihr Einverständnis für den vorhergehenden und weitere Besuche?"},el:{lang:"el",age:"Ηλικία",change:"Αλλαγή",city:"Πόλη",close:"Κλείσιμο",dislike:"Κακή",email:"Email",feedback:"Feedback",female:"Γυναίκα",full_name:"Ονοματεπώνυμο",hate:"Πολύ κακή",like:"Καλή",love:"Πολύ καλή",male:"Άνδρας",neutral:"Μέτρια",next:"Επόμενο",phone_number:"Τηλέφωνο",please_type_here:"Παρακαλώ πληκτρολογήστε εδώ...",powered_by_hotjar:"υλοποιήθηκε από το Hotjar",reply:"Απάντηση",send:"Αποστολή",sent:"Στάλθηκε",skip:"Παράλειψη",sign_me_up:"Εγγραφή!",select_the_area:"Επέλεξε ένα στοιχείο στη σελίδα.",tell_us_about_your_experience:"Πες μας σχετικά με την εμπειρία σου...",consent_more_information_url:"https://www.hotjarconsent.com/el.html",consent_more_information:"Περισσότερες πληροφορίες",consent:"Η σύνδεση των σχολίων σας με δεδομένα που σχετίζονται με τις επισκέψεις σας (δεδομένα σχετικά με τη συσκευή σας, δεδομένα χρήσης, cookies, συμπεριφορά και αλληλεπιδράσεις) θα μας βοηθήσει να βελτιωθούμε ταχύτερα. Μας δίνετε τη συναίνεσή σας να πραγματοποιήσουμε αυτή τη σύνδεση για προηγούμενες και επόμενες επισκέψεις σας;"},en:{lang:"en",age:"Age",change:"Change",city:"City",close:"Close",dislike:"Dislike",email:"Email",feedback:"Feedback",female:"Female",full_name:"Full name",hate:"Hate",like:"Like",love:"Love",male:"Male",neutral:"Neutral",next:"Next",phone_number:"Phone number",please_type_here:"Please type here...",powered_by_hotjar:"powered by Hotjar",reply:"Reply",send:"Send",sent:"Sent",skip:"Skip",sign_me_up:"Sign me up!",select_the_area:"Select an element on the page.",tell_us_about_your_experience:"Tell us about your experience...",consent_more_information_url:"https://www.hotjarconsent.com",consent_more_information:"More information",consent:"Before you go, can we connect your response with data (device, usage, cookies, behavior, and interactions) related to your visits? This will help us give you a better experience.",privacy_policy:"Privacy Policy",surveyComplete:"Survey complete",this_field_is_required:"This field is required",comment_for_option:"Comment for option"},es:{lang:"es",age:"Edad",change:"Cambiar",city:"Ciudad",close:"Cerrar",dislike:"No me gusta",email:"Email",feedback:"Sugerencias",female:"Mujer",full_name:"Nombre completo",hate:"Me enoja",like:"Me gusta",love:"Me encanta",male:"Hombre",neutral:"Neutral",next:"Siguiente",phone_number:"Teléfono",please_type_here:"Por favor, escribe aquí...",powered_by_hotjar:"powered by Hotjar",reply:"Responder",send:"Enviar",sent:"Enviado",skip:"Omitir",sign_me_up:"¡Inscríbeme!",select_the_area:"Selecciona un elemento de la página.",tell_us_about_your_experience:"Cuéntanos tu experiencia...",consent_more_information_url:"https://www.hotjarconsent.com/es.html",consent_more_information:"Más información",consent:"Conectar tus comentarios con datos relacionados de tus visitas (datos específicos del dispositivo o de uso, cookies, comportamiento e interacciones) nos ayudará a mejorar más rápidamente. ¿Nos das tu consentimiento para hacerlo con tus visitas anteriories y futuras?"},et:{lang:"et",age:"Vanus",change:"Muuda",city:"Linn",close:"Sulge",dislike:"Ei meeldi",email:"Email",feedback:"Tagasiside",female:"Naine",full_name:"Nimi",hate:"Üldse ei meeldi",like:"Meeldib",love:"Väga meeldib",male:"Mees",neutral:"Neutraalne",next:"Järgmine",phone_number:"Tel. nr.",please_type_here:"Palun sisestage siia...",powered_by_hotjar:"powered by Hotjar",reply:"Vasta",send:"Saada",sent:"Saadetud",skip:"Jätke vahele",sign_me_up:"Registreeru!",select_the_area:"Valige element leheküljel.",tell_us_about_your_experience:"Kirjutage meile oma kogemusest..."},fa:{lang:"fa",age:"سن",change:"تغییر",city:"شهر",close:"بستن",dislike:"ناراضی",email:"پست الکترونیک",feedback:"نظرسنجی",female:"زن",full_name:"نام کامل",hate:"خیلی ناراضی",like:"راضی",love:"خیلی راضی",male:"مرد",neutral:"معمولی",next:"بعد",phone_number:"شماره تلفن",please_type_here:"لطفا اینجا بنویسید",powered_by_hotjar:"نیرو گرفته از Hotjar",reply:"پاسخ",send:"بفرست",sent:"فرستاده شد",skip:"رد کردن",sign_me_up:"ثبت‌نام",select_the_area:"یک بخش صفحه انتخاب کنید",tell_us_about_your_experience:"تجربه خود را با ما درمیان بگذارید",comment_for_option:"نظر برای گزینه"},fi:{lang:"fi",age:"Ikä",change:"Muuta",city:"Kaupunki",close:"Sulje",dislike:"En pitänyt",email:"Sähköposti",feedback:"Palaute",female:"Nainen",full_name:"Koko nimi",hate:"Inhosin",like:"Pidin",love:"Rakastin",male:"Mies",neutral:"Neutraali",next:"Seuraava",phone_number:"Puhelinnumero",please_type_here:"Kirjoita tähän",powered_by_hotjar:"Alustana toimii Hotjar",reply:"Vastaa",send:"Lähetä",sent:"Lähetetty",skip:"Ohita",sign_me_up:"Rekisteröi minut!",select_the_area:"Valitse jokin elementti sivulta.",tell_us_about_your_experience:"Kerro meille kokemuksestasi...",consent_more_information_url:"https://www.hotjarconsent.com/fi.html",consent_more_information:"Lisää tietoja",consent:"Palautteesi yhdistäminen vierailuihisi liittyviin tietoihin (laitekohtaiset, käyttötiedot, evästeet, käyttäytyminen ja vuorovaikutus) auttaa meitä tekemään parannuksia nopeammin. Annatko meille suostumuksesi tehdä näin aiemmille sekä tuleville vierailuillesi?"},fr:{lang:"fr",age:"Âge",change:"Changer",city:"Ville",close:"Fermer",dislike:"N'aime pas",email:"E-mail",feedback:"Votre avis",female:"Femme",full_name:"Nom et prénom",hate:"Déteste",like:"Aime",love:"Adore",male:"Homme",neutral:"Neutre",next:"Suivant",phone_number:"Numéro de téléphone",please_type_here:"Ecrivez ici",powered_by_hotjar:"Propulsé par Hotjar",reply:"Répondre",send:"Envoyer",sent:"Envoyé",skip:"Passer",sign_me_up:"M'inscrire !",select_the_area:"Sélectionnez un élément sur la page.",tell_us_about_your_experience:"Racontez-nous votre expérience...",consent_more_information_url:"https://www.hotjarconsent.com/fr.html",consent_more_information:"En savoir plus",consent:"Lier vos commentaires avec les données relatives à vos visites (appareil, données d'utilisation, cookies, comportement et interactions) nous permettrait d'améliorer votre expérience plus rapidement. Donnez-vous votre accord pour que nous le fassions avec vos visites passées et futures ?"},he:{lang:"he",age:"גיל",change:"שנה",city:"עיר",close:"סגור",dislike:"לא אוהב",email:"דואר אלקטרוני",feedback:"חוות דעת",female:"נקבה",full_name:"שם מלא",hate:"שונא",like:"מחבב",love:"אוהב",male:"זכר",neutral:"בסדר",next:"הבא",phone_number:"טלפון",please_type_here:"הקלד כאן...",powered_by_hotjar:"פועל באמצעות Hotjar",reply:"תגובה",send:"שלח",sent:"נשלח",skip:"דלג",sign_me_up:"הרשם עכשיו!",select_the_area:"בחר חלק בדף",tell_us_about_your_experience:"ספר לנו על החוויה שלך...",consent_more_information:"מידע נוסף",consent:"חיבור המשוב שלך עם נתונים הקשורים לביקורים שלך (ספציפיים למכשיר, נתוני שימוש, קובצי cookie, התנהגות ואינטראקציות) יעזור לנו להשתפר מהר יותר. האם את/ה נותן/ת לנו את הסכמתך לעשות זאת לגבי ביקוריך בעבר ובעתיד?",comment_for_option:"הערה לאופציה"},hr:{lang:"hr",age:"Dob",change:"Promijeni",city:"Mjesto",close:"Zatvori",dislike:"Ne sviđa mi se",email:"Email",feedback:"Povratna informacija",female:"Žensko",full_name:"Ime i prezime",hate:"Izrazito mi se ne sviđa",like:"Sviđa mi se",love:"Obožavam",male:"Muško",neutral:"Neutralan/na sam",next:"Sljedeći",phone_number:"Telefon",please_type_here:"Pišite ovdje",powered_by_hotjar:"powered by Hotjar",reply:"Odgovor",send:"Pošalji",sent:"Poslano",skip:"Preskoči",sign_me_up:"Prijavi me!",select_the_area:"Označite element na stranici.",tell_us_about_your_experience:"Recite nam više o svom iskustvu...",consent_more_information:"Više podataka",consent:"Povjezivanje vaših primjedbi i prijedloga sa podacima u vezi vaših posjeta (po uređaju: podaci o korišćenju, kolačići, ponašanje i interakcije) će nam pomoći u našem bržem unapređenju. Da li nam dajete dozvolu za to uraditi za vaše prijethodne i buduće posjete?"},hu:{lang:"hu",age:"Kor",change:"Változtatás",city:"Település",close:"Bezárás",dislike:"Nem kedvelem",email:"E-mail",feedback:"Visszajelzés",female:"Nő",full_name:"Teljes név",hate:"Utálom",like:"Kedvelem",love:"Imádom",male:"Férfi",neutral:"Közömbös",next:"Következő",phone_number:"Telefon",please_type_here:"Ide írhat...",powered_by_hotjar:"készítette a Hotjar",reply:"Válasz",send:"Küldés",sent:"Elküldve",skip:"Átugrom",sign_me_up:"Regisztrálok!",select_the_area:"Jelöljön ki egy elemet az oldalon.",tell_us_about_your_experience:"Oszd meg velünk véleményedet...",consent_more_information:"Több információ",consent:"Az ön visszajelzésének és a látogatásával kapcsolatos adatainak (eszköztípus, felhasználási adatok, sütik, viselkedés és interakció) összekapcsolásával segíthet nekünk a gyorsabb fejlődésben. Hozzájárul ahhoz, hogy ezt megtegyük az ön előző és a jövőbeni látogatásai alkalmával?"},id:{lang:"id",age:"Umur",change:"Ubah",city:"Kota",close:"Tutup",dislike:"Tidak suka",email:"Email",feedback:"Umpan balik",female:"Wanita",full_name:"Nama lengkap",hate:"Benci",like:"Suka",love:"Sangat suka",male:"Pria",neutral:"Netral",next:"Berikutnya",phone_number:"Nomor telpon",please_type_here:"Silahkan ketik disini...",powered_by_hotjar:"dipersembahkan oleh Hotjar",reply:"Balasan",send:"Kirim",sent:"Terkirim",skip:"Lewati",select_the_area:"Pilih sebuah elemen dalam laman.",sign_me_up:"Daftarkan saya!",tell_us_about_your_experience:"Sampaikan penilaian Anda..."},it:{lang:"it",age:"Età",change:"Cambia",city:"Città",close:"Chiudi",dislike:"Non mi piace",email:"Email",feedback:"Feedback",female:"Femmina",full_name:"Nome e cognome",hate:"Odio",like:"Mi piace",love:"Amo",male:"Maschio",neutral:"Indifferente",next:"Prossima",phone_number:"Telefono",please_type_here:"Scrivi qui...",powered_by_hotjar:"offerto da Hotjar",reply:"Rispondi",send:"Invia",sent:"Inviato",skip:"Salta",select_the_area:"Seleziona un elemento della pagina",sign_me_up:"Iscrivimi!",tell_us_about_your_experience:"Raccontaci la tua esperienza...",consent_more_information_url:"https://www.hotjarconsent.com/it.html",consent_more_information:"Maggiori informazioni",consent:"Collegare questo feedback ai dati relativi alle tue visite (dispositivo, utilizzo, cookie, comportamento e interazioni) ci aiuterà a migliorare più rapidamente. Ci dai il consenso a farlo per visite passate e future?"},ja:{lang:"ja",age:"年齢",change:"変更",city:"市区町村",close:"閉じる",dislike:"悪い",email:"電子メール",feedback:"フィードバック",female:"女性",full_name:"姓名",hate:"非常に悪い",like:"良い",love:"非常に良い",male:"男性",neutral:"どちらでもない",next:"次",phone_number:"電話番号",please_type_here:"こちらにご記入ください...",powered_by_hotjar:"powered by Hotjar",reply:"返信",send:"送信",sent:"送信完了しました",skip:"スキップ",sign_me_up:"登録します",select_the_area:"ページの該当箇所を指定してください",tell_us_about_your_experience:"感想を聞かせてください",consent_more_information:"さらに詳しく",consent:"あなたのフィードバックとサイト訪問に関連するデータ（デバイスの仕様、利用時間データ、クッキー、行動、相互作用）を結び付けることが、私たちの改善スピードをより速くする助けとなります。あなたの以前の訪問と今後の訪問について、そのようにすることを承諾していただけますか？"},ko:{lang:"ko",age:"나이",change:"변화",city:"도시",close:"닫기",dislike:"싫어함",email:"이메일",feedback:"피드백",female:"여자",full_name:"이름",hate:"미움",like:"처럼",love:"애정",male:"남자",neutral:"중립국",next:"다음",phone_number:"전화번호",please_type_here:"여기에 입력해주세요",powered_by_hotjar:"powered by Hotjar",reply:"답변하기",send:"보내기",sent:"보냈습니다",skip:"버킷",sign_me_up:"참여하기!",select_the_area:"페이지에서 요소를 선택하십시오.",tell_us_about_your_experience:"귀하의 경험에 대해 알려주십시오...",consent_more_information:"더 알아보기",consent:"귀하의 피드백을 방문과 관련된 데이터 (장치별, 사용 데이터, 쿠키, 동작 및 상호 작용)와 연결해 주시면, 저희가 더 빨리 발전할 수 있습니다. 귀하의 이전과 미래의 방문에 대해 그렇게 해 주시는 것에 동의하시겠습니까?"},lt:{lang:"lt",age:"Amžius",change:"Keisti",city:"Miestas",close:"Uždaryti",dislike:"Nepatinka",email:"El. paštas",feedback:"Atsiliepimai",female:"Moteris",full_name:"Pilnas vardas",hate:"Nekenčiu",like:"Patinka",love:"Puiku",male:"Vyras",neutral:"Be nuomonės",next:"Kitas",phone_number:"Telefonas",please_type_here:"Rašyti čia...",powered_by_hotjar:"powered by Hotjar",reply:"Atsakyti",send:"Siųsti",sent:"Išsiusta",skip:"Praleisti",sign_me_up:"Registruotis",select_the_area:"Pažymėkite laukelį puslapyje.",tell_us_about_your_experience:"Parašykite atsiliepimą...",consent_more_information:"Daugiau informacijos",consent:"Jūsų atsiliepimas ir duomenys susiję su jūsų apsilankymais (konkretūs įrenginiai, naudojimo duomenys, slapukai, elgesys ir veiksmai) padės mums tobulėti greičiau. Ar sutinkate su tokių duomenų issaugojimu jūsų praeities ir ateities apsilankymams."},lv:{lang:"lv",age:"Vecums",change:"Mainīt",city:"Pilsēta",close:"Aizvērt",dislike:"Nepatika",email:"E-pasts",feedback:"Atsauksme",female:"Sieviete",full_name:"Pilns vārds",hate:"Ienīstu",like:"Patika",love:"Mīlu",male:"Vīrietis",neutral:"Neitrāla",next:"Nākamais",phone_number:"Tālruņa numurs",please_type_here:"Lūdzu, ievadiet šeit...",powered_by_hotjar:"powered by Hotjar",reply:"Atbilde",select_the_area:"Izvēlies elementu lapā.",send:"Nosūtīt",sent:"Nosūtīts",skip:"Izlaist",sign_me_up:"Pierakstiet mani!",tell_us_about_your_experience:"Pastāsti mums par savu pieredzi..."},mis:{lang:"mis",age:"Starost",city:"Grad",close:"Zatvori",email:"Email",female:"Žensko",full_name:"Ime i prezime",male:"Muško",next:"Sljedeći",phone_number:"Telefon",please_type_here:"Pišite ovdje...",powered_by_hotjar:"powered by Hotjar",reply:"Odgovori",send:"Pošalji",sent:"Poslato",sign_me_up:"Prijavi me!",consent_more_information:"Više podataka",consent:"Povjezivanje vaših primjedbi i prijedloga sa podacima u vezi vaših posjeta (po uređaju: podaci o korišćenju, kolačići, ponašanje i interakcije) će nam pomoći da se brže unaprijedimo. Da li nam dajete dozvolu da to uradimo za vaše prijethodne i buduće posjete?"},nb:{lang:"nb",age:"Alder",change:"Endre",city:"Sted",close:"Lukk",dislike:"Liker ikke",email:"E-post",feedback:"Tilbakemelding",female:"Kvinne",full_name:"Navn",hate:"Hater",like:"Liker",love:"Elsker",male:"Mann",neutral:"Nøytral",next:"Neste",phone_number:"Telefon",please_type_here:"Skriv her...",powered_by_hotjar:"powered by Hotjar",reply:"Svar",send:"Send",sent:"Sendt",skip:"Hopp over",sign_me_up:"Delta!",select_the_area:"Velg et element på siden.",tell_us_about_your_experience:"Fortell oss om din opplevelse",consent_more_information:"Mer informasjon",consent:"Ved å knytte tilbakemeldingen din med data som er relatert til besøkene dine (enhetsspesifikt, bruksdata, informasjonskapsler, atferd og samhandlinger), hjelper det oss med å bli bedre raskere. Gir du oss ditt samtykke til å gjøre det med dine tidligere og fremtidige besøk?"},nl:{lang:"nl",age:"Leeftijd",change:"Wijzigen",city:"Plaats",close:"Sluiten",dislike:"Matig",email:"E-mailadres",feedback:"Feedback",female:"Vrouw",full_name:"Volledige naam",hate:"Slecht",like:"Goed",love:"Geweldig",male:"Man",neutral:"Neutraal",next:"Volgende",phone_number:"Telefoonnummer",please_type_here:"Jouw bericht...",powered_by_hotjar:"ondersteund door Hotjar",reply:"Reageer",send:"Verstuur",sent:"Verstuurd",skip:"Overslaan",sign_me_up:"Schrijf me in!",select_the_area:"Selecteer een element op de pagina.",tell_us_about_your_experience:"Vertel ons over je ervaring...",consent_more_information_url:"https://www.hotjarconsent.com/nl.html",consent_more_information:"Meer informatie",consent:"Door het verbinden van uw feedback met data die verband houdt met uw bezoek aan de site (specifiek voor een apparaat, gebruiksdata, cookies, gedrag en interacties) kunnen we sneller verbeteringen aanbrengen. Geeft u ons toestemming om dit te doen voor uw bezoeken in het verleden en in de toekomst?"},pl:{lang:"pl",age:"Wiek",change:"Zmień",city:"Miasto",close:"Zamknij",dislike:"Źle",email:"Email",feedback:"Opinia",female:"Kobieta",full_name:"Imię i nazwisko",hate:"Okropnie",like:"Dobrze",love:"Świetnie",male:"Mężczyzna",neutral:"Neutralnie",next:"Dalej",phone_number:"Numer telefonu",please_type_here:"Wpisz wiadomość...",powered_by_hotjar:"powered by Hotjar",reply:"Odpowiedz",send:"Wyślij",sent:"Wysłano",skip:"Pomiń",sign_me_up:"Zarejestruj mnie!",select_the_area:"Zaznacz element na stronie.",tell_us_about_your_experience:"Podziel się z nami swoją opinią...",consent_more_information_url:"https://www.hotjarconsent.com/pl.html",consent_more_information:"Więcej informacji",consent:"Łączenie Twoich odpowiedzi z informacjami o Twoich wizytach na stronie (dot. używanego urządzenia, sposobu użytkowania strony, plików cookie, zachowania i interakcji) pozwoli nam na szybsze udoskonalenie się. Czy wyrażasz zgodę na łączenie tych danych z Twoich poprzednich i przyszłych wizyt?"},pt:{lang:"pt",age:"Idade",change:"Alterar",city:"Cidade",close:"Fechar",dislike:"Não gosto",email:"Email",feedback:"Feedback",female:"Feminino",full_name:"Nome completo",hate:"Odeio",like:"Gosto",love:"Adoro",male:"Masculino",neutral:"Neutro",next:"Seguinte",phone_number:"Telemóvel",please_type_here:"Por favor, escreva aqui ...",powered_by_hotjar:"powered by Hotjar",reply:"Responder",send:"Enviar",sent:"Enviado",skip:"Ignorar",sign_me_up:"Quero Registar-me!",select_the_area:"Selecione um elemento da página.",tell_us_about_your_experience:"Fale-nos da sua experiência",consent_more_information_url:"https://www.hotjarconsent.com/pt.html",consent_more_information:"Saber mais",consent:"Ao associar o seu feedback aos dados das suas visitas (dispositivo, dados de utilização, cookies, comportamento e interações) ajuda-nos a melhorar a sua experiência com mais rapidez. Para o continuarmos a fazer, precisamos do seu consentimento relativo a visitas anteriores e futuras."},pt_BR:{lang:"pt-BR",age:"Idade",change:"Trocar",city:"Cidade",close:"Fechar",dislike:"Não gostei",email:"Email",feedback:"Feedback",female:"Feminino",full_name:"Nome completo",hate:"Odiei",like:"Gostei",love:"Amei",male:"Masculino",neutral:"Neutra",next:"Seguinte",phone_number:"Telefone",please_type_here:"Por favor, escreva aqui...",powered_by_hotjar:"powered by Hotjar",reply:"Responder",send:"Enviar",sent:"Enviado",skip:"Pular",sign_me_up:"Inscreva-se!",select_the_area:"Selecione um elemento na página.",tell_us_about_your_experience:"Conte-nos sobre a sua experiência...",consent_more_information_url:"https://www.hotjarconsent.com/pt_br.html",consent_more_information:"Saiba mais",consent:"Associar o seu feedback aos dados das suas visitas (dispositivo, dados de uso, cookies, comportamento e interações) nos ajuda a melhorar a sua experiência com muito mais rapidez. Você nos dá o seu consentimento para associarmos os dados de suas visitas prévias e futuras?"},ro:{lang:"ro",age:"Vârsta",change:"Schimbă",city:"Oraș",close:"Închide",dislike:"Nu-mi place",email:"Email",feedback:"Feedback",female:"Femeie",full_name:"Nume complet",hate:"Îl urăsc",like:"Îmi place",love:"Îl iubesc",male:"Bărbat",neutral:"Neutru",next:"Următoarea",phone_number:"Telefon",please_type_here:"Scrie aici...",powered_by_hotjar:"powered by Hotjar",reply:"Răspunde",send:"Trimite",sent:"Trimis",skip:"Treci peste",sign_me_up:"Mă înscriu",select_the_area:"Selectează un element de pe pagină.",tell_us_about_your_experience:"Spune-ne despre experiența ta...",consent_more_information:"Mai multe informații",consent:"Conectarea observațiilor și opiniilor dvs. cu datele rezultate din vizitele dvs. (dispozitive folosite, date de utilizare, fișiere cookie, comportament și interacțiuni) ne va ajuta să ne îmbunătățim serviciile mai rapid. Ne acordați consimțământul dvs. pentru a face acest lucru atât pentru vizitele dvs. anterioare, cât și pentru cele viitoare?"},ru:{lang:"ru",age:"Возраст",change:"Изменить",city:"Город",close:"Закрыть",dislike:"Не нравится",email:"Email",feedback:"Обратная связь",female:"Женщина",full_name:"Полное имя",hate:"Ненавижу",like:"Нравится",love:"Обожаю",male:"Мужчина",neutral:"Нейтрально",next:"следующий",phone_number:"Номер телефона",please_type_here:"Место для ввода...",powered_by_hotjar:"powered by Hotjar",reply:"Ответить",send:"Отправить",sent:"Отправлено",skip:"Пропустить",sign_me_up:"Подписаться!",select_the_area:"Выделите элемент на странице.",tell_us_about_your_experience:"Расскажите о вашем опыте...",consent_more_information_url:"https://www.hotjarconsent.com/ru.html",consent_more_information:"Дополнительная информация",consent:"Связь ваших отзывов с данными о посещении вами сайта (данные об устройстве, сведения об использовании, файлы cookie, поведение и активность на сайте) поможет нам быстрее улучшить наши услуги. Даете ли вы нам свое согласие на использование данных о ваших предыдущих и будущих посещениях сайта?"},sk:{lang:"sk",age:"Vek",change:"Zmeniť",city:"Mesto",close:"Zavrieť",dislike:"Nepáči",email:"Email",feedback:"Váš názor",female:"Žena",full_name:"Celé meno",hate:"Neznášam",like:"Páči sa",love:"Milujem",male:"Muž",neutral:"Neutral",next:"Ďalšie",phone_number:"Telefónne číslo",please_type_here:"Začnite písať tu",powered_by_hotjar:"powered bz Hotjar",reply:"Odpovedať",send:"Poslať",sent:"Poslané",skip:"Preskočiť",sign_me_up:"Vytvoriť konto",select_the_area:"Vyberte element na stránke.",tell_us_about_your_experience:"Napíšte nám vašu skúsenosť...",consent_more_information:"Pre viac informácií kliknite sem",consent:"Prepojenie Vašej spätnej väzby s dátami súvisiacimi s Vašimi návštevami (špecifikácia zariadenia, využitie dát, správanie a interakcie) nám pomôže rýchlejšie sa zlepšovať. Dáte nám súhlas na to, aby sme tak spravili v prípade Vašich minulých aj budúcich návštev?"},sl:{lang:"sl",age:"Starost",change:"Spremeni",city:"Kraj",close:"Zapri",dislike:"Ni mi všeč",email:"Email",feedback:"Povratna informacija",female:"Ženska",full_name:"Ime in priimek",hate:"Sovražim",like:"Všeč mi je",love:"Obožujem",male:"Moški",neutral:"Vseeno mi je",next:"Naslednji",phone_number:"Telefon",please_type_here:"Prosimo vnesite tukaj...",powered_by_hotjar:"powered by Hotjar",reply:"Odgovori",send:"Pošlji",sent:"Poslano",skip:"Preskoči",sign_me_up:"Prijavi me!",select_the_area:"Izberi element na strani.",tell_us_about_your_experience:"Deli svoje mnenje z nami..."},sq:{lang:"sq",age:"Mosha",change:"Ndrysho",city:"Qyteti",close:"Mbyll",dislike:"Keq",email:"Email",feedback:"Mendimi juaj",female:"Femër",full_name:"Emri i plotë",hate:"Shumë keq",like:"Mirë",love:"Shume mirë",male:"Mashkull",neutral:"Neutrale",next:"Tjetër",phone_number:"Numër telefoni",please_type_here:"Ju lutem shkruani këtu...",powered_by_hotjar:"powered by Hotjar",reply:"Përgjigju",send:"Dërgo",sent:"Te dërguara",skip:"Kalo",sign_me_up:"Më regjistro!",select_the_area:"Zgjidhni një element nga faqja.",tell_us_about_your_experience:"Na tregoni rreth përvojës tuaj...",consent_more_information_url:"https://www.hotjarconsent.com/sq.html",consent_more_information:"Më shumë informacion",consent:"Lidhja midis vlerwsimit tuaj dhe infromacioneve qw kanw lidhje (nw lidhje) me vizitat tuaja(device-specific,…) do tw na ndihmonin tw permisohemi akoma mw shpejt. A do tw na jepni aprovimin tuaj pwr tw bwrw kwtw me vizitat tuaja tw mwparshme dhe me ato nw tw ardhmen?"},sr:{lang:"sr",age:"Starost",change:"Promeni",city:"Grad",close:"Zatvori",dislike:"Ne sviđa mi se",email:"Email",feedback:"Povratna informacija",female:"Žensko",full_name:"Ime i prezime",hate:"Baš mi se ne sviđa",like:"Sviđa mi se",love:"Obožavam",male:"Muško",neutral:"Svejedno mi je",next:"Sledeći",phone_number:"Telefon",please_type_here:"Pišite ovde...",powered_by_hotjar:"powered by Hotjar",reply:"Odgovori",send:"Pošalji",sent:"Poslato",skip:"Preskoči",sign_me_up:"Prijavi me!",select_the_area:"Označite element na stranici.",tell_us_about_your_experience:"Podelite vaše mišljenje sa nama...",consent_more_information:"Više podataka",consent:"Povezivanje vaših primedbi i predloga sa podacima u vezi vaših poseta (po uređaju: podaci o korišćenju, kolačići, ponašanje i interakcije) će nam pomoći da se brže unapredimo. Da li nam dajete dozvolu da to uradimo za vaše prethodne i buduće posete?"},sv:{lang:"sv",age:"Ålder",change:"Ändra",city:"Stad",close:"Stäng",dislike:"Ogillar",email:"Email",feedback:"Feedback",female:"Kvinna",full_name:"Namn",hate:"Hatar",like:"Gillar",love:"Älskar",male:"Man",neutral:"Neutral",next:"Nästa",phone_number:"Telefon",please_type_here:"Skriv här...",powered_by_hotjar:"powered by Hotjar",reply:"Besvara",send:"Skicka",sent:"Skickat",skip:"Hoppa över",sign_me_up:"Registrera mig!",select_the_area:"Markera ett element på sidan.",tell_us_about_your_experience:"Berätta om din upplevelse",consent_more_information_url:"https://www.hotjarconsent.com/sv.html",consent_more_information:"Mer information",consent:"Att koppla din feedback med data förknippade med dina besök (enhetsspecifik, användningsdata, cookies, beteende och interaktioner) hjälper oss att bli bättre snabbare. Ger du oss ditt tillstånd att göra detta för dina tidigare och framtida besök?"},sw:{lang:"sw",age:"Umri",change:"Badili",city:"Mji",close:"Funga",dislike:"Sipendi",email:"Barua pepe",feedback:"Mrejesho",female:"Mwanamke",full_name:"Jina kamili",hate:"Naichukia",like:"Naikubali",love:"Naipenda",male:"Mwanaume",neutral:"Sijui",next:"Ifuatayo",phone_number:"Namba ya simu",please_type_here:"Tafadhali andika hapa...",powered_by_hotjar:"powerered bt Hotjar",reply:"Jibu",send:"Tuma",sent:"Imetumwa",skip:"Ruka",sign_me_up:"Niunganishe!",select_the_area:"Chagua kipengele kwenye ukurasa.",tell_us_about_your_experience:"Tuambie kuhusu uzoefu wako...",consent_more_information:"Maelezo zaidi",consent:"Kuhusisha maoni yako na data inayohusiana na ziara zako (kifaa unachotumia, data ya utumizi, mwenendo na jinsi ya matumizi) itatusaidia kujiboresha kwa kasi zaidi. Je, unatupa idhini yako kufanya hivyo kwa ziara zako za awali na zijazo?"},th:{lang:"th",age:"อายุ",change:"เปลี่ยน",city:"เมือง",close:"ปิด",dislike:"ไม่ชอบ",email:"อีเมล",feedback:"ฟีดแบ็ค",female:"หญิง",full_name:"ชื่อ-นามสกุล",hate:"เกลียด",like:"ชอบ",love:"รัก",male:"ชาย",neutral:"เฉยๆ",next:"ต่อไป",phone_number:"เบอร์โทรศัพท์",please_type_here:"โปรดพิมพ์ที่นี่...",powered_by_hotjar:"สนับสนุนโดย Hotjar",reply:"ตอบ",send:"ส่ง",sent:"ส่ง",skip:"ข้าม",sign_me_up:"ลงทะเบียน",select_the_area:"เลือกองค์ประกอบบนหน้าเว็บ",tell_us_about_your_experience:"บอกให้เราทราบเกี่ยวกับประสบการณ์ของคุณ...",consent_more_information:"ข้อมูลเพิ่มเติม",consent:"การเชื่อมโยงข้อเสนอแนะของคุณกับข้อมูลที่เกี่ยวข้องกับการเข้าชมของคุณ (เจาะจงอุปกรณ์, คุกกี้, พฤติกรรม และการโต้ตอบ) จะช่วยให้เราปรับปรุงได้อย่างรวดเร็วยิ่งขึ้น คุณต้องการยินยอมให้เรากระทำการดังกล่าวสำหรับการเข้าชมของคุณในก่อนหน้านี้และในอนาคตหรือไม่"},tl:{lang:"tl",age:"Edad",change:"Baguhin",city:"Lunsod",close:"Isara",dislike:"Hindi gusto",email:"E-mail",feedback:"Feedback",female:"Babae",full_name:"Buong Pangalan",hate:"Poot",like:"Gaya ng",love:"Pag-ibig",male:"Lalaki",neutral:"Neutral",next:"Susunod",phone_number:"Telepono",please_type_here:"Dito po magsimulang magsulat...",powered_by_hotjar:"iniabot sa inyo ng Hotjar",reply:"Tumugon",send:"Ipadala",sent:"Naipadala",skip:"Laktawan",sign_me_up:"I-rehistro nyo ako!",select_the_area:"Pumili ng isang elemento sa pahina.",tell_us_about_your_experience:"Sabihin sa amin ang tungkol sa iyong karanasan...",consent_more_information:"Karagdagang impormasyon",consent:"Ang pag-uugnay ng iyong mga komento sa mga datos na may kaugnayan sa iyong mga pagbisita (para sa espesipikong aparato, mga datos sa paggamit, mga cookies, pag-uugali at pakikipag-ugnayan) ay makakatulong sa amin na humusay nang mas mabilis. Ibinibigay mo ba sa amin ang iyong pahintulot na gawin ito para sa iyong mga nakaraan at darating pang pagbisita?"},tr:{lang:"tr",age:"Yaş",change:"Değiştir",city:"Şehir",close:"Kapat",dislike:"Beğenmedim",email:"E-posta",feedback:"Geribildirim",female:"Kadın",full_name:"İsim",hate:"Hiç beğenmedim",like:"Beğendim",love:"Çok beğendim",male:"Erkek",neutral:"Bir fikrim yok",next:"Sonraki",phone_number:"Telefon Numaranız",please_type_here:"Cevabınızı buraya giriniz...",powered_by_hotjar:"powered by Hotjar",reply:"Cevapla",send:"Gönder",sent:"Gönderildi",skip:"Atla",sign_me_up:"Kayıt ol!",select_the_area:"Sayfadaki bir alanı seç.",tell_us_about_your_experience:"Yaşadığın deneyimi bizimle paylaşır mısın?"},uk:{lang:"uk",age:"Вік",change:"Змінити",city:"Місто",close:"Закрити",dislike:"Не подобається",email:"Email",feedback:"Зворотній зв'язок",female:"Жінка",full_name:"Повне ім'я",hate:"Ненавиджу",like:"Подобається",love:"Обожнюю",male:"Чоловік",neutral:"Нейтрально",next:"наступний",phone_number:"Номер телефону",please_type_here:"Місце для вводу...",powered_by_hotjar:"powered by Hotjar",reply:"Відповісти",send:"Надіслати",sent:"Надіслано",skip:"Пропустити",sign_me_up:"Підписатися!",select_the_area:"Вкажіть елемент на сторінці.",tell_us_about_your_experience:"Розкажіть нам про свій досвід...",consent_more_information:"Більше інформації",consent:"Поєднання ваших відгуків із даними, пов'язаними з вашими візитами (дані про окремі пристрої, користування, файли-реп'яшки, поведінку та взаємодії), допоможе нам покращуватись швидше. Ви дозволяєте робити це щодо ваших попередніх та майбутніх візитів?"},vi:{lang:"vi",age:"Tuổi",change:"Thay đổi",city:"Thành phố",close:"Đóng",dislike:"Không thích",email:"Email",feedback:"Phản hồi",female:"Nữ",full_name:"Tên đầy đủ",hate:"Ghét",like:"Thích",love:"Rất thích",male:"Nam",neutral:"Không có ý kiến",next:"Kế tiếp",phone_number:"Điện thoại",please_type_here:"Vui lòng nhập nội dung tại đây...",powered_by_hotjar:"powered by Hotjar",reply:"Trả lời",send:"Gởi",sent:"Đã gởi",skip:"Bỏ qua",sign_me_up:"Đăng ký!",select_the_area:"Chọn một phần trên website",tell_us_about_your_experience:"Chia sẻ cảm nhận của bạn...",consent_more_information:"Thêm thông tin",consent:"Kết nối phản hồi của bạn với dữ liệu liên quan đến các lần truy cập của bạn (thiết bị cụ thể, dữ liệu sử dụng, cookie, hành vi và tương tác) sẽ giúp chúng tôi cải thiện nhanh hơn. Bạn có đồng ý cho chúng tôi làm như vậy cho các lần truy cập trước và trong tương lai của bạn không?"},zh_CN:{lang:"zh-CN",age:"年龄",change:"改变",city:"城市",close:"关闭",dislike:"不喜欢",email:"电子邮箱",feedback:"反馈",female:"女",full_name:"姓名",hate:"讨厌",like:"喜欢",love:"大爱",male:"男",neutral:"中立",next:"下一个",phone_number:"电话",please_type_here:"请输入...",powered_by_hotjar:"由Hotjar呈现",reply:"回复",send:"发送",sent:"已发达",skip:"跳过",sign_me_up:"我要参加!",select_the_area:"选择一个页面元素",tell_us_about_your_experience:"请告知您的体验...",consent_more_information_url:"https://www.hotjarconsent.com/zh.html",consent_more_information:"更多信息",consent:"为了运营和改善Hotjar的技术和服务，Hotjar 希望将您的反馈与您的访问相关数据相结合。您是否同意我们收集您在过去以及未来访问过程中产生的访问数据(包括设备信息、使用数据、Cookies、行为和互动数据）？"},zh_TW:{lang:"zh-TW",age:"年齡",change:"改變",city:"城市",close:"關閉",dislike:"不喜歡",email:"Email",feedback:"回饋",female:"女",full_name:"名稱",hate:"非常不喜歡",like:"喜歡",love:"非常喜歡",male:"男",neutral:"中立",next:"下一個",phone_number:"電話",please_type_here:"請輸入...",powered_by_hotjar:"powered by Hotjar",reply:"回覆",send:"送出",sent:"已送出",skip:"跳過",sign_me_up:"我要參加!",select_the_area:"選擇一個頁面區域",tell_us_about_your_experience:"請告知您的體驗...",consent_more_information:"更多資訊",consent:"將您的意見反應與您的造訪相關資料（所用的裝置、使用狀況資料、Cookie、行為與互動）做連結，將有助於我們更快速改善。您同意我們就您先前與之後的造訪來這麼做嗎？"}})))throw new Error('Invalid language "'+n+'"');e=t[n],hj.widget.activeLanguageDirection=o.indexOf(n)>-1?"rtl":"ltr",hj.widget.isActiveLanguageDirectionRtl="rtl"===hj.widget.activeLanguageDirection}),"common"),n),hj.widgetDelay=function(){var e={},t=null;return e.clear=hj.tryCatch((function(){clearTimeout(t),t=null}),"hj.widgetDelay.clear"),e.set=hj.tryCatch((function(n,r){e.clear(),t=setTimeout(n,r)}),"hj.widgetDelay.set"),e}()}),"widgets")()},function(e,t,n){"use strict";n.r(t);var r=n(56),i=n(4);hj.tryCatch((function(){hj.loader.registerModule("Feedback",function(){var e={loaded:!1};function t(){Object(r.a)(i.f.PREACT_INCOMING_FEEDBACK)}function n(){Object(r.a)(i.f.INCOMING_FEEDBACK)}function o(){var r=arguments.length>0&&void 0!==arguments[0]&&arguments[0];e.loaded&&!r||(e.loaded=!0,hj.widget.setLanguage(hj.widget.feedbackData.language),hj.features.hasFeature("feedback.widgetV2")?t():n())}function a(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];e.loaded&&!t||(e.loaded=!0,hj.widget.setLanguage(hj.widget.embeddedFeedbackWidgets[0].language),Object(r.a)(i.f.PREACT_INCOMING_FEEDBACK))}function s(t){var n=(hj.settings.feedback_widgets||[]).filter((function(e){var t=e.display_type;return"button"===(void 0===t?"button":t)}));hj.hq.each(n,(function(n,r){hj.targeting.matchRules(r.targeting,t,hj.tryCatch((function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];hj.log.debug("Feedback widget #"+r.id+" has matched.","feedback"),!hj.widget.data||hj.widget.data.id===r.id&&"incoming"===hj.widget.data.type?(r.created_epoch_time-=31536e4,hj.widget.addMatchingWidget("incoming",r.id,r.created_epoch_time,r.targeting_percentage,(function(){hj.widget.feedbackData=r,hj.tryCatch(o,"feedback-button-load")(t)}),e.remove)):hj.log.debug("Another feedback widget is already present.","feedback")}),"feedback.run.matchRules-callback"))}))}function c(e){var t=(hj.settings.feedback_widgets||[]).filter((function(e){return"inline"===e.display_type})).filter((function(t){return hj.targeting.matchRules(t.targeting,e,(function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];void 0!==hj.widget.embeddedFeedbackWidgets&&(hj.widget.embeddedFeedbackWidgets.push(t),hj.tryCatch(a,"feedback-embeddable-load")(e))}))}));hj.widget.embeddedFeedbackWidgets=t,t.length>0&&hj.tryCatch(a,"feedback-embeddable-load")()}return e.run=hj.tryCatch((function(t){e.loaded=!1,hj.tryCatch(s,"feedback-button")(t),hj.tryCatch(c,"feedback-embeddable")(t)}),"feedback"),e.remove=hj.tryCatch((function(e){hj.widget.feedbackData?(hj.hq("#_hj_feedback_container").length>0&&hj.hq("#_hj_feedback_container").parent().remove(),delete hj.widget.feedbackData,hj.hq(window).off("resize"),setTimeout((function(){e()}),1)):e()})),hj.isPreview&&(window._hjFeedbackReload=hj.tryCatch((function(e,t){hj.widget.feedbackData=e,hj.settings.legal_name=e.legal_name,hj.settings.privacy_policy_url=e.privacy_policy_url,t&&(hj.settings.features=t),hj.tryCatch(o,"feedback")(!0)}),"feedback")),e}(),!0)}),"feedback")()},function(e,t,n){"use strict";n.r(t);var r=n(156),i=n(56),o=n(4),a=n(28),s=n(6),c=n(17),u=n(51),l=n(26);hj.tryCatch((function(){hj.loader.registerModule("Polls",function(){var e,t={};function n(e,t){!function(e){if(hj.surveyImpressionsEndpoint&&!hj.isPreview){var t="".concat(hj.surveyImpressionsEndpoint,"?id=").concat(e,"&device=").concat(Object(c.a)());hj.log.debug("poll id: ".concat(e," recording impression."),"poll"),hj.ajax.get(t)}else hj.log.debug("poll id: ".concat(e," skipping recording impression."),"poll")}(e.id);var n={scriptSrc:"".concat(hj.scriptDomain).concat(o.f.SURVEY_ISOLATED.js)};hj.widget.renderSurvey(e,t,n)}function h(e){return hj.features.hasFeature("survey.iframe.".concat(e.display_type))?o.f.SURVEY_BOOTSTRAPPER:o.f.SURVEY_V2}function d(t,r){hj.widget.pollResponseUUID=Object(l.a)(),hj.widget.setLanguage(hj.widget.pollData.language),hj.log.debug("Rendering poll widget.","poll");var o=h(hj.widget.pollData);o!==e?Object(i.a)(h(hj.widget.pollData),(function(){e=o,n(hj.widget.pollData,r)})):n(hj.widget.pollData,r)}var p=hj.tryCatch((function(e){var t=JSON.parse(JSON.stringify(e));return function(e){hj.hq.each(e.content.questions,(function(e,t){t.answers&&hj.hq.each(t.answers,(function(e,t){t.index=e}))}))}(t),function(e){hj.hq.each(e.content.questions,(function(e,t){t.randomize_answer_order&&(t.pin_last_to_bottom?t.answers=Object(c.h)(t.answers.slice(0,-1)).concat(t.answers.slice(-1)):Object(c.h)(t.answers))}))}(t),t}),"polls");return t.add=hj.tryCatch((function(e){hj.widget.pollData=p(e),hj.tryCatch(hj.rendering.callAccordingToCondition,"polls")(hj.widget.pollData,"poll",d)}),"polls"),t.addEmbedded=hj.tryCatch((function(e,n){hj.widget.emptyMatchingWidgets();var r="external_link"===e.display_type?void 0:e.targeting_percentage;hj.widget.addMatchingWidget("poll",e.id,e.created_epoch_time,r,(function(){var t=p(e);t.skin="light",t.background="#ffffff",s.a.items.POLL_DONE.exists(e.id)&&"always"!==e.persist_condition&&(hj.log.debug("Offsite poll "+e.id+" was already submitted.","poll"),t.is_submitted=!0),hj.widget.pollData=t,d(0,n[0]),"function"==typeof window.hjRenderCallback&&window.hjRenderCallback({background:e.background})}),t.remove)}),"polls"),t.remove=hj.tryCatch((function(e){hj.widget.pollData?(Object(u.c)(document.body),Object(u.d)(document.body),delete hj.widget.pollData,setTimeout((function(){e()}),1)):e()}),"polls"),t.run=hj.tryCatch((function(e){var n=hj.hq("._hj-survey-embed-container"),i=n.attr("data-survey-id"),o=Object(r.b)(e),c=!1;hj.hq.each(hj.settings.polls||[],(function(r,u){var l=u.display_type===a.a.EXTERNAL,h=u.display_type===a.a.POPOVER||u.display_type===a.a.FULL_SCREEN;n.length>0&&l?u.uuid===i&&(hj.log.debug("Offsite poll #"+u.id+" has matched with the embedded UUID "+i,"poll"),c=!0,t.addEmbedded(u,n)):!o&&h&&hj.targeting.matchRules(u.targeting,e,hj.tryCatch((function(){hj.log.debug("Poll #"+u.id+" has matched.","poll"),s.a.items.POLL_DONE.exists(u.id)&&"always"!==u.persist_condition?hj.log.debug("Poll was already submitted.","poll"):hj.widget.addMatchingWidget("poll",u.id,u.created_epoch_time,u.targeting_percentage,(function(){return t.add(u)}),t.remove)}),"polls.run.matchRules-callback"))})),o&&!c&&(hj.hq(document).trigger("hj-embedded-survey-mismatch"),hj.widgetDelay.clear(),hj.widget.emptyMatchingWidgets(),hj.log.debug("Could not match the embedded UUID.","poll"))}),"polls"),hj.isPreview&&(window._hjPollReload=hj.tryCatch((function(e,t){hj.widget.pollData=p(e),hj.settings.legal_name=e.legal_name,hj.settings.privacy_policy_url=e.privacy_policy_url,t&&(hj.settings.features=t),hj.tryCatch(d,"polls")()}),"polls")),t}(),!0)}),"polls")()},function(e,t,n){"use strict";n.r(t);var r=n(6),i=n(56),o=n(4),a=n(25),s=n(28),c=n(156);hj.tryCatch((function(){hj.loader.registerModule("Surveys",function(){var e={};function t(e){return e.display_type===s.a.EXTERNAL?Object(c.a)(e.uuid,hj.surveysHost):e.public_url}return e.run=hj.tryCatch((function(n){if(!Object(c.b)(n)){var a=(hj.settings.polls||[]).filter((function(e){return e.invite_enabled&&e.display_type===s.a.EXTERNAL})),u=(hj.settings.surveys||[]).concat(a);hj.hq.each(u||[],(function(a,s){hj.targeting.matchRules(s.targeting,n,hj.tryCatch((function(){hj.log.debug("Survey #"+s.id+" has matched.","survey"),r.a.items.SURVEY_INVITES_CLOSED.exists(s.id)?hj.log.debug("Survey was already viewed.","survey"):hj.widget.addMatchingWidget("survey",s.id,s.created_epoch_time,s.targeting_percentage,(function(){hj.survey.data=s,hj.rendering.callAccordingToCondition(hj.survey.data,"survey",(function(){var e;e=hj.survey.data,hj.widget.surveyInvitationData={id:e.id,effectiveShowBranding:e.effective_show_branding,title:e.invite.title,description:e.invite.description,button:e.invite.button,close:e.invite.close,url:t(e)},Object(i.a)(o.f.SURVEY_INVITATION)}))}),e.remove)}),"surveys.run.matchRules-callback"))}))}}),"surveys"),e.remove=hj.tryCatch((function(e){hj.survey.data?(hj.hq(".".concat(a.b.SURVEY_INVITES)).length>0&&hj.hq(".".concat(a.b.SURVEY_INVITES)).remove(),delete hj.survey.data,setTimeout((function(){e()}),1)):e()})),e}(),!0)}),"surveys")()},function(e,t,n){var r,i,o;!function(n,a){"use strict";i=[],void 0===(o="function"==typeof(r=function(){function e(e){return e.charAt(0).toUpperCase()+e.substring(1)}function t(e){return function(){return this[e]}}var n=["isConstructor","isEval","isNative","isToplevel"],r=["columnNumber","lineNumber"],i=["fileName","functionName","source"],o=n.concat(r,i,["args"],["evalOrigin"]);function a(t){if(t)for(var n=0;n<o.length;n++)void 0!==t[o[n]]&&this["set"+e(o[n])](t[o[n]])}a.prototype={getArgs:function(){return this.args},setArgs:function(e){if("[object Array]"!==Object.prototype.toString.call(e))throw new TypeError("Args must be an Array");this.args=e},getEvalOrigin:function(){return this.evalOrigin},setEvalOrigin:function(e){if(e instanceof a)this.evalOrigin=e;else{if(!(e instanceof Object))throw new TypeError("Eval Origin must be an Object or StackFrame");this.evalOrigin=new a(e)}},toString:function(){var e=this.getFileName()||"",t=this.getLineNumber()||"",n=this.getColumnNumber()||"",r=this.getFunctionName()||"";return this.getIsEval()?e?"[eval] ("+e+":"+t+":"+n+")":"[eval]:"+t+":"+n:r?r+" ("+e+":"+t+":"+n+")":e+":"+t+":"+n}},a.fromString=function(e){var t=e.indexOf("("),n=e.lastIndexOf(")"),r=e.substring(0,t),i=e.substring(t+1,n).split(","),o=e.substring(n+1);if(0===o.indexOf("@"))var s=/@(.+?)(?::(\d+))?(?::(\d+))?$/.exec(o,""),c=s[1],u=s[2],l=s[3];return new a({functionName:r,args:i||void 0,fileName:c,lineNumber:u||void 0,columnNumber:l||void 0})};for(var s=0;s<n.length;s++)a.prototype["get"+e(n[s])]=t(n[s]),a.prototype["set"+e(n[s])]=function(e){return function(t){this[e]=Boolean(t)}}(n[s]);for(var c=0;c<r.length;c++)a.prototype["get"+e(r[c])]=t(r[c]),a.prototype["set"+e(r[c])]=function(e){return function(t){if(n=t,isNaN(parseFloat(n))||!isFinite(n))throw new TypeError(e+" must be a Number");var n;this[e]=Number(t)}}(r[c]);for(var u=0;u<i.length;u++)a.prototype["get"+e(i[u])]=t(i[u]),a.prototype["set"+e(i[u])]=function(e){return function(t){this[e]=String(t)}}(i[u]);return a})?r.apply(t,i):r)||(e.exports=o)}()},,,,,,,,function(e,t,n){"use strict";n.r(t);var r=/^[1-9][0-9]*$/;function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function o(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){a(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function a(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,i=!1,o=void 0;try{for(var a,s=e[Symbol.iterator]();!(r=(a=s.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){i=!0,o=e}finally{try{r||null==s.return||s.return()}finally{if(i)throw o}}return n}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return c(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function u(){return Boolean(window[window.GoogleAnalyticsObject||"ga"])}function l(){(0,window[window.GoogleAnalyticsObject||"ga"])((function(e){var t=e.get("sendHitTask");e.set("sendHitTask",(function(e){t(e);var n=function(e){var t=decodeURIComponent(e).split("&").map((function(e){return e.split("=")})).filter((function(e){var t=s(e,1)[0];return["t","ec","ea","el","ev"].indexOf(t)>-1})).reduce((function(e,t){return o(o({},e),{},a({},t[0],t[1]))}),{});if(!t.t)return{};return{event:t.t,category:t.ec||"",action:t.ea||"",label:t.el||"",value:t.ev||""}}(e.get("hitPayload"));"event"===n.event&&h("ga",n)}))}))}function h(e,t){var n=function(e){return"GA_"+[e.action,e.category,e.label,e.value].filter((function(e){return 0===e||Boolean(e)})).join("-")}(t);hj.log.debug("intercepting ".concat(e,": ")+JSON.stringify(t)),hj("event",n),hj.event.signal("int.ga",t)}function d(e){return!(!e||"string"!=typeof e)&&!!e.match(r)}function p(e,t){return function(n){hj.log.debug("[".concat(e,"] ").concat(n),t)}}function f(e){return Array.from(e).map((function(e){return JSON.stringify(e)}))}var g,m,v,y=p("ga.forward_events"),b={setup:hj.tryCatch((function(e){if(e.tag_sessions){var t=u(),n="function"==typeof window.gtag;!function e(t,n,r,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;o>=i&&n(!1),setTimeout((function(){t()?n(!0):e(t,n,r,i,o+1)}),r)}((function(){return void 0!==window.dataLayer}),(function(e){if(e)return y("Intercepting dataLayer"),void function(){var e=window.dataLayer;if(!0!==e._hj){var t=e.push;e.push=function(){t.apply(e,arguments);var n=arguments[0];n.length>=3&&"event"===n[0]&&h("dataLayer",{action:n[1],category:n[2].event_category||"",label:n[2].event_label||"",value:n[2].value||""})},e._hj=!0}}();var t;n&&(y("Intercepting gtag"),t=window.gtag,window.gtag=function(){var e=Array.prototype.slice.call(arguments);t.apply(null,e),e.length<3||"event"!==e[0]||h("gtag",{action:e[1],category:e[2].event_category||"",label:e[2].event_label||"",value:e[2].value||""})})}),3e3,5),t&&(y("Intercepting ga"),l())}}),"ga.forward_events")},j=p("ga.send_hjuid"),_=60,w=!0,S=hj.tryCatch((function(e){var t=e.hjuid_dimension_index;d(t)?(j("setup dimension index: ".concat(t)),g="dimension".concat(t),hj.globals.ready("userId",(function(e){var t=e.get("userId");"string"!=typeof t||t.length<8?j("invalid userid: '".concat(t,"'")):(j("got userId"),v=t.substring(0,8),C())})),E()):j("dimension index is not valid: '".concat(t,"'"))}),"integrations.googleAnalytics"),E=hj.tryCatch((function(){if(!m){if(u()){var e=window[window.GoogleAnalyticsObject||"ga"];return j("`ga` variable is available, waiting for tracker."),void e((function(e){k(e,!0)}))}_<=0?j("given up looking for GA module"):(_-=1,setTimeout(E,500))}}),"integrations"),k=hj.tryCatch((function(e,t){g&&e&&(m&&t||(m!==e&&(j("got fresh tracker"),w=!0),m=e,C()))}),"integrations"),C=hj.tryCatch((function(){w&&v&&m&&g&&(w=!1,m.set(g,v),m.send("event","Hotjar","detect_user",v,{nonInteraction:1}),j("successfully sent detect_user event"))}),"integrations"),O={setup:S,setTracker:hj.tryCatch((function(e){k(e,!1)}),"integrations.googleAnalytics")};var I=p("ga.send_hjuid_gtag"),N={setup:hj.tryCatch((function(e){var t=window.dataLayer;function n(){I("calling gtag(".concat(f(arguments),")")),t.push(arguments)}void 0!==t?hj.globals.ready("userId",(function(t){var r=t.get("userId");if(r&&"string"==typeof r&&!(r.length<8)){var i,o,a,s=r.substring(0,8),c=e.hjuid_dimension_index;d(c)?n("set",(i={},o="dimension".concat(c),a=s,o in i?Object.defineProperty(i,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):i[o]=a,i)):I("Invalid dimension index ".concat(c)),n("set","user_properties",{hjuid:s}),n("event","detect_user",{event_category:"Hotjar",event_label:s,non_interaction:!0})}})):I("`dataLayer` is undefined")}),"ga.send_hjuid_gtag")};function T(e){return(T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function R(e){hj.log.debug("[GTM Patch] ".concat(e))}function A(){window.dataLayer.push(arguments)}var x={setup:function(){if(window.dataLayer&&"function"==typeof window.dataLayer.push&&"object"===T(window.google_optimize)&&"object"===T(window.google_tag_manager)&&Object.keys(window.google_tag_manager).some((function(e){return e.startsWith("OPT-")||e.startsWith("GTM-")}))){var e,t,n=window.gtag||A;e=0,(t=window.google_tag_manager.mb)&&t.m&&t.m.length&&(t.m=t.m.map((function(t){return function(n){n&&n.message&&"optimize.callback"===n.message[1]?(e>10?(R("Trial ".concat(e," - pausing for ").concat(Math.floor(10),"s")),setTimeout((function(){t(n)}),1e4),setTimeout((function(){e=0}),6e4)):t(n),e++):t(n)}}))),n("event","optimize.callback",{callback:function(e,t,n){R("Experiment with ID: ".concat(t," is on variant: ").concat(e)),hj.event.signal("exp.go",{experimentId:t,variantId:e,containerId:n})}})}else R("Skipping Google Optimize integration since it was not active in this page")}},P={},D=window.optimizely;function M(e){"applied"===e.name&&(hj.log.debug("Optimizely - campaign decided; ready to tag experiments.","integration"),L())}function L(){var e,t,n,r,i;hj.log.debug("Optimizely - attempting to tag active experiments.","integration"),hj.log.debug("Optimizely - refreshing active experiment variation map.","integration"),P={},D&&"get"in D&&(r=D.get("state"),i=D.get("data"),r.getActiveExperimentIds().forEach((function(o){t=i.experiments[o].name||o,e=r.getVariationMap()[o],n=e.name||e.id,P[t]=n}))),function(){hj.log.debug("Optimizely - looking for tags.","integration");var e=[];for(var t in P)hj.event.signal("exp.opt",{experimentId:t,variantId:P[t]}),e.push(t+"/"+P[t]);return e.length>0?hj.log.debug("Optimizely - found "+e.length+" tags.","integration",e):hj.log.debug("Optimizely - no tags found.","integration"),e}().forEach((function(e){hj("event",e)}))}var H={setup:function(){void 0!==window.optimizely&&"function"==typeof window.optimizely.push?(hj.log.debug("Optimizely - listening for campaignDecided event.","integration"),(D=window.optimizely||[]).push({type:"addListener",filter:{name:"campaignDecided"},handler:M}),L()):hj.log.debug("`window.optimizely` is not an array","integration")}};hj.tryCatch((function(){hj.loader.registerModule("IntegrationsModule",(hj.integrations=hj.tryCatch((function(){return{optimizely:H,google_analytics:{forwardEvents:b,sendHotjarUserId:O,sendHotjarUserIdGtag:N},google_optimize:x}}),"integrations")(),{run:hj.tryCatch((function(){var e=hj.settings.integrations;e&&(e.optimizely&&e.optimizely.tag_recordings&&hj.integrations.optimizely.setup(),e.google_analytics&&(hj.integrations.google_analytics.forwardEvents.setup(e.google_analytics),e.google_analytics.send_hotjar_id&&(hj.integrations.google_analytics.sendHotjarUserId.setup(e.google_analytics),hj.integrations.google_analytics.sendHotjarUserIdGtag.setup(e.google_analytics))),e.google_optimize&&e.google_optimize.tag_recordings&&hj.integrations.google_optimize.setup())}))}),!1)}),"integrations")()},function(e,t,n){"use strict";n.r(t),n.d(t,"eventStream",(function(){return O}));n(126),n(136);var r=n(64),i=Object.freeze({MOUSE_CLICK:r.a.MOUSE_CLICK,MOUSE_MOVE:r.a.MOUSE_MOVE,SCROLL:r.a.SCROLL,VIEWPORT_RESIZE:r.a.VIEWPORT_RESIZE,KEY_PRESS:r.a.KEY_PRESS,SELECT_CHANGE:r.a.SELECT_CHANGE,INPUT_CHOICE_CHANGE:r.a.INPUT_CHOICE_CHANGE,CLIPBOARD:r.a.CLIPBOARD}),o=n(8);function a(e){return"".concat(parseFloat(e/1048576).toFixed(2))}var s=n(118),c=n(6);function u(e){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var l=hj.tryCatch((function(e){var t,n,l,h,d={},p="",f=void 0,g=null,m=o.a.now(),v=!1;d.events=[];var y=s.create();d.storePageContent=function(t,n){hj.log.warnIfEmpty(t,"tryStorePageContent: content"),hj.log.warnIfEmpty(n,"tryStorePageContent: urlMD5"),e.storePageContent(t,(function(e){var t=e.content_uuid;t?(hj.globals.set("pageContentUUID",t),y.set("pageContent",{uuid:t,md5:n})):hj.log.warn("Expecting res.content_uuid but it was not found!")}),(function(e){if(413===e.status){var n=a(t.length);d.write("content_size_too_large",{size:n,source:"page_visit",timestamp:o.a.now()},!1)}}))},d.reportPageContent=function(e){y.ready("pageContent",(function(t){if(t.get("pageContent")){var n=t.get("pageContent"),i=n.uuid,o=n.md5,a=hj.dom.getCSSURLs().map((function(e){return{content_type:2,url:e,url_hash:hj.md5(hj.b64EncodeUnicode(e))}}));hj.log.warnIfEmpty(e,"sendReportContent: pageVisitKey"),hj.log.warnIfEmpty(i,"sendReportContent: uuid"),hj.log.warnIfEmpty(o,"sendReportContent: md5"),hj.log.warnIfEmpty(a,"sendReportContent: webResourceInfos");var s,c,u,l={page_content_url_md5:o,page_content_uuid:i,web_resource_infos:a};d.writeNewFrame((s={},c=r.a.REPORT_CONTENT,u=l,c in s?Object.defineProperty(s,c,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[c]=u,s),e).flush()}}))},d.clearPageContent=function(){hj.globals.set("pageContentUUID",void 0),y.set("pageContent",void 0)},d.setCurrentPageVisitKey=hj.tryCatch((function(e){p!==e&&(p=e,t()),d.flush()}),"data"),d.writeNewFrame=hj.tryCatch((function(e,n){return t(n),d.write(e),t(),d})),d.write=hj.tryCatch((function(e,t,n){var r;if(hj.isPreview)return d;if("object"===u(e))return hj.hq.each(e,(function(e,t){d.write(e,t,!0)})),d;var i=e,a=j(i);if(!a&&v)return d;if(!a&&w())return S(),d;a&&(g=o.a.now()),a&&v&&E();var s=null!==(r=d.events.pop())&&void 0!==r?r:b(p);return n?s[i]=t:(s[i]=s[i]||[],s[i].push(t)),d.events.push(s),d}),"data"),d.flush=hj.tryCatch((function(){var n=hj.globals.get("userId");if(f&&clearInterval(f),!n)return hj.globals.ready("userId",d.flush),t();var r=_(),i=r.length;if(i>0)for(var o=0;o<i;o++){var a=r[o];e.send(a,n)}f=setInterval(d.flush,1e3)}),"data");var b=function(e){return{pageVisitKey:e}},j=function(e){return hj.hq.inArray(e,hj.hq.values(i))};t=hj.tryCatch((function(e){var t={pageVisitKey:e||p};d.events.push(t)}),"data");var _=hj.tryCatch((function(){for(var e,r=d.events.length,i=[],o=[],a=[],s=0;s<r;s++)if(e=d.events[s],Object.keys(e).length>1){var c=[];e.autotag_recording&&(c=e.autotag_recording.filter((function(e){return"rageclick"===e.name}))),c.length>0?o.push(e):(e.clipboard&&i.push(e.clipboard),a.push(n(e)))}var u=h(o,i);return d.events=u.leftover,t(),[].concat(a,u.sendable)}),"data");h=hj.tryCatch((function(e,t){var r=[],i=[];return hj.hq.each(t,(function(t,n){e=e.filter((function(e){if("copy"===n.action||"cut"===n.action)return n.time-e.autotag_recording[0].time>5e3}))})),hj.hq.each(e,(function(e,t){hj.time.getNow()-t.autotag_recording[0].time<5e3?r.push(t):i.push(n(t))})),{leftover:r,sendable:i}}),"data.filterRageClicks"),n=hj.tryCatch((function(e){return hj.hq.each(e,(function(t,n){hj.hq.isFunction(n)&&(e[t]=n())})),e.page_visit_key=e.pageVisitKey,delete e.pageVisitKey,e.viewport_id=hj.viewport.getId(),"object"===u(e.mutation)&&(e.mutation=l(e.mutation)),e}),"data"),l=hj.tryCatch((function(e){var t,n="";if("object"===u(e))return hj.hq.each(e,(function(r,i){"object"===u(i.c)&&(hj.hq.each(i.c,(function(i,o){"object"===u(o.attributes)&&"string"==typeof o.attributes.style&&(o.attributes.style===t&&o.id===n&&(e[r].c[i]=null),t=o.attributes.style,n=o.id)})),e[r].c=e[r].c.filter((function(e){return e})),e[r].c.length||delete e[r].c),void 0===e[r].a&&void 0===e[r].b&&void 0===e[r].c&&void 0===e[r].d&&(e[r]=null)})),e.filter((function(e){return e}))}),"data");var w=function(){var e,t=null!==(e=g)&&void 0!==e?e:m;return o.a.now()-t>18e5},S=function(){sessionStorage.removeItem("_hjRecordingEnabled"),sessionStorage.removeItem("_hjRecordingLastActivity"),v=!0},E=function(){v=!1,c.a.items.SESSION_RESUMED.set(!0),hj._init.reinit(window.location.href,!0)};return d})),h=n(30),d=n(10),p=n(17),f=Object(h.a)(180,Object(h.d)(2,4)),g=Object(d.i)(Object(h.e)(Object(h.b)({cliffValue:30,exponentialTicker:function(){return f()},jitterTicker:function(){return Object(d.h)(30)}})));function m(e){if(!e)throw new Error("HotjarWebSocket requires a flush callback");this._connected=!1,this._connecting=!1,this._closedPermanently=!1,this.sessionTimedOutDueToInactivity=!1,this._finished=!1,this._pingInterval=3e4,this._pingIntervalId=void 0,this._sessionInactiveInterval=18e5,this._lastUserActivityTime=(new Date).getTime(),this._unloadTimeoutStarted=!1,this._ws=null,this._wsUrl="",this._flush=e,this.reconnecting=!1,this.retryOnError=!1}function v(e){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}m.prototype.connect=hj.tryCatch((function(){if(this._sessionIsDisabled())return!1;if(!this._connected&&!this._connecting){this._connecting=!0;var e=this;hj.globals.ready("userId",(function(){if(e._wsUrl=_hjSettings.wsMockUrl||"wss://".concat(Object(p.c)(),"/api/v2/client/ws"),!e.reconnecting)try{e._createAndConnect()}catch(e){hj.log.debug("The Web Socket connection was refused. \n ".concat(e))}}))}return!0}),"data.HotjarWebSocket.connect"),m.prototype.updateLastUserActivityTime=hj.tryCatch((function(){this._lastUserActivityTime=(new Date).getTime()}),"data.HotjarWebSocket.updateLastUserActivityTime"),m.prototype.disconnect=hj.tryCatch((function(){this._connected&&(hj.log.debug("Disconnecting Web Socket.","websocket"),this._flush(),this._connected=!1,this._connecting=!1,this._finished=!0,this.close())}),"data.HotjarWebSocket.disconnect"),m.prototype.isConnected=hj.tryCatch((function(){return!!this._connected&&((new Date).getTime()-this._lastUserActivityTime<=this._sessionInactiveInterval||(sessionStorage.removeItem("_hjRecordingEnabled"),sessionStorage.removeItem("_hjRecordingLastActivity"),this.close(),this.sessionTimedOutDueToInactivity=!0,!1))}),"data.HotjarWebSocket.isConnected"),m.prototype.send=hj.tryCatch((function(e){this._sessionIsDisabled()||(hj.log.debug("Sending data to Web Socket","websocket",e),this._ws.send(e))}),"data.HotjarWebSocket.send"),m.prototype.close=hj.tryCatch((function(){hj.log.debug("Closing Web Socket.","websocket"),this._ws.close()}),"data.HotjarWebSocket.close"),m.prototype.getBufferedAmount=hj.tryCatch((function(){return this._ws.bufferedAmount}),"data.HotjarWebSocket.getBufferedAmount"),m.prototype._sessionIsDisabled=hj.tryCatch((function(){return this._closedPermanently||this.sessionTimedOutDueToInactivity||"1"===c.a.items.SESSION_TOO_LARGE.get()}),"data.HotjarWebSocket._sessionIsDisabled"),m.prototype._createAndConnect=hj.tryCatch((function(){var e=this,t=this;if(t._finished)hj.log.debug("Unload event triggered, don't reconnect"),!1===t._unloadTimeoutStarted&&(t._unloadTimeoutStarted=!0,setTimeout((function(){t._finished=!1,t._unloadTimeoutStarted=!1}),1e3));else{hj.log.debug("Connecting to Web Socket ["+this._wsUrl+"]","websocket");var n=Object(d.a)();t._ws=function r(i){var o;t.reconnecting=!1;try{o=new WebSocket(t._wsUrl)}catch(e){return void hj.log.debug("Web socket connection retry error. \n ".concat(e))}return o.onopen=function(e){i(o),t._onOpen.call(t,e),t.retryOnError&&(hj.log.debug("Successfully opened WS. Disabling WS reconnections.","websocket"),t.retryOnError=!1)},o.onmessage=function(e){t._onMessage.call(t,e)},o.onclose=function(o){hj.log.debug("onClose was called.","websocket",o),t._onClose.call(t,o),o.wasClean&&1012!=o.code?(e._connected=!1,e._connecting=!1,e._closedPermanently=!0):(e._connecting=!0,g((function(){return n((function(t){if(t>=8)return e._connected=!1,e._connecting=!1,void(e._closedPermanently=!0);r(i)}))})))},o.onerror=function(e){hj.log.debug("onError was called.","websocket",e),t._onError.call(t,e)},o}((function(e){t._ws=e})),window.addEventListener("beforeunload",this.disconnect,!1)}}),"data.HotjarWebSocket._createAndConnect"),m.prototype._ping=hj.tryCatch((function(){this._connected&&(hj.log.debug("Pinging Web Socket.","websocket"),this._ws.send("ping"))}),"data.HotjarWebSocket._ping"),m.prototype._onOpen=hj.tryCatch((function(){hj.log.debug("Web Socket opened.","websocket"),this._pingIntervalId=setInterval(this._ping.bind(this),this._pingInterval),this._connected=!0,this._connecting=!1,this._flush()}),"data.HotjarWebSocket._onOpen"),m.prototype._onMessage=hj.tryCatch((function(e){var t;try{t=JSON.parse(e.data)}catch(t){throw hj.log.warn("Could not parse websocket message: "+e.data),t}switch(t.type){case"SESSION_TOO_LARGE":hj.log.warn("Session became too large. Will stop sending websocket data."),c.a.items.SESSION_TOO_LARGE.set("1"),this.disconnect();break;default:hj.log.warn("Received unknown websocket message: "+e.data)}}),"data.HotjarWebSocket._onMessage"),m.prototype._onClose=hj.tryCatch((function(e){hj.log.debug("Web Socket closed.","websocket"),e.wasClean||hj.log.warn("Websocket close was unclean: "+e.code),this._connected&&(clearInterval(this._pingIntervalId),this._connected=!1)}),"data.HotjarWebSocket._onClose"),m.prototype._onError=hj.tryCatch((function(){hj.log.debug("Web Socket errored.","websocket"),this._connected&&(clearInterval(this._pingIntervalId),this._connected=!1)}),"data.HotjarWebSocket._onError");var y=hj.tryCatch((function(e){var t,n,u,l,h,d={},p="",f=void 0;d.events=[];var g=s.create();d.storePageContent=function(t,n){hj.log.warnIfEmpty(t,"tryStorePageContent: content"),hj.log.warnIfEmpty(n,"tryStorePageContent: urlMD5"),e.storePageContent(t,(function(e){var t=e.content_uuid;t?(hj.globals.set("pageContentUUID",t),g.set("pageContent",{uuid:t,md5:n})):hj.log.warn("Expecting res.content_uuid but it was not found!")}),(function(e){if(413===e.status){var n=a(t.length);d.write("content_size_too_large",{size:n,source:"page_visit",timestamp:o.a.now()},!1)}}))},d.reportPageContent=function(e){g.ready("pageContent",(function(t){var n=t.get("pageContent");if(n){var i=n.uuid,o=n.md5,a=hj.dom.getCSSURLs().map((function(e){return{content_type:2,url:e,url_hash:hj.md5(hj.b64EncodeUnicode(e))}}));hj.log.warnIfEmpty(e,"sendReportContent: pageVisitKey"),hj.log.warnIfEmpty(i,"sendReportContent: uuid"),hj.log.warnIfEmpty(o,"sendReportContent: md5"),hj.log.warnIfEmpty(a,"sendReportContent: webResourceInfos");var s,c,u,l={page_content_url_md5:o,page_content_uuid:i,web_resource_infos:a};d.writeNewFrame((s={},c=r.a.REPORT_CONTENT,u=l,c in s?Object.defineProperty(s,c,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[c]=u,s),e).flush()}}))},d.clearPageContent=function(){hj.globals.set("pageContentUUID",void 0),g.set("pageContent",void 0)},d.setCurrentPageVisitKey=hj.tryCatch((function(e){p!==e&&(p=e,t()),d.flush()}),"data"),d.writeNewFrame=hj.tryCatch((function(e,n){return t(n),d.write(e),t(),d})),d.write=hj.tryCatch((function(e,t,n,r){var i;if(hj.isPreview)return d;if("object"===v(e))return hj.hq.each(e,(function(e,t){d.write(e,t,!0,r)})),d;var o=e;if(!r&&!d._ws.connect()){if(!d._ws.sessionTimedOutDueToInactivity||!b(o))return d;c.a.items.SESSION_RESUMED.set(!0),d._ws=new m(d.flush),hj._init.reinit(window.location.href,!0),d._ws.connect()}var a=null!==(i=d.events.pop())&&void 0!==i?i:y(p);return n?a[o]=t:(a[o]=a[o]||[],a[o].push(t)),d.events.push(a),d}),"data"),d.flush=hj.tryCatch((function(){var e,r,i=hj.globals.get("userId");if(f&&clearInterval(f),i||(hj.globals.ready("userId",d.flush),t()),d._ws.isConnected()&&i){if((r=(e=n()).length)>0){var s={uuid:i,viewportid:hj.viewport.getId(),site_id:hj.settings.site_id},c=hj.hq.stringify(s)+"\n";j(e)&&d._ws.updateLastUserActivityTime();for(var u=0;u<r;u++){var l=e[u],h=hj.hq.stringify(l);if(h.length<10485760)d._ws.send(c+h);else{var p=hj.hq.stringify({content_size_too_large:{size:a(h.length),source:"flush",timestamp:o.a.now()},page_visit_key:l.page_visit_key,viewport_id:l.viewport_id});d._ws.send(c+p)}}}f=setInterval(d.flush,1e3)}}),"data"),d._ws=new m(d.flush);var y=function(e){return{pageVisitKey:e}},b=function(e){return hj.hq.inArray(e,hj.hq.values(i))},j=function(e){return e.some((function(e){return Object.keys(e).some(b)}))};return t=hj.tryCatch((function(e){var t={pageVisitKey:e||p};d.events.push(t)}),"data"),n=hj.tryCatch((function(){for(var e,n=d.events.length,r=[],i=[],o=[],a=0;a<n;a++)if(e=d.events[a],Object.keys(e).length>1){var s=[];e.autotag_recording&&(s=e.autotag_recording.filter((function(e){return"rageclick"===e.name}))),s.length>0?i.push(e):(e.clipboard&&r.push(e.clipboard),o.push(u(e)))}var c=h(i,r);return d.events=c.leftover,t(),[].concat(o,c.sendable)}),"data"),h=hj.tryCatch((function(e,t){var n=[],r=[];return hj.hq.each(t,(function(t,n){e=e.filter((function(e){if("copy"===n.action||"cut"===n.action)return n.time-e.autotag_recording[0].time>5e3}))})),hj.hq.each(e,(function(e,t){hj.time.getNow()-t.autotag_recording[0].time<5e3?n.push(t):r.push(u(t))})),{leftover:n,sendable:r}}),"data.filterRageClicks"),u=hj.tryCatch((function(e){return hj.hq.each(e,(function(t,n){hj.hq.isFunction(n)&&(e[t]=n())})),e.page_visit_key=e.pageVisitKey,delete e.pageVisitKey,e.viewport_id=hj.viewport.getId(),"object"===v(e.mutation)&&(e.mutation=l(e.mutation)),e}),"data"),l=hj.tryCatch((function(e){var t,n="";if("object"===v(e))return hj.hq.each(e,(function(r,i){"object"===v(i.c)&&(hj.hq.each(i.c,(function(i,o){"object"===v(o.attributes)&&"string"==typeof o.attributes.style&&(o.attributes.style===t&&o.id===n&&(e[r].c[i]=null),t=o.attributes.style,n=o.id)})),e[r].c=e[r].c.filter((function(e){return e})),e[r].c.length||delete e[r].c),void 0===e[r].a&&void 0===e[r].b&&void 0===e[r].c&&void 0===e[r].d&&(e[r]=null)})),e.filter((function(e){return e}))}),"data"),d})),b=n(120);function j(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var _=Object(b.Resource)("/api/v2/sites/<int:site_id>/recordings/content",{post:["POST",["content"]]},p.c),w=Object(b.Resource)("/",{post:["POST"]},(function(){return _hjSettings.contentHost||(_hjSettings.contentHost="content.hotjar.io"),_hjSettings.contentHost})),S=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t.hasFeature("ingestion.http.page_content")?(this.pageContentResource=w,this.isHttpPageContent=!0):(this.isHttpPageContent=!1,this.pageContentResource=_)}var t,n,r;return t=e,(n=[{key:"storePageContent",value:function(e,t,n){var r={site_id:hj.settings.site_id,content:e},i=this.isHttpPageContent?"text":"json",o="text"===i?e:r;return this.pageContentResource.post(o,t,n,i)}},{key:"send",value:function(){}}])&&j(t.prototype,n),r&&j(t,r),e}(),E=n(111);function k(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function C(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var O,I=Object(h.a)(180,Object(h.d)(2,4)),N=Object(b.Resource)("/",{post:["POST"]},(function(){return _hjSettings.contentHost||(_hjSettings.contentHost="content.hotjar.io"),_hjSettings.contentHost})),T=function(){function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3;k(this,e),this.viewport=t,this.siteId=n,this.maxRetries=r}var t,n,r;return t=e,(n=[{key:"storePageContent",value:function(e,t,n){return N.post(e,t,n,"text")}},{key:"send",value:function(e,t){var n=(_hjSettings.eventsHost||(_hjSettings.eventsHost="https://events.hotjar.io"),_hjSettings.eventsHost+"/"),r={uuid:t,viewportid:this.viewport.getId(),site_id:this.siteId},i=hj.hq.stringify(r)+"\n"+hj.hq.stringify(e),o=Object(d.i)(Object(h.e)(Object(h.b)({cliffValue:30,exponentialTicker:function(){return I()},jitterTicker:function(){return Object(d.h)(30)}}))),a=Object(d.e)(this.maxRetries),s=function e(){return Object(E.post)(n,i,(function(e){hj.log.debug("Events sent successfully to host","events",e)}),(function(){hj.log.debug("Events failed to send to host, retrying","events"),o((function(){return a((function(){return e()}))}))}),"text")};s()}}])&&C(t.prototype,n),r&&C(t,r),e}();O=hj.features.hasFeature("ingestion.http")?l(new T(hj.viewport,hj.settings.site_id)):y(new S(hj.features)),hj.eventStream=O},function(e,t,n){"use strict";n.r(t);var r=n(60),i=n(83),o=n(64),a=n(156),s=n(26),c=n(56),u=n(4),l=n(35),h=n(17),d=n(25);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var f=n(14),g=f.f.HJ_HEATMAP_ID,m=f.f.HJ_SITE_ID,v=f.f.HJ_USER_ID,y=f.f.HJ_MATCH_TYPE,b=function(){var e=hj.features.hasFeature("heatmap.continuous.dynamic_heatmaps");hj.features.hasFeature("heatmap.dynamic_view")?Object(c.a)(u.f.HEATMAP_DYNAMIC_VIEW):e&&(window.opener&&window.opener.postMessage("".concat(window.name,"-handshake"),"https://".concat(hj.insightsHost)),window.addEventListener("message",(function(e){e.origin==="https://".concat(hj.insightsHost)&&e.data[d.a.HEATMAP_VIEWER]&&(!function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};sessionStorage.setItem(d.a.HEATMAP_VIEWER,!0),sessionStorage.setItem(v,e[v]||hj.url.getParameter(v)),sessionStorage.setItem(m,e[m]||hj.url.getParameter(m)),sessionStorage.setItem(g,e[g]||hj.url.getParameter(g)),sessionStorage.setItem(y,e[y]||hj.url.getParameter(y))}(e.data),Object(c.a)(u.f.HEATMAP_VIEWER))})))},j=n(34),_=n(29),w=!1;function S(e,t){return!!e&&(e.startsWith?e.startsWith(t):Object(j.a)(e,t))}var E="https://".concat(hj.insightsHost);try{hj.ui.retaker=Object.freeze({loadWidget:function(){document.getElementById(_.a)||(w=!1,window.opener.postMessage({type:_.e.ready,origin:window.location.origin},E))}})}catch(e){}var k=function(){!function(){var e=sessionStorage.getItem(d.a.RETAKER),t=hj.url.getParameter(d.a.RETAKER),n=sessionStorage.getItem(d.a.TARGETING),r=atob(decodeURIComponent(hj.url.getParameter(d.a.TARGETING)));if((t||e)&&(r||n)){var i=hj.url.getParameter(l.a);if(t){var o;sessionStorage.setItem(d.a.RETAKER,t),sessionStorage.setItem(l.a,i),sessionStorage.setItem(d.a.TARGETING,r);var a=Object(h.i)(window.location.href,(p(o={},d.a.RETAKER,null),p(o,l.a,null),p(o,d.a.TARGETING,null),o));history.replaceState&&history.replaceState(null,"",a)}Object(c.a)(u.f.HEATMAP_SCREENSHOTTER)}}(),b(),function(){if(hj.features.hasFeature("heatmap.continuous.manual_retaker")){var e="https://".concat(hj.insightsHost);if(S(document.referrer+"",e))if(!(!window.opener||!window.opener.postMessage)){window.addEventListener("message",(function(t){if(S(t.origin,e)&&t.data&&t.data.type===_.e.start){if(w)return;sessionStorage.setItem(_.g,t.data.mode),Object(c.a)(u.f.HEATMAP_RETAKER),w=!0}}));try{t()}catch(e){window.addEventListener("load",(function(){t()}))}window.addEventListener("popstate",(function(){n()})),window.addEventListener("hashchange",(function(){n()}))}}function t(){window.opener.postMessage({type:_.e.ready,origin:window.location.origin},e)}function n(){document.getElementById(_.a)||(w=!1,t())}}()},C=n(112),O=n(71),I=n(12),N=n(57),T=n(6),R=n(8),A=n(67),x=n(10),P=Object(A.a)({storageAccessor:T.a.items.HJ_SESSION,serializer:x.j,deserializer:x.b}),D=function(e){var t,n,r,i=null!==(t=null==e?void 0:e.id)&&void 0!==t?t:Object(x.c)(),o=null!==(n=null==e?void 0:e.created)&&void 0!==n?n:R.a.now(),a=null!==(r=null==e?void 0:e.inSample)&&void 0!==r?r:Object(N.b)();return Object.freeze({id:i,created:o,inSample:a})},M=n(38),L=n(68),H=n(172),U=n.n(H),z=n(173),V=n.n(z),q=n(24);function W(e){return(W="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var F={maxLogsPerPage:100,_overflow:!1,_total:0,init:function(){hj.tryCatch((function(){hj.features.hasFeature("error_reporting")&&hj.settings.session_capture_console&&(hj.isPreview||(Object(N.b)("error-capture.run")||Object(N.a)("error-capture.run"))&&(window.addEventListener("error",F.onError),window.addEventListener("unhandledrejection",F.onUnhandledRejection)))}),"error-capture.run")()},onError:function(e){F.addError(e)},onUnhandledRejection:function(e){F.addError({error:e.reason,message:"Uncaught (in promise)",filename:"",lineno:0})},addError:function(e){hj.tryCatch((function(){var t=e.error,n=e.message,r=e.lineno,i=e.filename;if((t||n||r||i)&&F.hasReachedOverflow()){var o=i&&"object"===W(i)?F.truncate(F.stringify(i),100):i,a="object"===W(r)?null:r,s=t&&t instanceof Error&&t.stack?V.a.parse(t).slice(0,20):null;Object(q.a)("error",{text:hj.privacy.suppressErrorMessage(F.getErrorMessage(e)),filename:o,lineno:a,trace:s,time:hj.time.getNow(),timestamp:R.a.now()})}}),"error-capture.addError")()},getErrorMessage:function(e){if(!e.error)return e.message||"";var t=F.truncate(F.stringify(e.error),1e3);if(!e.message)return t;var n=e.error instanceof Error?e.error.toString():void 0;return-1!==e.message.indexOf(n)?t.replace(n,e.message):"".concat(e.message," ").concat(t)},stringify:function(e){if(e instanceof Error){var t=e.toString();return e.stack?-1!==e.stack.indexOf(t)?e.stack:[t,e.stack].join("\n"):t}return U()(e)},truncate:function(e,t){return e.length<=t?e:"".concat(e.substring(0,t-3),"...")},hasReachedOverflow:function(){return F._overflow||F._total===F.maxLogsPerPage?(F._overflow=!0,!1):(F._total++,!0)}};function G(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function B(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?G(Object(n),!0).forEach((function(t){K(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):G(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function K(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}hj.tryCatch((function(){void 0===hj.scriptLoaded&&(hj._init=hj.tryCatch((function(){var e={};e.performanceData=function(){if(Object(r.e)())return{execution_start:Object(r.d)(),loading_start:Object(r.b)()}}(),e._sendVisitData=function(e){var t="".concat(hj.apiUrlBase,"/client/sites/").concat(hj.settings.site_id,"/verify-installation?sv=").concat(_hjSettings.hjsv||0);hj.ajax.post(t,{},hj.tryCatch(e,"init._sendVisitData"))};return e._reportVerificationResults=function(e,t,n){if(e){var r="".concat(hj.apiUrlBase,"/tcvs/verification/").concat(e,"/result"),i=B(B({},n&&{error_detail:n}),{},{status:t});hj.ajax.post(r,i,(function(){hj.tcVerificationResultsSent=!0}),(function(e){e&&400!==e.status&&404!==e.status&&hj.exceptions.log({message:"TCVS endpoint failed",stacktrace:""}),hj.tcVerificationResultsSent=!0}))}},e._verifyInstallation=function(){var t,n=hj.url.getParameter("hjVerifyInstall"),r=hj.url.getParameter("hjVerifyUUID"),o=function(){Object(i.a)({title:"Hotjar installation invalid",message:"The tracking code you are trying to verify does not match the one installed on this page. Please make sure you install the correct tracking code provided for this site.",status:"bad"}),e._reportVerificationResults(r,"wrong_code",{expected:String(hj.verifyInstall),actual:String(hjSiteSettings.site_id)});var t="Passed Site ID: ".concat(hj.verifyInstall," != Configured Site ").concat(hj.settings.site_id);hj.exceptions.log({message:t,stacktrace:""})};try{t=sessionStorage.getItem("hjVerifyInstall")}catch(e){}if(n||t){hj.verifyInstall=parseInt(n||t);try{sessionStorage.setItem("hjVerifyInstall",n||t)}catch(e){}if(window.hjBootstrapCalled&&window.hjBootstrapCalled.length>1){var a,s=window.hjBootstrapCalled.filter((function(e,t){return window.hjBootstrapCalled.indexOf(e)===t})),c="You have "+window.hjBootstrapCalled.length+" tracking scripts installed on your site. ",u=2===window.hjBootstrapCalled.length?"script as this":"scripts as these",l=!!window.dataLayer;s.length>1?(a=c+"Please remove the duplicate "+u+" will cause issues.",Object(i.a)({title:"Multiple different Hotjar scripts detected",message:a,status:"bad"}),e._reportVerificationResults(r,"multiple_codes",{expected:String(hj.verifyInstall),actual:s,gtm:!1})):l?(a=c+"If you've installed Hotjar through GTM - please remove the duplicate "+u+" will cause issues.",e._reportVerificationResults(r,"multiple_codes",{expected:String(hj.verifyInstall),actual:s,gtm:!0}),Object(i.a)({title:"Multiple Hotjar scripts detected",message:a,status:"bad"})):hj.verifyInstall===hj.settings.site_id?(a=c+"This will not affect data collection, but we do suggest removing redundant scripts.",e._reportVerificationResults(r,"warning",{expected:String(hj.verifyInstall),reason:"multiple scripts"}),Object(i.a)({title:"Multiple Hotjar scripts detected",message:a,status:"warning"})):o();var h="Passed Site ID: "+hj.verifyInstall+" contains multiple scripts "+window.hjBootstrapCalled.join(", ");hj.exceptions.log({message:h,stacktrace:""})}else hj.verifyInstall===hj.settings.site_id?(e._sendVisitData((function(e){e.success||hj.exceptions.log({message:"Verify installation endpoint failed",stacktrace:""})})),Object(i.a)({title:"Hotjar installation verified",message:"The Hotjar tracking code has been properly installed on this page. Browse your site in this window if you wish to verify installation on any other pages.",status:"good"}),e._reportVerificationResults(r,"ok")):o()}},e._browserIsSupported=hj.tryCatch((function(){return!(Object(h.d)()<11)||(hj.log.debug("IE < 11 is not supported.","init"),"1"===hj.url.getParameter("hjVerifyInstallation")&&Object(i.a)({title:"Hotjar installation cannot be verified.",message:"Sorry – your browser is not supported.",status:"bad"}),!1)}),"init"),e._checkDebug=hj.tryCatch((function(){var e=hj.url.getParameter(T.a.items.DEBUG_FLAG.getKey());e&&("1"===e||"true"===e?hj.debug.on(!0):hj.debug.off()),"true"===T.a.items.DEBUG_FLAG.get()&&hj.debug.on(!1)}),"init"),e._canRun=hj.tryCatch((function(){return-1===navigator.userAgent.indexOf("Hotjar")&&(T.a.canUseCookies()?T.a.canUseLocalStorage()?!!T.a.canUseSessionStorage()||(hj.log.debug("sessionStorage is not available","init"),!1):(hj.log.debug("localStorage is not available","init"),!1):(hj.log.debug("Cookies are not enabled"),!1))}),"init"),e._configureStateChangeListenMode=function(){var e="manual";hj.settings&&hj.settings.state_change_listen_mode&&(e=hj.settings.state_change_listen_mode),hj.locationListener.setMode(e)},e.run=hj.tryCatch((function(t){hj.currentUrl=t,hj.scriptLoaded=!0,e._browserIsSupported()&&(e._checkDebug(),e._canRun()?hj.remoteStorage.get("_hjOptOut",(function(n){e._run(t,n)})):hj._init._verifyInstallation())}),"init"),e._run=hj.tryCatch((function(t,n){if("true"!==n&&"1"!==navigator.doNotTrack&&"1"!==window.doNotTrack&&"1"!==navigator.msDoNotTrack||(hj.log.debug("Visitor has opted out of tracking.","init"),hj.optOut=!0),hj.log.debug("Site settings","init",hj.settings),k(),hj.settings.site_id){hj.isIncludedInSample=N.b;var r=Object(M.a)();Object(M.b)(r),r.existing||I.a.setFirstSeen(),!function(e){P.set(e)}(function(){var e=P.get();return null===e?D():D(e)}()),L.userAttributes.init(),F.init(),e._configureStateChangeListenMode(),e._runPage(t),e._verifyInstallation(),hj.command.activate(),"1"===hj.url.getParameter("hjIncludeInSample")&&Object(i.a)({title:"Hotjar tracking active.",message:"Hotjar tracking is active for your session.",status:"good"})}else hj.log.warn("Script execution for halted due to no site id: ".concat(window.location.href),"init")}),"init"),e.reinit=hj.tryCatch((function(t,n){hj.currentUrl=t,hj.widgetDelay.clear(),C.a.disableHeatmap(),hj.widget.emptyMatchingWidgets(),hj.metrics.reset(),n&&(T.a.items.CACHED_USER_ATTRIBUTES.clear(),O.a.reset()),e._runPage(t)}),"init"),e._runPage=hj.tryCatch((function(t){hj.optOut||(Object(a.b)(t)||(Object(N.a)("init._runPage")?(hj.visitData.track(t,!1),e.performanceData&&hj.eventStream.write(o.a.SCRIPT_PERFORMANCE,{loading_start:e.performanceData.loading_start,execution_start:e.performanceData.execution_start},!1,!0)):Object(N.b)("init._runPage")&&hj.visitData.lazyTrack.enqueue(t,!0)),hj.visitData.trackView()),hj.hq.each(hj.loader.getModules(),(function(e,n){hj.optOut&&!n.nonTracking||(hj.log.debug("Running module","init",n.name),n.module.run(t))})),hj.widget.runLatestMatchingWidget()}),"init"),e}),"init")(),hj.hq(document).ready((function(){hj.log.debug("Document is ready. Initializing...","init"),hj.scriptContextId=Object(s.a)(),hj._init.run(location.href)})))}),"init")()},,,,,function(e,t,n){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}n.r(t);var i=function(e,t,n){[["src","https://"+(n||hj.insightsHost)+"/static/app/img/transparent.png"],["style",function(e,t){var n="https://"+(t||hj.insightsHost)+"/static/app/img/suppressed.png",r=(e.meta||{}).style||{},i=r.width,o=r.height;return[e.style,'background: url("'+n+'") repeat !important',i?"width: "+i:"",o?"height: "+o:""].filter((function(e){return e}))}(t,n).join(";")]].forEach((function(t){e.setAttribute(t[0],t[1])}))},o=n(17);hj.tryCatch((function(){var e=hj.tryCatch((function(){function e(e,t){this.root=e,this.delegate=t,this.idMap={}}return e.prototype.initialize=function(e,t){this.idMap[e]=this.root;for(var n=0;n<t.length;n++)this.deserializeNode(t[n],this.root)},e.prototype.deserializeDocument=function(e,t,n){this.root=document.cloneNode(),n&&(this.idMap={}),this.idMap[e]=this.root;for(var r=0;r<t.length;r++)this.deserializeNode(t[r],this.root,n);return this.root},e.prototype.deserializeNode=function(e,t,n){var r=this,i=!1;if(null===e)return null;var o=this.idMap[e.id];if(o&&!n)return o;var a=this.root.ownerDocument;switch(null===a&&(a=this.root),e.nodeType){case Node.COMMENT_NODE:o=a.createComment(e.textContent);break;case Node.TEXT_NODE:o=a.createTextNode(e.textContent);break;case Node.DOCUMENT_TYPE_NODE:o=a.implementation.createDocumentType(e.name,e.publicId,e.systemId);break;case Node.ELEMENT_NODE:try{this.delegate&&this.delegate.createElement&&(o=this.delegate.createElement(e.tagName)),o||(o=a.createElement(e.tagName))}catch(e){o=a.createComment('hj.treeMirror.deserializeNode.error: "'+e.message+'"'),i=!0;break}Object.keys(e.attributes).forEach((function(t){try{r.delegate&&r.delegate.setAttribute&&r.delegate.setAttribute(o,t,e.attributes[t])||o.setAttribute(t,e.attributes[t])}catch(e){}}))}if(!o)throw"Could not create node of type: "+e.nodeType;if(this.idMap[e.id]=o,t&&t.appendChild(o),e.childNodes&&!i)for(var s=0;s<e.childNodes.length;s++)this.deserializeNode(e.childNodes[s],o,n);return o},e}),"TreeMirrorMirror")(),t=hj.tryCatch((function(){function e(e,t){var n;this.target=e,this.mirror=t,this.nextId=1,this.redactedContentId=-100,this.mutationSummaries=[],this.pendingMutations=[],this.initialSerializedTarget=null,hj.treeMirror.mutationObserverAvailable?(this.knownNodes=new hj.MutationSummary.NodeMap,this.shadowRoots=new hj.MutationSummary.NodeMap):(this.knownNodes={get:function(){},set:function(){},deleteNode:function(){}},this.shadowRoots={get:function(){},set:function(){},deleteNode:function(){},getValues:function(){}}),this.initialSerializedTarget=this.serializeTarget(e);try{void 0!==window.MutationObserver?n=window.MutationObserver:void 0!==window.WebKitMutationObserver?n=window.WebKitMutationObserver:void 0!==window.MozMutationObserver&&(n=window.MozMutationObserver)}catch(e){n=void 0}if(void 0!==n){this.listenToMutations(e);var r=this;this.shadowRoots.getValues().forEach((function(e){r.listenToMutations(e)}))}}return e.prototype.hookAttachShadow=function(){var e=this,t=Element.prototype.attachShadow;Element.prototype.attachShadow=function(){var n=t.apply(this,arguments);return e.shadowRoots.set(this,n),e.listenToMutations(n),n}},e.prototype.listenToMutations=function(e){var t=this;this.mutationSummaries.push(new hj.MutationSummary({rootNode:e,callback:hj.tryCatch((function(e){t.applyChanged(e)}),"hj.treeMirrorClient"),queries:[{all:!0}],observeOwnChanges:!0}))},e.prototype.serializeTarget=function(e,t){for(var n=this.serializeNode(e).id,r=[],i=e.firstChild;i;i=i.nextSibling)r.push(this.serializeNode(i,!0,t));return{rootId:n,children:r}},e.prototype.disconnect=function(){this.mutationSummaries&&this.mutationSummaries.length&&(this.mutationSummaries.forEach((function(e){e.disconnect()})),this.mutationSummaries=[])},e.prototype.rememberNode=function(e){var t=this.nextId++;return this.knownNodes.set(e,t),(null==e?void 0:e.shadowRoot)&&!this.shadowRoots.get(e)&&this.shadowRoots.set(e,e.shadowRoot),t},e.prototype.forgetNode=function(e){this.knownNodes.deleteNode(e),this.shadowRoots.get(e)&&this.shadowRoots.deleteNode(e)},e.prototype.serializeNode=function(e,t,n,r){if(null===e)return null;var i=this.knownNodes.get(e);if(void 0!==i&&!n)return{id:i};void 0===i&&(i=this.rememberNode(e));var a,s,c,u={nodeType:e.nodeType,id:i},l=e.shadowRoot||this.shadowRoots.get(e);if(l&&(u.hasShadowRoot=!!l,u.isSyntheticShadow=!!l.synthetic,l.adoptedStyleSheets&&l.adoptedStyleSheets.length>0)){var h=l.adoptedStyleSheets.reduce((function(e,t){for(var n=[],r=0;r<t.cssRules.length;r++)n.push(t.cssRules[r].cssText);var i=Object(o.b)();return t.ownerHostNode=l.host,t.sheetId=i,e.push({id:i,rules:n}),e}),[]);u.adoptedStyleSheetsRules=h}switch(u.nodeType){case Node.DOCUMENT_TYPE_NODE:a=e,u.name=""===a.name?"html":a.name,u.publicId=a.publicId,u.systemId=a.systemId;break;case Node.COMMENT_NODE:case Node.TEXT_NODE:r=(s=hj.privacy.getSuppressedTextNode(e,r)).shouldSuppressNode,u.textContent=s.content;break;case Node.ELEMENT_NODE:if(r=(c=hj.privacy.getSuppressedNode(e,r)).shouldSuppressNode,u.tagName=c.node.tagName,u.attributes=c.node.attributes,hj.cssBlobs.handleBlobStyles(u),!u.attributes["data-hj-suppressed"]&&"IMG"===u.tagName&&e.currentSrc&&(u.attributes.src=e.currentSrc),e&&e.namespaceURI&&"http://www.w3.org/1999/xhtml"!==e.namespaceURI&&(u.namespaceURI=e.namespaceURI),"SCRIPT"===u.tagName||"NOSCRIPT"===u.tagName)u.childNodes=[{nodeType:Node.TEXT_NODE,id:this.redactedContentId,textContent:""}],this.redactedContentId--;else if(t){if(l&&l.childNodes.length){u.childNodes=[];for(var d=l.firstChild;d;d=d.nextSibling){var p=this.serializeNode(d,!0,n,r);p.isInShadowRoot=!0,u.childNodes.push(p)}}if(e.childNodes.length){u.childNodes=u.childNodes||[];for(var f=e.firstChild;f;f=f.nextSibling){var g=this.serializeNode(f,!0,n,r);u.childNodes.push(g)}}}}return u},e.prototype.serializeAddedAndMoved=function(e,t,n){var r=this,i=e.concat(t).concat(n),o=new hj.MutationSummary.NodeMap;i.forEach((function(e){var t=e.parentNode,n=o.get(t);n||(n=new hj.MutationSummary.NodeMap,o.set(t,n)),n.set(e,!0)}));var a=[];return o.keys().forEach((function(e){for(var t=o.get(e),n=t.keys();n.length;){for(var i=n[0];i.previousSibling&&t.has(i.previousSibling);)i=i.previousSibling;for(;i&&t.has(i);){var s=r.serializeNode(i);s.previousSibling=r.serializeNode(i.previousSibling),i.parentNode&&i.parentNode.host&&i.parentNode.host instanceof HTMLElement?(s.parentNode=r.serializeNode(i.parentNode.host),s.isInShadowRoot=!0):s.parentNode=r.serializeNode(i.parentNode),a.push(s),t.deleteNode(i),i=i.nextSibling}n=t.keys()}})),a},e.prototype.serializeAttributeChanges=function(e){var t=this,n=new hj.MutationSummary.NodeMap;return Object.keys(e).forEach((function(r){e[r].forEach((function(e){var i=n.get(e);i||((i=t.serializeNode(e)).attributes={},n.set(e,i));var o=e.getAttribute(r);if(hj.cssBlobs.handleBlobStyles(e),"string"==typeof o&&o.length&&"src"!==r&&"class"!==r&&(o=o.replace(/-?\d+\.\d+%/g,(function(e){return Math.round(parseFloat(e))+"%"})).replace(/-?\d+\.\d+/g,(function(e){return parseFloat(e).toFixed(1)}))),"string"==typeof o){var a={value:o,name:r},s=hj.privacy.getSuppressedNodeAttribute(e,a,!1);s&&(i.attributes[s.name]=s.value)}else i.attributes[r]=o}))})),n.keys().map((function(e){return n.get(e)}))},e.prototype.applyChanged=function(e){var t=this,n=e[0],r=n.removed.map((function(e){return t.serializeNode(e)})),i=this.serializeAddedAndMoved(n.added,n.reparented,n.reordered),o=this.serializeAttributeChanges(n.attributeChanged),a=n.characterDataChanged.map((function(e){var n=t.serializeNode(e);return n.textContent=hj.privacy.getSuppressedTextNode(e,!1).content,n}));this.mirror.applyChanged(r,i,o,a),n.removed.forEach((function(e){t.forgetNode(e)})),this.processPendingMutation()},e.prototype.getKnownNode=function(e){return this.knownNodes.get(e)},e.prototype.onTreeMirrorUpdate=function(e){this.pendingMutations.push(e)},e.prototype.processPendingMutation=function(){for(var e=void 0;e=this.pendingMutations.shift();)e()},e.prototype.onInitialSerializationEnd=function(e){e(this.initialSerializedTarget.rootId,this.initialSerializedTarget.children),this.initialSerializedTarget=null},e}),"TreeMirrorClient")();hj.treeMirror=hj.tryCatch((function(){var n,o,a={},s=[];function c(e,t,n,r){s.forEach((function(i){i(e,t,n,r)}))}return a.mutationObserverAvailable=void 0!==window.MutationObserver||void 0!==window.WebKitMutationObserver||void 0!==window.MozMutationObserver,a.getTree=hj.tryCatch((function(e,r){var i;n&&a.mutationObserverAvailable?e((i=n.serializeTarget(document,!0,!0)).rootId,i.children):((n=new t(document,{applyChanged:c})).onInitialSerializationEnd(e),n.hookAttachShadow());r&&a.mutationObserverAvailable&&s.push(r)}),"hj.treeMirror.manager.getTree"),a.getMirroredDocument=hj.tryCatch((function(t){var n,s;o||(o=new e(document.cloneNode(),{setAttribute:(n=function(e,t,n){e.setAttribute(t,n)},function(e,t,o){return"data-hj-suppressed"===t&&"object"===r(o)?(i(e,o,s),!0):n(e,t,o)})})),a.getTree((function(e,n){t(o.deserializeDocument(e,n,!0))}))}),"hj.treeMirror.manager.getMirroredDocument"),a.resetMutationListeners=function(){s=[]},a.disconnect=function(){n&&(n.disconnect(),n=null)},a.getNodeId=function(e){return n.getKnownNode(e)},a.onTreeMirrorUpdate=function(e){n.onTreeMirrorUpdate(e)},a}),"hj.treeMirror.manager")()}),"hj.treeMirror")()},function(e,t,n){"use strict";n.r(t);var r=n(64),i=n(156),o=n(4),a=n(12),s=n(24),c=n(112),u=n(138);var l={setup:hj.tryCatch((function(e){hj.visitData.lazyTrack.execute();var t,n,i,o=hj.visitData.property,a=hj.ui.getWindowSize();Object(s.a)((t={},n=r.a.HEATMAP_HELO,i=function(){var t=hj.visitData.getPageVisitInfo(o,a,hj.settings.site_id);return hj.log.warnIfEmpty(t,"heatmap.setup: pageVisitInfo"),Object(u.a)(t,e.id)},n in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i,t)).flush(),Object(s.a)(r.a.SCROLL_REACH,{max_bottom:hj.ui.getBottomAsPercentage()},!0),hj.log.warnIfEmpty(e.selector_version,"heatmap.setup: heatmapData.selector_version"),c.a.enableHeatmap(e.selector_version)}),"behavior-data.heatmap.setup")},h=n(57),d=n(71),p=n(6),f=n(8);var g=function(e){hj.hq.each(hj.settings.heatmaps||[],(function(t,n){hj.targeting.matchRules(n.targeting,e,hj.tryCatch((function(){n.capture_type!==o.k&&Object(h.a)("behavior-data.maybeStartHeatmaps")&&l.setup(n)})),"behavior-data.maybeStartHeatmaps")})),hj.settings.continuous_capture_enabled&&a.a.isRecordingEnabled()&&l.setup(o.b)},m=function(e){if(d.a.active=!1,hj.settings.record){var t=void 0===hj.settings.record_targeting_rules||!hj.settings.record_targeting_rules.length;a.a.setRecordingEnabled(!!sessionStorage.getItem("_hjRecordingEnabled"));var n=sessionStorage.getItem("_hjRecordingLastActivity"),r=!n||f.a.now()-n<o.a.MAX_TIME_SINCE_LAST_RECORDING_ACTIVITY_IN_SESSION;hj.log.debug("_hjRecordingEnabled is set to "+a.a.isRecordingEnabled(),"recordings"),hj.log.debug("_hjRecordingLastActivity is set to "+n,"recordings"),hj.log.debug("_hjRecordingLastActivity still valid: "+r,"recordings"),a.a.isRecordingEnabled()&&r||t?d.a.start():hj.targeting.matchRules(hj.settings.record_targeting_rules,e,hj.tryCatch((function(){return d.a.start()}),"behavior-data.maybeStartRecordings")),p.a.items.SESSION_RESUMED.get()&&(p.a.items.SESSION_RESUMED.clear(),d.a.resume())}};hj.tryCatch((function(){return hj.loader.registerModule("BehaviorData",(e={},hj.behaviorData={tagRecording:function(e,t){e=e||[];for(var n=[],i=0;i<e.length;i+=1){var o=hj.hq.trim(e[i]);o.length&&o.length<=hj.maxRecordingTagLength?n.push({name:o,time:hj.time.getNow(),timestamp:f.a.now()}):hj.log.warn('Invalid recording tag: " '.concat(o,' ", should have length 1.. ').concat(hj.maxRecordingTagLength," characters, was ").concat(o.length,"."))}if(n.length)if(d.a.active){var a=t?r.a.AUTOTAG_RECORDING:r.a.TAG_RECORDING;Object(s.a)(a,n,!0).flush()}else t?d.a.autoTagsToProcess=d.a.autoTagsToProcess.concat(n):d.a.tagsToProcess=d.a.tagsToProcess.concat(n)}},e.run=hj.tryCatch((function(t){hj.isPreview||Object(i.b)(t)||(Object(h.b)("behavior-data.run")||Object(h.a)("behavior-data.run"))&&(e.runRecordings(t),e.runHeatmaps(t))}),"behavior-data.run"),e.runRecordings=function(e){Object(h.b)("behavior-data.runRecordings")&&(p.a.items.SESSION_RESUMED.get()&&a.a.trackSessionResumed(),m(e))},e.runHeatmaps=function(e){hj.hq.each(hj.hq("[data-hj-ignore-attributes]"),(function(e,t){hj.hq(t).find("*").attr("data-hj-ignore-attributes","")})),g(e)},e),!1);var e}),"behavior-data")()}]);
