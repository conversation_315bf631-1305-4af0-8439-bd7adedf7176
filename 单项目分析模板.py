#coding:utf-8
import zipfile
####################################################抬头
import threading
import pandas as pd
import numpy as np
import time
import os
import xlsxwriter
from pathlib import Path
import warnings
import tkinter as tk
import tkinter.messagebox #弹出框
from tkinter import *
from PIL import Image, ImageTk, ImageSequence
import xlsxwriter
import traceback
import datetime
from tkinter import scrolledtext
from datetime import datetime
from openpyxl.utils import get_column_letter
warnings.filterwarnings('ignore')

time_start = time.time()
print("一、数据读取..")
'''
screm_total.insert(INSERT, '\n一、数据读取..', '\n')
window_total.update()
'''
###############################################################################################################一、生成项目预算表
Path_budget = r'单项目分析-数据\01.概算表'

'''#################一、料'''
filename_budget = os.listdir(Path_budget)
for i in range(len(filename_budget)):
    if str(filename_budget[i]).count('~$') == 0:
        # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
        budget_mater = pd.read_excel(Path_budget + '/' + str(filename_budget[i]), header=4, sheet_name='概算总表').loc[
            0, '价格(万)']
        budget_mater=budget_mater*10000

'''#################二、工'''
work = []
for i in range(len(filename_budget)):
    writer = pd.ExcelFile(Path_budget + '\\' + str(filename_budget[i]))
    sheet_len = len(writer.sheet_names)
    for k in range(0, sheet_len):
        if str(writer.sheet_names[k]).count('~$') == 0 and '工' in str(writer.sheet_names[k]) and '费' not in str(
                writer.sheet_names[k]):
            # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
            print('正在读取'+str(writer.sheet_names[k]))
            df_work = pd.read_excel(Path_budget + '/' + str(filename_budget[i]), sheet_name=writer.sheet_names[k],
                                    header=9, usecols='A:M')
            work.append(df_work)
budget_work = pd.concat(work).reset_index(drop=True)[['阶段', '岗位', '工时成本', '阶段.1']]

####工格式
budget_work[['阶段', '岗位', '阶段.1']] = budget_work[['阶段', '岗位', '阶段.1']].fillna('')
budget_work.loc[budget_work['阶段.1'].str.contains('m2|M2'), '阶段.1'] = '产品设计阶段'
budget_work.loc[budget_work['阶段.1'].str.contains('m3|M3'), '阶段.1'] = '生成准备阶段'
budget_work.loc[budget_work['阶段.1'].str.contains('m4|M4'), '阶段.1'] = '调试'
budget_work.loc[budget_work['阶段.1'].str.contains('m5|M5'), '阶段.1'] = '完成预验收'
budget_work.loc[budget_work['阶段.1'].str.contains('m6|M6'), '阶段.1'] = '完成验收'
budget_work['工时成本'] = budget_work['工时成本'].fillna(0)
budget_work = budget_work[budget_work['阶段.1'] != ''].reset_index(drop=True)

budget_work_group = budget_work.groupby(['岗位', '阶段.1'])[['工时成本']].sum().add_suffix('-之和').reset_index()
budget_work_group1 = budget_work_group[budget_work_group['阶段.1'].str.contains('设计')].reset_index(drop=True)
budget_work_group2 = budget_work_group[budget_work_group['阶段.1'].str.contains('准备')].reset_index(drop=True)
budget_work_group3 = budget_work_group[budget_work_group['阶段.1'].str.contains('装配')].reset_index(drop=True)
budget_work_group4 = budget_work_group[budget_work_group['阶段.1'].str.contains('调试')].reset_index(drop=True)
budget_work_group5 = budget_work_group[budget_work_group['阶段.1'].str.contains('完成预验收')].reset_index(drop=True)
budget_work_group6 = budget_work_group[budget_work_group['阶段.1'].str.contains('完成验收')].reset_index(drop=True)
print('工——阶段判断：')
for m in list(budget_work_group['阶段.1'].drop_duplicates()):
    if '-' in str(m):
        print('#######warning##########')
        print('概算工存在不识别阶段：' + str(m))
        print('#######warning##########')

###建立岗位模板
post_list = ["项目总负责人", "售后负责人", "项目经理", "技术负责人", "产品经理", "机械工程师", "电气工程师", "PLC控制工程师", "上位机工程师", "机器人工程师", "视觉工程师",
             "激光工艺工程师", "激光软件工程师", "机械装配技术员", "电气装配技术员", "调试技术员"]
print('工——岗位判断：')
for m in list(budget_work_group['岗位'].drop_duplicates()):
    if str(m) not in post_list:
        print('#######warning##########' )
        print('概算工存在不识别岗位：' + str(m))
        print('#######warning##########')

budget_work_use = pd.DataFrame(post_list, columns=['类别'])
budget_work_use['工时1'] = 10
budget_work_use['人数1'] = 0
budget_work_use['合计1'] = pd.merge(budget_work_use, budget_work_group1, left_on='类别', right_on='岗位', how='left')[
    '工时成本-之和']

budget_work_use['工时2'] = 10
budget_work_use['人数2'] = 0
budget_work_use['合计2'] = pd.merge(budget_work_use, budget_work_group2, left_on='类别', right_on='岗位', how='left')[
    '工时成本-之和']

budget_work_use['工时3'] = 10
budget_work_use['人数3'] = 0
budget_work_use['合计3'] = pd.merge(budget_work_use, budget_work_group3, left_on='类别', right_on='岗位', how='left')[
    '工时成本-之和']

budget_work_use['工时4'] = 10
budget_work_use['人数4'] = 0
budget_work_use['合计4'] = pd.merge(budget_work_use, budget_work_group4, left_on='类别', right_on='岗位', how='left')[
    '工时成本-之和']

budget_work_use['工时5'] = 10
budget_work_use['人数5'] = 0
budget_work_use['合计5'] = pd.merge(budget_work_use, budget_work_group5, left_on='类别', right_on='岗位', how='left')[
    '工时成本-之和']

budget_work_use['工时6'] = 10
budget_work_use['人数6'] = 0
budget_work_use['合计6'] = pd.merge(budget_work_use, budget_work_group6, left_on='类别', right_on='岗位', how='left')[
    '工时成本-之和']
budget_work_use['工时总计'] = 0
for num in range(6):
    budget_work_use['合计' + str(num + 1)] = budget_work_use['合计' + str(num + 1)].fillna(0)
    budget_work_use['工时总计'] = budget_work_use['工时总计'] + budget_work_use['合计' + str(num + 1)]
    budget_work_use['人数' + str(num + 1)] = budget_work_use['合计' + str(num + 1)] / budget_work_use['工时' + str(num + 1)]

budget_work_use['人数1'] = budget_work_use['人数1'] / 26
budget_work_use['人数2'] = budget_work_use['人数2'] / 50
budget_work_use['人数3'] = budget_work_use['人数3'] / 10
budget_work_use['人数4'] = budget_work_use['人数4'] / 20
budget_work_use['人数5'] = budget_work_use['人数5'] / 70
budget_work_use['人数6'] = budget_work_use['人数6'] / 30

'''#################三、费'''
cost = []
for i in range(len(filename_budget)):
    writer = pd.ExcelFile(Path_budget + '\\' + str(filename_budget[i]))
    sheet_len = len(writer.sheet_names)
    for k in range(0, sheet_len):
        if str(writer.sheet_names[k]).count('~$') == 0 and str(writer.sheet_names[k]) in ['费', '海外费', '海外费-物流线']:
            # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
            print('正在读取'+str(writer.sheet_names[k]))
            df_cost = pd.read_excel(Path_budget + '/' + str(filename_budget[i]), sheet_name=writer.sheet_names[k],
                                    header=7, usecols='B:L')
            df_cost = df_cost.rename(columns={df_cost.columns[2]: '金额'})
            cost.append(df_cost)
budget_cost = pd.concat(cost).reset_index(drop=True)[['金额', '阶段', '科目']]

budget_cost[['阶段', '科目']] = budget_cost[['阶段', '科目']].fillna('')
budget_cost['金额'] = budget_cost['金额'].fillna(0)
budget_cost = budget_cost[budget_cost['科目'] != ''].reset_index(drop=True)

budget_cost.loc[budget_cost['科目'].str.contains('住宿'), '科目'] = '住宿费'
budget_cost.loc[budget_cost['科目'].str.contains('交通'), '科目'] = '差旅费'

budget_cost['阶段'] = budget_cost['阶段'].replace('m', 'M', regex=True).astype(str)
budget_cost = budget_cost[budget_cost['阶段'].str.contains('M')].reset_index(drop=True)
print('费--阶段判断：')
for m in list(budget_cost['阶段'].drop_duplicates()):
    if '-' in str(m):
        print('#######warning##########')
        print('概算费存在不识别阶段：' + str(m))
        print('#######warning##########')
print('费--科目判断：')
project_list = ["海外小时工", "出差补贴", "住宿费", "差旅费", "物流费用", "认证费", "其他费用", "签证费", "CCD等供应商海外费"]
for m in list(budget_cost['科目'].drop_duplicates()):
    if str(m) not in project_list:
        print('#######warning##########')
        print('概算费存在不识别科目：' + str(m))
        print('#######warning##########')

budget_cost_group = budget_cost.groupby(['科目', '阶段'])[['金额']].sum().add_suffix('-之和').reset_index()
budget_cost_group1 = budget_cost_group[budget_cost_group['阶段'].str.contains('M1')].reset_index(drop=True)
budget_cost_group2 = budget_cost_group[budget_cost_group['阶段'].str.contains('M2')].reset_index(drop=True)
budget_cost_group3 = budget_cost_group[budget_cost_group['阶段'].str.contains('M3')].reset_index(drop=True)
budget_cost_group4 = budget_cost_group[budget_cost_group['阶段'].str.contains('M4')].reset_index(drop=True)
budget_cost_group5 = budget_cost_group[budget_cost_group['阶段'].str.contains('M5')].reset_index(drop=True)
budget_cost_group6 = budget_cost_group[budget_cost_group['阶段'].str.contains('M6')].reset_index(drop=True)

budget_cost_use = pd.DataFrame(project_list, columns=['科目'])

budget_cost_use['合计1'] = pd.merge(budget_cost_use, budget_cost_group1, left_on='科目', right_on='科目', how='left')['金额-之和']
budget_cost_use['合计2'] = pd.merge(budget_cost_use, budget_cost_group2, left_on='科目', right_on='科目', how='left')['金额-之和']
budget_cost_use['合计3'] = pd.merge(budget_cost_use, budget_cost_group3, left_on='科目', right_on='科目', how='left')['金额-之和']
budget_cost_use['合计4'] = pd.merge(budget_cost_use, budget_cost_group4, left_on='科目', right_on='科目', how='left')['金额-之和']
budget_cost_use['合计5'] = pd.merge(budget_cost_use, budget_cost_group5, left_on='科目', right_on='科目', how='left')['金额-之和']
budget_cost_use['合计6'] = pd.merge(budget_cost_use, budget_cost_group6, left_on='科目', right_on='科目', how='left')['金额-之和']

budget_cost_use['总计'] = 0
for num in range(6):
    budget_cost_use['合计' + str(num + 1)] = budget_cost_use['合计' + str(num + 1)].fillna(0)
    budget_cost_use['总计'] = budget_cost_use['总计'] + budget_cost_use['合计' + str(num + 1)]

len1 = len(budget_cost_use)
budget_cost_use.loc['费合计'] = budget_cost_use.iloc[0:len1, 1:8].sum(axis=0)
budget_cost_use.loc['费合计', '科目'] = '费合计'
budget_cost_use = budget_cost_use.reset_index(drop=True)

############################################################################################################################
Path_summary = r'单项目分析-数据\02.概预核决汇总表'
Path_calcu = r'单项目分析-数据\03.核算明细表'
Path_bom = r'单项目分析-数据\04.BOM明细表'
###############################################################################################################二、概预核决汇总表
filename_summary = os.listdir(Path_summary)
if os.listdir(Path_summary):
    for i in range(len(filename_summary)):
        if str(filename_summary[i]).count('~$') == 0:
            # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
            summary = pd.read_excel(Path_summary + '/' + str(filename_summary[i]), header=1)
            

else:
    summary=pd.DataFrame(columns=['无汇总表'])
###############################################################################################################三、核算明细表表
filename_calcu= os.listdir(Path_calcu)
if os.listdir(Path_calcu):
    for i in range(len(filename_calcu)):
        if str(filename_calcu[i]).count('~$') == 0:
            # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
            po = pd.read_excel(Path_calcu + '/' + str(filename_calcu[i]),sheet_name='采购PO')
            mater = pd.read_excel(Path_calcu + '/' + str(filename_calcu[i]), sheet_name='料')
            work = pd.read_excel(Path_calcu + '/' + str(filename_calcu[i]), sheet_name='工')
            cost = pd.read_excel(Path_calcu + '/' + str(filename_calcu[i]), sheet_name='费')
            po[['采购数量', '未税单价', '采购金额-未税']]=po[['采购数量', '未税单价', '采购金额-未税']].fillna(0)
            po=po.fillna('')

            mater[['数量', '未税单价', '未税金额']] = mater[['数量', '未税单价', '未税金额']].fillna(0)
            mater = mater.fillna('')
else:
    po= pd.DataFrame(columns=['没有采购明细'])
    mater=pd.DataFrame(columns=['没有料'])
    work = pd.DataFrame(columns=['没有工'])
    cost = pd.DataFrame(columns=['没有费'])
###############################################################################################################四、BOM明细表
filename_bom= os.listdir(Path_bom)
if os.listdir(Path_bom):
    for i in range(len(filename_bom)):
        if str(filename_bom[i]).count('~$') == 0:
            # order_new = pd.read_excel(filePathT_1 + '/' + str(file_name1[i]), header=2)
            bom = pd.read_excel(Path_bom + '/' + str(filename_bom[i]))
            bom[['模组用量', '最近本币未税采购单价']]=bom[['模组用量', '最近本币未税采购单价']].fillna(0)
            bom=bom.fillna('')
else:
    bom=pd.DataFrame(columns=['无BOM明细'])

choice=str(input('是否执行物料&模组对比：'))
if '是' in choice:

    #########################################################################物料对比
    pk_mater_po = po[['料件编号', '品名', '规格', '采购数量', '未税单价']]
    pk_mater_po = pk_mater_po.rename(columns={'料件编号': '元件料号'})
    pk_mater_po_use=pk_mater_po.drop_duplicates(subset=['元件料号', '品名', '规格', '未税单价']).reset_index(drop=True)
    pk_mater_po_group = pk_mater_po.groupby(['元件料号', '品名', '规格'])[['采购数量']].sum().add_suffix('').reset_index()

    pk_mater_mat = mater[['料号', '品名', '规格', '数量', '未税单价']]
    pk_mater_mat = pk_mater_mat.rename(columns={'料号': '元件料号', '数量': '领料数量'})

    pk_mater_mat_use=pk_mater_mat.drop_duplicates(subset=['元件料号', '品名', '规格', '未税单价']).reset_index(drop=True)
    pk_mater_mat_group = pk_mater_mat.groupby(['元件料号', '品名', '规格'])[['领料数量']].sum().add_suffix('').reset_index()

    pk_mater_bom = bom[['元件料号', '品名', '规格', '模组用量', '最近本币未税采购单价']]
    pk_mater_bom = pk_mater_bom.rename(columns={'模组用量': 'BOM数量'})
    pk_mater_bom_use=pk_mater_bom.drop_duplicates(subset=['元件料号', '品名', '规格', '最近本币未税采购单价']).reset_index(drop=True)
    pk_mater_bom_group = pk_mater_bom.groupby(['元件料号', '品名', '规格'])[['BOM数量']].sum().add_suffix('').reset_index()


    pk_mater1=pd.merge(pk_mater_po_group,pk_mater_mat_group,left_on=['元件料号','品名','规格'],right_on=['元件料号', '品名', '规格'],how='outer').reset_index(drop=True)
    pk_mater1[['采购数量','领料数量']]=pk_mater1[['采购数量','领料数量']].fillna('无')
    pk_mater=pd.merge(pk_mater1, pk_mater_bom_group, left_on=['元件料号', '品名', '规格'], right_on=['元件料号', '品名', '规格'], how='outer').reset_index(drop=True)
    pk_mater[['采购数量', '领料数量','BOM数量']] = pk_mater[['采购数量', '领料数量','BOM数量']].fillna(19950222)
    pk_mater['数量比较']='不一致'
    pk_mater.loc[(pk_mater['采购数量']==pk_mater['BOM数量'])&(pk_mater['采购数量']==pk_mater['领料数量']),'数量比较']=''
    for num in ['采购数量', '领料数量','BOM数量']:
        pk_mater.loc[(pk_mater[num] == 19950222), num] = '无'


    ###拉单价
    pk_mater['采购单价'] =pd.merge(pk_mater, pk_mater_po_use, left_on=['元件料号', '品名', '规格'], right_on=['元件料号', '品名', '规格'], how='left')['未税单价']
    pk_mater['领料单价'] = pd.merge(pk_mater, pk_mater_mat_use, left_on=['元件料号', '品名', '规格'], right_on=['元件料号', '品名', '规格'], how='left')['未税单价']
    pk_mater['BOM单价']=pd.merge(pk_mater,pk_mater_bom_use,left_on=['元件料号','品名','规格'],right_on=['元件料号', '品名', '规格'],how='left')['最近本币未税采购单价']
    pk_mater[['采购单价', '领料单价', 'BOM单价']] = pk_mater[['采购单价', '领料单价', 'BOM单价']].fillna('无')

    #########################################################################模组对比
    pk_module_po = po[['核算项目号', '作业编号', '品名', '采购数量', '采购金额-未税']]
    pk_module_po['库存管理特征']=pk_module_po['核算项目号']+'-'+pk_module_po['作业编号']
    pk_module_po = pk_module_po.rename(columns={'核算项目号': '项目号'})
    pk_module_po_use = pk_module_po.drop_duplicates(subset=['库存管理特征', '品名']).reset_index(drop=True)
    pk_module_po_group = pk_module_po.groupby(['项目号','库存管理特征', '品名', '作业编号']).agg({'采购数量': "sum", '采购金额-未税': "sum"}).add_suffix('').reset_index()

    pk_module_mat = mater[['核算项目号', '作业编号', '品名', '数量', '未税金额']]
    pk_module_mat['库存管理特征'] = pk_module_mat['核算项目号'] + '-' + pk_module_mat['作业编号']
    pk_module_mat = pk_module_mat.rename(columns={ '数量': '领料数量', '未税金额': "领料金额",'核算项目号': '项目号'})
    pk_module_mat_use = pk_module_mat.drop_duplicates(subset=['库存管理特征', '品名']).reset_index(drop=True)
    pk_module_mat_group = pk_module_mat.groupby(['项目号','库存管理特征', '品名', '作业编号']).agg({'领料数量': "sum", '领料金额': "sum"}).add_suffix('').reset_index()

    pk_module_bom = bom[['项目号', '品名', '模组用量','作业编号','最近本币未税采购单价']]
    pk_module_bom['库存管理特征'] = pk_module_bom['项目号'] + '-' + pk_module_po['作业编号']
    pk_module_bom['BOM金额']=pk_module_bom['模组用量']*pk_module_bom['最近本币未税采购单价']
    pk_module_bom = pk_module_bom.rename(columns={'模组用量': 'BOM数量'})
    pk_module_bom_use = pk_module_bom.drop_duplicates(subset=['库存管理特征', '品名']).reset_index(drop=True)
    pk_module_bom_group = pk_module_bom.groupby(['项目号','库存管理特征', '品名', '作业编号']).agg({'BOM数量': "sum", 'BOM金额': "sum"}).add_suffix('').reset_index()

    pk_module1 = pd.merge(pk_module_po_group, pk_module_mat_group, left_on=['项目号','库存管理特征', '品名', '作业编号'],right_on=['项目号','库存管理特征', '品名', '作业编号'], how='outer').reset_index(drop=True)

    pk_module = pd.merge(pk_module1, pk_module_bom_group, left_on=['项目号','库存管理特征', '品名', '作业编号'], right_on=['项目号','库存管理特征', '品名', '作业编号'],how='outer').reset_index(drop=True)
    pk_module[['采购数量', '采购金额-未税','领料数量','领料金额','BOM数量','BOM金额']] = pk_module[['采购数量', '采购金额-未税','领料数量','领料金额','BOM数量','BOM金额']].fillna(19950222)
    pk_module['数量比较'] = '不一致'
    pk_module.loc[(pk_module['采购数量'] == pk_module['BOM数量']) & (pk_module['采购数量'] == pk_module['领料数量']), '数量比较'] = ''
    for num in ['采购数量', '采购金额-未税','领料数量','领料金额','BOM数量','BOM金额']:
        pk_module.loc[(pk_module[num] == 19950222), num] = '无'
    pk_module=pk_module.reindex(columns=['项目号','库存管理特征','品名','作业编号','采购数量','领料数量','BOM数量','数量比较','采购金额-未税','领料金额','BOM金额'])

    #################################################################成本概况
    expenses=pd.DataFrame(columns=['科目','预算','占比','核算','预算-核算'])
    expenses.loc[0,'科目']='合计'
    expenses.loc[1, '科目'] = '料'
    expenses.loc[2, '科目'] = '生产工'
    expenses.loc[3, '科目'] = '交付工'
    expenses.loc[4, '科目'] = '设计工'
    expenses.loc[5, '科目'] = '费'

    expenses.loc[1, '预算'] = budget_mater  #料
    expenses.loc[2, '预算'] = summary['生产工.1'].sum()  # 生产工
    expenses.loc[3, '预算'] = summary['交付工.1'].sum() # 交付工
    expenses.loc[4, '预算'] = summary['设计工.1'].sum() # 设计工
    expenses.loc[5, '预算'] = summary['费.1'].sum() # 费
    expenses.loc[0, '预算'] =expenses.loc[1, '预算']+expenses.loc[2, '预算']+expenses.loc[3, '预算']+expenses.loc[4, '预算']+expenses.loc[5, '预算'] # 合计

    expenses.loc[1, '核算'] =summary['料.2'].sum()  # 料
    expenses.loc[2, '核算'] =summary['生产工.2'].sum()  # 生产工
    expenses.loc[3, '核算'] =summary['交付工.2'].sum() # 交付工
    expenses.loc[4, '核算'] =summary['设计工.2'].sum() # 设计工
    expenses.loc[5, '核算'] =summary['费.2'].sum()  # 费
    expenses.loc[0, '核算'] = expenses.loc[1, '核算'] + expenses.loc[2, '核算'] + expenses.loc[3, '核算'] + expenses.loc[4, '核算'] + expenses.loc[5, '核算']  # 合计

    expenses['预算-核算']=expenses['预算']-expenses['核算']
    expenses['备注']=''

else:
    pk_mater=pd.DataFrame(columns=['无需进行物料分析'])
    pk_module=pd.DataFrame(columns=['无需进行模组分析'])




def writer_contents(sheet, array, start_row, start_col, format=None, percent_format=None, percentlist=[]):
    # start_col = 0
    for col in array:
        if percentlist and (start_col in percentlist):
            sheet.write_column(start_row, start_col, col, percent_format)
        else:
            sheet.write_column(start_row, start_col, col, format)
        start_col += 1


def write_color(book, sheet, data, fmt, col_num='I'):
    start = 3
    format_red = book.add_format({'font_name': 'Arial',
                                  'font_size': 10,
                                  'bg_color': '#F86470'})
    format_red.set_align('center')
    format_red.set_align('vcenter')

    for item in data:
        if '找不到' in str(item) in str(item):
            sheet.write(col_num + str(start), item, format_red)
        else:
            sheet.write(col_num + str(start), item, fmt)
        start += 1


# 写入表格
print('5.1：正在设置表格格式...')

now_time = time.strftime("%Y-%m-%d-%H", time.localtime(time.time()))
book_name = '单项目分析' + now_time
workbook = xlsxwriter.Workbook(book_name + '.xlsx', {'nan_inf_to_errors': True})
worksheet0 = workbook.add_worksheet('结案报告')
worksheet1= workbook.add_worksheet('项目预算表')
worksheet3= workbook.add_worksheet('BOM明细表')
worksheet2= workbook.add_worksheet('概预核决汇总表')
worksheet4= workbook.add_worksheet('采购PO')
worksheet5= workbook.add_worksheet('料')
worksheet6= workbook.add_worksheet('工')
worksheet7= workbook.add_worksheet('费')
worksheet8= workbook.add_worksheet('料分析')
worksheet9= workbook.add_worksheet('模组分析')
######主色调
title_format = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 10,
                                    'font_color': 'white',
                                    'bg_color': '#1F4E78',
                                    'bold': True,
                                    'align': 'center',
                                    'valign': 'vcenter',
                                    'border': 1,
                                    'border_color': 'white'
                                    })
title_format.set_align('vcenter')

######分析模板
title_format_main = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 16,
                                    'font_color': 'white',
                                    'bg_color': '#16365C',
                                    'bold': True,
                                    'align': 'right',
                                    'valign': 'vcenter',
                                    'border': 0,
                                    'border_color': 'white'
                                    })
title_format_main.set_align('vcenter')

title_format_second = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 10,
                                    'font_color': 'white',
                                    'bg_color': '#16365C',
                                    'bold': True,
                                    'align': 'left',
                                    'valign': 'vcenter',
                                    'border': 0,
                                    'border_color': 'white'
                                    })
title_format_second.set_align('vcenter')
######分析模板--目录
title_format_contents1 = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 11,
                                    'font_color': 'black',
                                    'bg_color': '#DCE6F1',
                                    'bold': True,
                                    'align': 'left',
                                    'valign': 'vcenter',
                                    'border': 1,
                                    'border_color': 'white'
                                    })
title_format_contents1.set_align('vcenter')

title_format_contents2= workbook.add_format({'font_name': 'Arial',
                                    'font_size': 11,
                                    'font_color': 'black',
                                    'bg_color': '#EBF1DE',
                                    'bold': True,
                                    'align': 'left',
                                    'valign': 'vcenter',
                                    'border': 1,
                                    'border_color': 'white'
                                    })
title_format_contents2.set_align('vcenter')

title_format_contents3= workbook.add_format({'font_name': 'Arial',
                                    'font_size': 11,
                                    'font_color': 'black',
                                    'bg_color': '#E4DFEC',
                                    'bold': True,
                                    'align': 'left',
                                    'valign': 'vcenter',
                                    'border': 1,
                                    'border_color': 'white'
                                    })
title_format_contents3.set_align('vcenter')

title_format_contents4= workbook.add_format({'font_name': 'Arial',
                                    'font_size': 11,
                                    'font_color': 'black',
                                    'bg_color': '#FDE9D9',
                                    'bold': True,
                                    'align': 'left',
                                    'valign': 'vcenter',
                                    'border': 1,
                                    'border_color': 'white'
                                    })
title_format_contents4.set_align('vcenter')

title_format_contents5= workbook.add_format({'font_name': 'Arial',
                                    'font_size': 11,
                                    'font_color': 'black',
                                    'bg_color': '#DAEEF3',
                                    'bold': True,
                                    'align': 'left',
                                    'valign': 'vcenter',
                                    'border': 1,
                                    'border_color': 'white'
                                    })
title_format_contents5.set_align('vcenter')

###################################项目预算表格式
######主色调
main_format = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 16,
                                    'font_color': 'black',
                                    'bg_color': '#D9D9D9',
                                    'bold': True,
                                    'align': 'center',
                                    'valign': 'vcenter',
                                    'right': 1,
                                    'bottom':0,
                                    'border_color': 'black'
                                    })
main_format.set_align('vcenter')
main_format1 = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 14,
                                    'font_color': 'black',
                                    'bg_color': '#D9D9D9',
                                    'bold': True,
                                    'align': 'center',
                                    'valign': 'vcenter',
                                    'right': 1,
                                    'top':0,
                                    'border_color': 'black'
                                    })
main_format1.set_align('vcenter')
title_format = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 10,
                                    'font_color': 'black',
                                    'bg_color': 'D9D9D9',
                                    'bold': True,
                                    'align': 'center',
                                    'valign': 'vcenter',
                                    'border': 1,
                                    'border_color': 'black'
                                    })
title_format.set_align('vcenter')
######项目&数量  |存货
title_format1 = workbook.add_format({'font_name': 'Arial',
                                     'font_size': 10,
                                     'font_color': 'black',
                                     'bg_color': '#FFFFFF',
                                     'bold': True,
                                     'align': 'center',
                                     'valign': 'vcenter',
                                     'border': 1,
                                     'border_color': 'black'
                                     })
title_format1.set_align('vcenter')

col_format = workbook.add_format({'font_name': 'Arial',
                                  'font_size': 8,
                                  'font_color': 'white',
                                  'bg_color': '#595959',
                                  'text_wrap': True,
                                  'border': 1,
                                  'border_color': 'white',
                                  'align': 'center',
                                  'valign': 'vcenter'
                                  })

data_format = workbook.add_format({'font_name': 'Arial',
                                   'font_size': 10,
                                   'align': 'center',
                                   'valign': 'vcenter',
                                   'text_wrap': True,
                                   'border': 1,
                                    'border_color': 'black'
                                   })
data_format1 = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 10,
                                    'align': 'center',
                                    'bg_color': '#F2F2F2',
                                    'valign': 'vcenter',
                                    'text_wrap': True,
                                   'border': 1,
                                    'border_color': 'black'
                                    })
data_format1_1 = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 10,
                                    'align': 'left',
                                    'bg_color': '#F2F2F2',
                                    'valign': 'vcenter',
                                    'text_wrap': True,
                                   'border': 1,
                                    'border_color': 'black'
                                    })
data_format2 = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 10,
                                    'align': 'center',
                                    'valign': 'vcenter'
                                    })
data_format2.set_num_format('0.00')
data_format3 = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 10,
                                    'align': 'center',
                                    'valign': 'vcenter'
                                    })
data_format3.set_num_format('0.00%')
num_percent_data_format = workbook.add_format({'font_name': 'Arial',
                                               'font_size': 10,
                                               'align': 'center',
                                               'valign': 'vcenter',
                                               'num_format': '0.00%'
                                               })
statis_format2 = workbook.add_format({'font_name': 'Arial',  # 系列总计
                                      'font_size': 9,
                                      'align': 'center',
                                      'valign': 'vcenter',
                                      'bg_color': '#92CDDC'
                                      })
data_format_percent = workbook.add_format({'font_name': 'Arial',
                                           'font_size': 10,
                                           'align': 'center',
                                           'valign': 'vcenter'
                                           })
data_format_percent.set_num_format('0.00%')

################先把数据写入报告页
worksheet0.write_row("B3", expenses.columns, title_format)
writer_contents(sheet=worksheet0, array=expenses.T.values, start_row=3, start_col=1)

# 设置外框边框加粗样式
bold_border_left = workbook.add_format({'left': 2,'border_color': 'black'})
bold_border_right = workbook.add_format({'right': 2,'border_color': 'black'})
bold_border_top= workbook.add_format({'top': 2,'border_color': 'black'})

####输出
# 在单元格A1中写入数据并应用粗边框样式
#worksheet0.hide_gridlines(2)
#worksheet0.set_bottom_border('B1:Q64', {'style': 'bold'})
#worksheet0.write_row('B1:Q64', '', bold_border)
worksheet0.hide_gridlines(2)
#worksheet0.conditional_format('S2:T5', {'type':'no_blanks', 'format': bold_border})


worksheet0.merge_range('B1:H1', '项目阶段性结案报告', title_format_main)
worksheet0.merge_range('I1:Q1', '----数据截至'+time.strftime("%Y-%m-%d", time.localtime(time.time())), title_format_second)
worksheet0.merge_range('B2:Q2', '一、成本概况', title_format_contents1)
worksheet0.merge_range('B11:Q11', '二、料情况', title_format_contents2)
worksheet0.merge_range('B19:Q19', '三、工时情况-预算', title_format_contents3)
worksheet0.merge_range('B38:Q38', '四、工时情况-核算VS预算', title_format_contents4)
worksheet0.merge_range('B55:Q55', '五、费情况', title_format_contents5)
#####设置外框粗
for i in range(64):
    i=i+1
    worksheet0.write('A'+str(i), '', bold_border_right)
    worksheet0.write('R' + str(i), '', bold_border_left)
for i in range(2,18):
    worksheet0.write(get_column_letter(i) + str(65),'', bold_border_top)
worksheet0.set_row(0, 42)
worksheet0.set_row(1, 19.5)
worksheet0.set_row(10, 19.5)
worksheet0.set_row(9, 72)
worksheet0.set_row(17, 72)
worksheet0.set_row(18, 19.5)
worksheet0.set_row(37, 19.5)
worksheet0.set_row(53, 72)
worksheet0.set_row(54, 19.5)
worksheet0.set_row(62, 72)
worksheet0.set_column("B:B", 5)
worksheet0.set_column("C:F", 15)
#参数0，不隐藏
#参数1，仅隐藏打印的网格线
#参数2，隐藏屏幕和打印的网格线
#############项目预算表
worksheet1.merge_range('A2:U2', '项目预算表', main_format1)
worksheet1.merge_range('A1:U1', '海目星激光', main_format)
worksheet1.merge_range('A3:B3', '项目名称', title_format)
worksheet1.merge_range('C3:H3', '', title_format1)
worksheet1.merge_range('I3:K3', '项目号', title_format)
worksheet1.merge_range('L3:Q3', '', title_format1)
worksheet1.merge_range('R3:T3', '订单数量', title_format)
worksheet1.write('U3', '', title_format1)

worksheet1.merge_range('A4:A5', '大类', title_format)
worksheet1.merge_range('B4:B5', '小类', title_format)
worksheet1.merge_range('C4:E5', '产品设计阶段', title_format)
worksheet1.merge_range('F4:H5', '生产准备阶段', title_format)
worksheet1.merge_range('I4:N4', '装配调试阶段（设变率1%）', title_format)
worksheet1.merge_range('I5:K5', '装配', title_format)
worksheet1.merge_range('L5:N5', '装配', title_format)
worksheet1.merge_range('O4:T4', '装配调试阶段（设变率1%）', title_format)
worksheet1.merge_range('O5:Q5', '完成预验收', title_format)
worksheet1.merge_range('R5:T5', '完成验收', title_format)
worksheet1.merge_range('U4:U5', '合计',title_format)

worksheet1.merge_range('A6:A7', '料\t(元)',data_format1)
worksheet1.write('B6', '装备线材料费', data_format1)
worksheet1.write('B7', '合计', data_format1)

worksheet1.merge_range('C6:E6', 0, data_format)
worksheet1.merge_range('F6:H6', budget_mater*0.95, data_format)
worksheet1.merge_range('I6:K6', budget_mater*0.02, data_format)
worksheet1.merge_range('L6:N6', budget_mater*0.02, data_format)
worksheet1.merge_range('O6:Q6', budget_mater*0.01, data_format)
worksheet1.merge_range('R6:T6', 0, data_format)
worksheet1.write('U6', budget_mater, data_format)

worksheet1.merge_range('C7:E7', 0, data_format1)
worksheet1.merge_range('F7:H7', budget_mater*0.95, data_format1)
worksheet1.merge_range('I7:K7', budget_mater*0.02, data_format1)
worksheet1.merge_range('L7:N7', budget_mater*0.02, data_format1)
worksheet1.merge_range('O7:Q7', budget_mater*0.01, data_format1)
worksheet1.merge_range('R7:T7', 0, data_format1)
worksheet1.write('U7', budget_mater, data_format1)

worksheet1.merge_range('A8:A27', "工", data_format1)
worksheet1.write('B8', "周期", data_format1)
worksheet1.merge_range('C8:E8', 26, data_format)
worksheet1.merge_range('F8:H8', 50, data_format)
worksheet1.merge_range('I8:K8', 10, data_format)
worksheet1.merge_range('L8:N8', 20, data_format)
worksheet1.merge_range('O8:Q8', 70, data_format)
worksheet1.merge_range('R8:T8', 30, data_format)
worksheet1.write('U8', '/', data_format)
worksheet1.write_row('B9:U9',["类别","工时/天","人数","合计","工时/天","人数","合计","工时/天","人数","合计","工时/天","人数","合计","工时/天","人数","合计","工时/天","人数","合计","工时总计"],data_format1)
#worksheet1.write_row('C10:U25',budget_work_use.T.values,data_format)
#writer_contents(sheet=worksheet1, array=budget_work_use.T.values, start_row=9, start_col=1)
worksheet1.write_column('B10:B25',list(budget_work_use[budget_work_use.columns[0]]),data_format1)
for num in range(3,22):
    worksheet1.write_column(get_column_letter(num)+'10:'+get_column_letter(num)+'25', list(budget_work_use[budget_work_use.columns[num-2]]), data_format)
worksheet1.write('B26', '工时合计-H', data_format1)
worksheet1.write('B27', '工成本金额合计', data_format1)
worksheet1.merge_range('C26:E26', '=sum(E10:E25)', data_format1)
worksheet1.merge_range('F26:H26', '=sum(H10:H25)', data_format1)
worksheet1.merge_range('I26:K26', '=sum(K10:K25)', data_format1)
worksheet1.merge_range('L26:N26', '=sum(N10:N25)', data_format1)
worksheet1.merge_range('O26:Q26', '=sum(Q10:Q25)', data_format1)
worksheet1.merge_range('R26:T26', '=sum(T10:T25)', data_format1)
worksheet1.write('U26','=sum(U10:U25)', data_format1)

worksheet1.merge_range('C27:E27', '=sum(E10:E22)*75+SUM(E23:E25)*50', data_format1)
worksheet1.merge_range('F27:H27', '=sum(H10:H22)*75+SUM(H23:H25)*50', data_format1)
worksheet1.merge_range('I27:K27', '=sum(K10:K22)*75+SUM(K23:K25)*50', data_format1)
worksheet1.merge_range('L27:N27', '=sum(N10:N22)*75+SUM(N23:N25)*50', data_format1)
worksheet1.merge_range('O27:Q27', '=sum(Q10:Q22)*75+SUM(Q23:Q25)*50', data_format1)
worksheet1.merge_range('R27:T27', '=sum(T10:T22)*75+SUM(T23:T25)*50', data_format1)
worksheet1.write('U27','=sum(U10:U22)*75+SUM(U23:U25)*50', data_format1)

worksheet1.merge_range('A28:A37', "费", data_format1)
worksheet1.write_column('B28:B37',list(budget_cost_use[budget_cost_use.columns[0]]),data_format1)

t=0
for i in range(3,19):
    if i%3==0:
        t=t+1
        k=0
        for j in range(28,38):
            worksheet1.merge_range(get_column_letter(i)+str(j)+':'+get_column_letter(i+2)+str(j),budget_cost_use.loc[k,budget_cost_use.columns[t]],data_format)
            k=k+1

for i in range(28,37):
    worksheet1.write('U'+str(i), '=sum(C%d+F%d+I%d+L%d+O%d+R%d)'%(i,i,i,i,i,i),data_format)



worksheet1.write('C37','=sum(C28:C36)', data_format1)
worksheet1.write('F37','=sum(F28:F36)', data_format1)
worksheet1.write('I37','=sum(I28:I36)', data_format1)
worksheet1.write('L37','=sum(L28:L36)', data_format1)
worksheet1.write('O37','=sum(O28:O36)', data_format1)
worksheet1.write('R37','=sum(R28:R36)', data_format1)
worksheet1.write('U37','=sum(U28:U36)', data_format1)

worksheet1.merge_range('A38:U38', '1、此表在立项前由项目经理编制，经项目总负责人、交付中心负责人、产品线负责人、经管部负责人审核，由海外业务中心负责人审批；\n2、需将《预算总表》作为附件一同签批；\n3、此表作为《项目指标责任书》的附件一同签批；\n4、如在项目执行期间，因客观因素导致项目预算发生变化，需要按照此表重新发起预算变更申请。', data_format1_1)
worksheet1.merge_range('A39:G39', '制表：', data_format)
worksheet1.merge_range('H39:N39', '审核：', data_format)
worksheet1.merge_range('O39:U39', '审批：', data_format)

worksheet1.set_column("A:A", 5)
worksheet1.set_column("B:B", 12)
worksheet1.set_column("C:T", 6)
'''
worksheet1.set_column("B10:B25", 10,data_format1)
worksheet1.set_column("C10:T25", 6,data_format)
worksheet1.set_column("U10:U25", 8,data_format)
get_column_letter(c + 1)
'''
worksheet1.set_row(0, 28)
worksheet1.set_row(1, 23)
worksheet1.set_row(2, 20)
worksheet1.set_row(3, 18)
worksheet1.set_row(4, 18)
worksheet1.set_row(37, 80)
worksheet1.set_row(38, 80)

######主色调
title_format = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 10,
                                    'font_color': 'white',
                                    'bg_color': '#1F4E78',
                                    'bold': True,
                                    'align': 'center',
                                    'valign': 'vcenter',
                                    'border': 1,
                                    'border_color': 'white'
                                    })
title_format.set_align('vcenter')
######项目&数量  |存货
title_format1 = workbook.add_format({'font_name': 'Arial',
                                     'font_size': 10,
                                     'font_color': 'white',
                                     'bg_color': '#974706',
                                     'bold': True,
                                     'align': 'center',
                                     'valign': 'vcenter',
                                     'border': 1,
                                     'border_color': 'white'
                                     })
title_format1.set_align('vcenter')
######出货&验收
title_format2 = workbook.add_format({'font_name': 'Arial',
                                     'font_size': 10,
                                     'font_color': 'white',
                                     'bg_color': '#215967',
                                     'bold': True,
                                     'align': 'center',
                                     'valign': 'vcenter',
                                     'border': 1,
                                     'border_color': 'white'
                                     })
title_format2.set_align('vcenter')
######收入\滚动预测
title_format3 = workbook.add_format({'font_name': 'Arial',
                                     'font_size': 10,
                                     'font_color': 'white',
                                     'bg_color': '#7030A0',
                                     'bold': True,
                                     'align': 'center',
                                     'valign': 'vcenter',
                                     'border': 1,
                                     'border_color': 'white'
                                     })
title_format3.set_align('vcenter')
######预算
title_format4 = workbook.add_format({'font_name': 'Arial',
                                     'font_size': 10,
                                     'font_color': 'white',
                                     'bg_color': '#BF6753',
                                     'bold': True,
                                     'align': 'center',
                                     'valign': 'vcenter',
                                     'border': 1,
                                     'border_color': 'white'
                                     })
title_format4.set_align('vcenter')
######核算
title_format5 = workbook.add_format({'font_name': 'Arial',
                                     'font_size': 10,
                                     'font_color': 'white',
                                     'bg_color': '#00B050',
                                     'bold': True,
                                     'align': 'center',
                                     'valign': 'vcenter',
                                     'border': 1,
                                     'border_color': 'white'
                                     })
title_format5.set_align('vcenter')
######还需
title_format6 = workbook.add_format({'font_name': 'Arial',
                                     'font_size': 10,
                                     'font_color': 'white',
                                     'bg_color': '#E26B0A',
                                     'bold': True,
                                     'align': 'center',
                                     'valign': 'vcenter',
                                     'border': 1,
                                     'border_color': 'white'
                                     })
title_format6.set_align('vcenter')
######财务|年初预算
title_format7 = workbook.add_format({'font_name': 'Arial',
                                     'font_size': 10,
                                     'font_color': 'white',
                                     'bg_color': '#FF0000',
                                     'bold': True,
                                     'align': 'center',
                                     'valign': 'vcenter',
                                     'border': 1,
                                     'border_color': 'white'
                                     })
title_format7.set_align('vcenter')
######项目进度
title_format8 = workbook.add_format({'font_name': 'Arial',
                                     'font_size': 10,
                                     'font_color': 'white',
                                     'bg_color': '#00B0F0',
                                     'bold': True,
                                     'align': 'center',
                                     'valign': 'vcenter',
                                     'border': 1,
                                     'border_color': 'white'
                                     })
title_format8.set_align('vcenter')
col_format = workbook.add_format({'font_name': 'Arial',
                                  'font_size': 8,
                                  'font_color': 'white',
                                  'bg_color': '#595959',
                                  'text_wrap': True,
                                  'border': 1,
                                  'border_color': 'white',
                                  'align': 'center',
                                  'valign': 'vcenter'
                                  })

data_format = workbook.add_format({'font_name': 'Arial',
                                   'font_size': 10,
                                   'align': 'left',
                                   'valign': 'vcenter'
                                   })
data_format1 = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 10,
                                    'align': 'center',
                                    'valign': 'vcenter'
                                    })
data_format2 = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 10,
                                    'align': 'center',
                                    'valign': 'vcenter'
                                    })
data_format2.set_num_format('0.00')
data_format3 = workbook.add_format({'font_name': 'Arial',
                                    'font_size': 10,
                                    'align': 'center',
                                    'valign': 'vcenter'
                                    })
data_format3.set_num_format('0.00%')
num_percent_data_format = workbook.add_format({'font_name': 'Arial',
                                               'font_size': 10,
                                               'align': 'center',
                                               'valign': 'vcenter',
                                               'num_format': '0.00%'})
statis_format2 = workbook.add_format({'font_name': 'Arial',  # 系列总计
                                      'font_size': 9,
                                      'align': 'center',
                                      'valign': 'vcenter',
                                      'bg_color': '#92CDDC'
                                      })
data_format_percent = workbook.add_format({'font_name': 'Arial',
                                           'font_size': 10,
                                           'align': 'center',
                                           'valign': 'vcenter'
                                           })
data_format_percent.set_num_format('0.00%')

worksheet2.write_row("A2", summary.columns, title_format)
writer_contents(sheet=worksheet2, array=summary.T.values, start_row=2, start_col=0)
# end = len(report_work1) + 1
worksheet2.merge_range('A1:Q1', '项目基础信息', title_format)
worksheet2.merge_range('R1:U1', '计划出货/验收', title_format2)
worksheet2.merge_range('V1:Y1', '收入数据', title_format3)
worksheet2.merge_range('Z1:AG1', '概算数据', title_format)
worksheet2.merge_range('AH1:AO1', '预算数据', title_format4)
worksheet2.merge_range('AP1:BC1', '核算数据', title_format5)
worksheet2.merge_range('BD1:BK1', '还需数据', title_format6)
worksheet2.merge_range('BL1:BU1', '滚动预测数据', title_format3)
worksheet2.merge_range('BV1:CA1', '财务成本数据', title_format7)
worksheet2.merge_range('CB1:CE1', '存货数据', title_format1)
worksheet2.merge_range('CF1:CL1', '辅助信息', title_format)
worksheet2.merge_range('CM1:CU1', '项目进度信息', title_format8)
worksheet2.merge_range('CV1:DC1', '年初预算数据', title_format7)
worksheet2.merge_range("DD1:DF1", '工时汇总', title_format5)

worksheet2.write_row("J2:L2", ['项目数量', '已出货数量', '在产数量'], title_format1)
worksheet2.write_row("R2:U2", ['出货年份', '出货月份', '验收年份', '验收月份'], title_format2)
worksheet2.write_row("V2:Y2", ['事业部收入', '集团收入', '软件收入', '硬件收入'], title_format3)
worksheet2.write_row("Z2:AG2", ['成本合计', '料', '生产工', '交付工', '设计工', '其他费', '毛利', '毛利率'], title_format)
worksheet2.write_row("AH2:AO2", ['成本合计', '料', '生产工', '交付工', '设计工', '其他费', '毛利', '毛利率'], title_format4)
worksheet2.write_row("AP2:BC2",
                     ['采购PO', '原材料-存货', '成本合计', '料', '工单料', '设变料', '工', '生产工', '交付工', '费', '设计工', '其他费','毛利','毛利率'], title_format5)
worksheet2.write_row("BD2:BK2", ['成本合计', '料', '工', '生产工', '交付工', '费', '设计工', '其他费'], title_format6)
worksheet2.write_row("BL2:BU2", ['成本合计', '料', '工', '生产工', '交付工', '费', '设计工', '其他费', '毛利', '毛利率'],title_format3)
worksheet2.write_row("BV2:CA2", ['成本合计', '料', '工', '费', '毛利', '毛利率'], title_format7)
worksheet2.write_row("CB2:CE2", ['成本合计', '料', '工', '费'], title_format1)
worksheet2.write_row("CM2:CU2",['实际出货时间', '实际验收时间', '系统验收时间', '是否YY','OA状态', '是否有风险', '风险分类', '生产实际进度', '验收实际进度'],title_format8)
worksheet2.write_row("CV2:DC2", ['成本合计', '料', '工', '生产工', '交付工', '费', '设计工', '其他费'], title_format7)
worksheet2.write_row("DD2:DF2", ['工时', '生产工时', '交付工时'], title_format5)
worksheet2.set_row(0, 25)
worksheet2.set_row(1, 22)
worksheet2.set_column('AG:AG', 8, data_format_percent)
worksheet2.set_column('AO:AO', 8, data_format_percent)
worksheet2.set_column('BC:BC', 8, data_format_percent)
worksheet2.set_column('BU:BU', 8, data_format_percent)
worksheet2.set_column('CA:CA', 8, data_format_percent)
worksheet2.set_column('AF:AF', 8, data_format2)
worksheet2.set_column('AN:AN', 8, data_format2)
worksheet2.set_column('BB:BB', 8, data_format2)
worksheet2.set_column('BT:BT', 8, data_format2)
worksheet2.set_column('BZ:BZ', 8, data_format2)

##########写入公式
for i in range(len(summary)):
    n=i+3
    worksheet2.write_formula('BL'+str(n), '=IF(BD%d<>"",IF(AP%d<>"",AR%d+BD%d,""),"")'%(n,n,n,n)) #'=B2+C2+D2'
    worksheet2.write_formula('BM' + str(n), '=IF(BE%d<>"",IF(AS%d<>"",AS%d+BE%d,""),"")'%(n,n,n,n))
    worksheet2.write_formula('BN' + str(n), '=IF(AV%d<>"",IF(BF%d<>"",AV%d+BF%d,""),"")'%(n,n,n,n))
    worksheet2.write_formula('BO' + str(n), '=IF(AW%d<>"",IF(BG%d<>"",AW%d+BG%d,""),"")'%(n,n,n,n))
    worksheet2.write_formula('BP' + str(n), '=IF(AX%d<>"",IF(BH%d<>"",AX%d+BH%d,""),"")'%(n,n,n,n))
    worksheet2.write_formula('BQ' + str(n), '=IF(AY%d<>"",IF(BI%d<>"",AY%d+BI%d,""),"")'%(n,n,n,n))
    worksheet2.write_formula('BR' + str(n), '=IF(AZ%d<>"",IF(BJ%d<>"",AZ%d+BJ%d,""),"")'%(n,n,n,n))
    worksheet2.write_formula('BS' + str(n), '=IF(BA%d<>"",IF(BK%d<>"",BA%d+BK%d,""),"")'%(n,n,n,n))
    worksheet2.write_formula('BT' + str(n), '=IF(W%d<>"",IF(BL%d<>"",W%d-BL%d,""),"")'%(n,n,n,n))
    worksheet2.write_formula('BU' + str(n), '=IF(W%d<>"",IF(W%d<>0,IF(BT%d<>"",BT%d/W%d,""),""),"")' % (n, n, n, n,n))
#################bom
worksheet3.write_row("A1", bom.columns, title_format)
writer_contents(sheet=worksheet3, array=bom.T.values, start_row=1, start_col=0)

#################po
worksheet4.write_row("A1", po.columns, title_format)
writer_contents(sheet=worksheet4, array=po.T.values, start_row=1, start_col=0)

#################料
worksheet5.write_row("A1", mater.columns, title_format)
writer_contents(sheet=worksheet5, array=mater.T.values, start_row=1, start_col=0)

#################工
worksheet6.write_row("A1", work.columns, title_format)
writer_contents(sheet=worksheet6, array=work.T.values, start_row=1, start_col=0)

#################费
worksheet7.write_row("A1", cost.columns, title_format)
writer_contents(sheet=worksheet7, array=cost.T.values, start_row=1, start_col=0)

#################料分析
worksheet8.merge_range("A1:C1", '基础信息', title_format)
worksheet8.merge_range("D1:G1", '数量对比', title_format)
worksheet8.merge_range("H1:J1", '单价对比', title_format)
worksheet8.write_row("A2", pk_mater.columns, title_format)
writer_contents(sheet=worksheet8, array=pk_mater.T.values, start_row=2, start_col=0)
worksheet8.set_row(0, 25)

#################模组分析
worksheet9.merge_range("A1:D1", '基础信息', title_format)
worksheet9.merge_range("E1:H1", '数量对比', title_format)
worksheet9.merge_range("I1:K1", '金额对比', title_format)
worksheet9.write_row("A2", pk_module.columns, title_format)
writer_contents(sheet=worksheet9, array=pk_module.T.values, start_row=2, start_col=0)
worksheet9.set_row(0, 25)

workbook.close()

