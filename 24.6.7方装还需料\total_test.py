#coding:utf-8
import zipfile
####################################################抬头
import threading
import pandas as pd
#import modin.pandas as pd
import numpy as np
import time
import os
import xlsxwriter
from pathlib import Path
import warnings
import tkinter as tk
import tkinter.messagebox #弹出框
from tkinter import *
from PIL import Image, ImageTk, ImageSequence
import xlsxwriter
import traceback
import datetime
from tkinter import scrolledtext
from datetime import datetime
from picture import *
import gc
from wholeuse import*
from part_base import *
from part_sea_base import *
from part_calculate import *
from part_sea_summary import *
from part_sea_calculate import *
from part_n3on import *
from part_n3off import *
from part_summary import *
from part_transform import *
from part_proj_rece_locked import *
roll=Scrollbar(window_total,orient='vertical',command=frame_total.yview)
#frame.pack(fill="both",side='right')
frame_total['yscrollcommand']=roll.set
roll.pack(side=RIGHT, fill=Y)
frame_total.pack(side=TOP, fill=Y, expand=True)
image_file = ImageTk.PhotoImage(file=r'0、软件附带文件\大背景.jpg')
image =frame_total.create_image(0, 0, anchor='n', image=image_file)
image1 =frame_total.create_image(600, 0, anchor='n', image=image_file)
image_file1 = ImageTk.PhotoImage(file=r'0、软件附带文件\海目星.png')
image_new =frame_total.create_image(530, 490, anchor='n', image=image_file1)
###底表按钮
image_base = ImageTk.PhotoImage(file=r'0、软件附带文件\Purple White.jpg')
button_base= tk.Button(frame_total, text='底表', image=image_base,width=140, height=26, fg='black',font=('华文行楷', 18),compound=CENTER,cursor="star")
frame_total.create_window(180,110,window=button_base)
###核算
image_cal = ImageTk.PhotoImage(file=r'0、软件附带文件\Cool Sky.jpg')
button_cal= tk.Button(frame_total,image=image_cal, text='核算', width=140, height=26, fg='black',font=('华文行楷', 18),compound=CENTER,cursor="star")
frame_total.create_window(180,195,window=button_cal)
###周进度表
image_abance = ImageTk.PhotoImage(file=r'0、软件附带文件\Pale Wood.jpg')
button_abance= tk.Button(frame_total, text='周进度',image=image_abance,  width=140, height=26, fg='black',font=('华文行楷', 17),compound=CENTER,cursor="star")
frame_total.create_window(180,280,window=button_abance)
###生成-汇总表
image_total = ImageTk.PhotoImage(file=r'0、软件附带文件\Aqualicious.jpg')
button_total= tk.Button(frame_total, text='概预核决', image=image_total,  width=140, height=26, fg='black',font=('华文行楷', 18),compound=CENTER,cursor="star")
frame_total.create_window(180,365,window=button_total)
####进度表是否启用n+3
image_n3on = ImageTk.PhotoImage(file=r'0、软件附带文件\Superman.jpg')
n3_on = tk.Button(frame_total, text='启用N+3', width=140, height=26,image=image_n3on,fg='black',font=('华文行楷', 18),compound=CENTER,cursor="star")
image_n3off = ImageTk.PhotoImage(file=r'0、软件附带文件\Grade Grey.jpg')
n3_off = tk.Button(frame_total, text='不启用N+3', width=140, height=26,image=image_n3off,fg='black',font=('华文行楷', 18),compound=CENTER,cursor="star")

image_del = ImageTk.PhotoImage(file=r'0、软件附带文件\Grade Grey.jpg')
button_cal_del= tk.Button(frame_total, text='核算转移变更处理', width=180, height=26, image=image_del,fg='black',font=('华文行楷', 16),compound=CENTER,cursor="star")

image_rece = ImageTk.PhotoImage(file=r'0、软件附带文件\What lies Beyond.jpg')
button_rece= tk.Button(frame_total, text='已验收项目成本冻结', width=180, height=26, image=image_del,fg='black',font=('华文行楷', 16),compound=CENTER,cursor="star")

image_incal = ImageTk.PhotoImage(file=r'0、软件附带文件\Aqualicious.jpg')
image_outcal = ImageTk.PhotoImage(file=r'0、软件附带文件\Opa.jpg')
button_incal= tk.Button(frame_total, text='国内-核算', width=180, height=26, image=image_incal,fg='black',font=('华文行楷', 16),compound=CENTER,cursor="star")
button_outcal= tk.Button(frame_total, text='海外-核算', width=180, height=26, image=image_outcal,fg='black',font=('华文行楷', 16),compound=CENTER,cursor="star")

button_inall= tk.Button(frame_total, text='国内-概预核决', width=190, height=26, image=image_incal,fg='black',font=('华文行楷', 16),compound=CENTER,cursor="star")
button_outall= tk.Button(frame_total, text='海外-概预核决', width=190, height=26, image=image_outcal,fg='black',font=('华文行楷', 16),compound=CENTER,cursor="star")

button_inbase= tk.Button(frame_total, text='国内-底表', width=180, height=26, image=image_incal,fg='black',font=('华文行楷', 16),compound=CENTER,cursor="star")
button_outbase= tk.Button(frame_total, text='海外-底表', width=180, height=26, image=image_outcal,fg='black',font=('华文行楷', 16),compound=CENTER,cursor="star")

###################################################################################################核算
#button_cal['command']=calcu
###############################################################################################################进度
def  advance():
    frame_total.create_image(740, 60, image=load_image(r'0、软件附带文件\框子.png'), anchor="n")
    for button in button_texts:
        frame_total.coords(button, -200, -200)
    on=frame_total.create_window(740, 200, window=n3_on)
    off=frame_total.create_window(740, 290, window=n3_off)
    button_texts.append(on)
    button_texts.append(off)

def  base_all():
    frame_total.create_image(740, 60, image=load_image(r'0、软件附带文件\框子.png'), anchor="n")
    for button in button_texts:
        frame_total.coords(button, -200, -200)
    inbase=frame_total.create_window(740, 200, window=button_inbase)
    outbase=frame_total.create_window(740, 300, window=button_outbase)
    button_texts.append(inbase)
    button_texts.append(outbase)

def  caicau_all():
    frame_total.create_image(740, 60, image=load_image(r'0、软件附带文件\框子.png'), anchor="n")
    for button in button_texts:
        frame_total.coords(button, -200, -200)

    cal_del=frame_total.create_window(600, 285, window=button_cal_del)
    inside=frame_total.create_window(600, 180, window=button_incal)
    outside=frame_total.create_window(900, 180, window=button_outcal)
    rece=frame_total.create_window(900, 285, window=button_rece)
    button_texts.append(inside)
    button_texts.append(outside)
    button_texts.append(cal_del)
    button_texts.append(rece)

def  all_all():
    frame_total.create_image(740, 60, image=load_image(r'0、软件附带文件\框子.png'), anchor="n")
    for button in button_texts:
        frame_total.coords(button, -200, -200)
    insideall=frame_total.create_window(740, 200, window=button_inall)
    outsideall=frame_total.create_window(740, 300, window=button_outall)
    button_texts.append(insideall)
    button_texts.append(outsideall)

button_base['command']=base_all
button_inbase['command']=base
button_outbase['command']=outside_base

button_abance['command']=advance
n3_on['command']=n3on
n3_off['command'] = n3off

button_cal['command']= caicau_all
button_cal_del['command']=transform
button_incal['command']=calcu
button_outcal['command']=out_calcu
button_total['command']=all_all
button_inall['command']=total
button_outall['command']=total_out
button_rece['command']=rece_lock

window_total.mainloop()








