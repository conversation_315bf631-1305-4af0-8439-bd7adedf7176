#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转视频工具 - AI视觉版
使用AI视觉分析PDF页面图片生成讲解文案
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
import time
import json
import base64
from io import BytesIO

def check_and_install_dependencies():
    """检查并安装依赖"""
    import subprocess
    
    packages = [
        "PyMuPDF",
        "Pillow", 
        "edge-tts",
        "moviepy==1.0.3",
        "requests"
    ]
    
    for package in packages:
        try:
            __import__(package.split('==')[0].lower().replace('-', '_'))
        except ImportError:
            print(f"正在安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
    
    # 导入所需模块
    try:
        import fitz
        from PIL import Image, ImageDraw, ImageFont
        from moviepy.editor import ImageClip, AudioFileClip, concatenate_videoclips
        import requests
        
        return {
            'fitz': fitz,
            'Image': Image,
            'ImageDraw': ImageDraw, 
            'ImageFont': ImageFont,
            'ImageClip': ImageClip,
            'AudioFileClip': AudioFileClip,
            'concatenate_videoclips': concatenate_videoclips,
            'requests': requests
        }
    except ImportError as e:
        print(f"❌ 依赖导入失败: {e}")
        return None

class AIVisionScriptGenerator:
    """AI视觉文案生成器"""
    
    def __init__(self):
        self.api_available = False
        self.setup_ai_service()
    
    def setup_ai_service(self):
        """设置AI服务"""
        print("\n🤖 AI视觉分析设置")
        print("=" * 30)
        print("选择AI服务:")
        print("1. 本地图像分析（基础版）")
        print("2. 在线AI服务（需要API密钥）")
        print("3. 手动描述模式")
        
        choice = input("请选择 (1-3): ").strip()
        
        if choice == "2":
            api_key = input("请输入AI服务API密钥（如OpenAI、百度等）: ").strip()
            if api_key:
                self.api_key = api_key
                self.api_available = True
                print("✅ AI服务配置完成")
            else:
                print("⚠️ 未配置API密钥，将使用本地分析")
        elif choice == "3":
            self.manual_mode = True
            print("✅ 设置为手动描述模式")
        else:
            print("✅ 使用本地图像分析")
    
    def analyze_image_local(self, image, page_num):
        """本地图像分析（基础版）"""
        # 基于图像特征的简单分析
        width, height = image.size
        
        # 转换为灰度图分析
        gray_image = image.convert('L')
        
        # 简单的内容类型判断
        # 计算图像的复杂度
        import numpy as np
        img_array = np.array(gray_image)
        
        # 计算边缘数量（简单的复杂度指标）
        edges = np.sum(np.abs(np.diff(img_array, axis=0))) + np.sum(np.abs(np.diff(img_array, axis=1)))
        complexity = edges / (width * height)
        
        # 根据复杂度判断内容类型
        if complexity > 50:
            content_type = "图表或复杂内容"
            description = "这一页包含了详细的图表或复杂的信息结构"
        elif complexity > 20:
            content_type = "文字内容"
            description = "这一页主要是文字说明和概念介绍"
        else:
            content_type = "简单内容"
            description = "这一页的内容相对简洁明了"
        
        return {
            'content_type': content_type,
            'description': description,
            'complexity': complexity
        }
    
    def analyze_image_ai(self, image, page_num):
        """使用AI服务分析图像"""
        if not self.api_available:
            return self.analyze_image_local(image, page_num)
        
        try:
            # 将图像转换为base64
            buffer = BytesIO()
            image.save(buffer, format='PNG')
            img_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            # 这里可以调用各种AI服务的API
            # 示例：调用视觉分析API
            analysis = self.call_vision_api(img_base64, page_num)
            return analysis
            
        except Exception as e:
            print(f"AI分析失败，使用本地分析: {e}")
            return self.analyze_image_local(image, page_num)
    
    def call_vision_api(self, img_base64, page_num):
        """调用视觉分析API（示例）"""
        # 这里是API调用的示例框架
        # 实际使用时需要根据具体的AI服务进行调整
        
        prompt = f"""
        请分析这张PDF第{page_num}页的图片内容，并生成适合语音讲解的文案。
        要求：
        1. 描述页面的主要内容和结构
        2. 提取关键信息点
        3. 生成自然流畅的讲解文案
        4. 文案长度控制在50-100字
        5. 语言要口语化，适合教学讲解
        """
        
        # 模拟API响应（实际使用时替换为真实API调用）
        return {
            'content_type': '教学内容',
            'description': f'第{page_num}页展示了重要的学习内容，包含了关键概念和实用信息',
            'script': f'现在我们来看第{page_num}页。这里展示的内容对我们的学习很重要，让我来为大家详细解释一下其中的关键要点。'
        }
    
    def manual_describe_image(self, image, page_num, original_text=""):
        """手动描述图像内容"""
        print(f"\n📷 第{page_num}页图像分析")
        print("-" * 30)
        
        # 显示图像信息
        width, height = image.size
        print(f"图像尺寸: {width}x{height}")
        
        if original_text:
            print(f"提取的文字: {original_text[:100]}...")
        
        print("\n请描述这一页的主要内容:")
        print("（例如：这页讲的是增值税的计算方法，包含了公式和实例）")
        
        description = input("内容描述: ").strip()
        
        if not description:
            description = f"第{page_num}页的重要内容"
        
        print("\n请输入适合语音讲解的文案:")
        print("（例如：现在我们来学习增值税的计算。这个公式很重要，大家要仔细理解）")
        
        script = input("讲解文案: ").strip()
        
        if not script:
            script = f"现在我们来看第{page_num}页。{description}，这部分内容需要大家认真学习。"
        
        return {
            'content_type': '用户描述',
            'description': description,
            'script': script
        }
    
    def generate_script_from_image(self, image, page_num, original_text="", total_pages=1):
        """根据图像生成文案"""
        
        if hasattr(self, 'manual_mode') and self.manual_mode:
            # 手动描述模式
            analysis = self.manual_describe_image(image, page_num, original_text)
        else:
            # AI分析模式
            analysis = self.analyze_image_ai(image, page_num)
        
        # 生成完整的讲解文案
        if 'script' in analysis:
            script = analysis['script']
        else:
            # 基于分析结果生成文案
            content_type = analysis.get('content_type', '内容')
            description = analysis.get('description', '重要信息')
            
            if page_num == 1:
                script = f"大家好，欢迎来到今天的课程。我们首先来看第一页。{description}。"
            elif page_num == total_pages:
                script = f"最后我们来看第{page_num}页。{description}。好的，今天的课程就到这里，谢谢大家。"
            else:
                script = f"现在我们来看第{page_num}页。这里展示的是{content_type}。{description}。大家要重点理解这部分内容。"
        
        return script

class AIVisionPDFConverter:
    def __init__(self, deps):
        self.deps = deps
        self.temp_dir = Path(tempfile.mkdtemp())
        self.output_dir = Path("output")
        self.output_dir.mkdir(exist_ok=True)
        
        self.script_generator = AIVisionScriptGenerator()
        
    def extract_pdf_content(self, pdf_path):
        """提取PDF内容"""
        print("📖 正在提取PDF页面图像...")
        
        try:
            doc = self.deps['fitz'].open(str(pdf_path))
            pages_data = []
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                
                # 提取文本（作为参考）
                text = page.get_text()
                
                # 转换为高质量图片
                mat = self.deps['fitz'].Matrix(2.0, 2.0)  # 2倍缩放
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("ppm")
                
                from io import BytesIO
                image = self.deps['Image'].open(BytesIO(img_data))
                
                pages_data.append({
                    'page_num': page_num + 1,
                    'image': image,
                    'original_text': text.strip(),
                    'script_text': '',
                    'use_audio': True
                })
                
                pix = None
            
            doc.close()
            print(f"✅ 成功提取 {len(pages_data)} 页图像")
            return pages_data
            
        except Exception as e:
            print(f"❌ PDF内容提取失败: {e}")
            return []
    
    def generate_ai_scripts(self, pages_data, course_title):
        """使用AI视觉分析生成文案"""
        print("\n🤖 正在进行AI视觉分析...")
        
        total_pages = len(pages_data)
        
        for page_data in pages_data:
            page_num = page_data['page_num']
            image = page_data['image']
            original_text = page_data['original_text']
            
            print(f"  分析第 {page_num}/{total_pages} 页...")
            
            # 使用AI视觉分析生成文案
            script = self.script_generator.generate_script_from_image(
                image, page_num, original_text, total_pages
            )
            
            page_data['script_text'] = script
            print(f"  ✅ 第{page_num}页文案生成完成")
        
        # 保存生成的文案
        self.save_scripts_to_file(pages_data, course_title)
        
        return pages_data
    
    def save_scripts_to_file(self, pages_data, course_title):
        """保存文案到文件"""
        scripts_file = Path(f"{course_title}_ai_vision_scripts.txt")
        
        with open(scripts_file, 'w', encoding='utf-8') as f:
            f.write(f"# {course_title} - AI视觉分析文案\n")
            f.write("# 这些文案是基于PDF页面图像的AI视觉分析生成的\n")
            f.write("# 您可以根据需要进行调整\n\n")
            
            for page_data in pages_data:
                f.write(f"[{page_data['page_num']}] {page_data['script_text']}\n\n")
        
        print(f"\n📝 AI生成的文案已保存到: {scripts_file}")
        
        # 预览文案
        print("\n📋 AI视觉分析文案预览:")
        print("=" * 50)
        for i, page_data in enumerate(pages_data[:3]):
            print(f"\n第{page_data['page_num']}页:")
            print(f"文案: {page_data['script_text']}")
        
        if len(pages_data) > 3:
            print(f"\n... 还有 {len(pages_data) - 3} 页")
        
        print("\n选择操作:")
        print("1. 使用AI生成的文案")
        print("2. 编辑文案文件")
        
        choice = input("请选择 (1-2): ").strip()
        
        if choice == "2":
            print(f"\n请编辑文案文件: {scripts_file}")
            print("编辑完成后按回车继续...")
            input()
            self.load_scripts_from_file(pages_data, scripts_file)
    
    def load_scripts_from_file(self, pages_data, scripts_file):
        """从文件加载文案"""
        try:
            with open(scripts_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            scripts_dict = {}
            lines = content.split('\n')
            
            for line in lines:
                line = line.strip()
                if line.startswith('[') and ']' in line and not line.startswith('#'):
                    try:
                        end_bracket = line.index(']')
                        page_num = int(line[1:end_bracket])
                        script_text = line[end_bracket + 1:].strip()
                        if script_text:
                            scripts_dict[page_num] = script_text
                    except:
                        continue
            
            for page_data in pages_data:
                page_num = page_data['page_num']
                if page_num in scripts_dict:
                    page_data['script_text'] = scripts_dict[page_num]
            
            print(f"✅ 成功加载文案")
            
        except Exception as e:
            print(f"❌ 文案加载失败: {e}")
    
    def generate_speech(self, text, output_path):
        """生成语音"""
        try:
            import subprocess
            
            cmd = [
                "edge-tts",
                "--voice", "zh-CN-XiaoxiaoNeural",
                "--rate", "-10%",
                "--text", text,
                "--write-media", str(output_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0 and Path(output_path).exists()
            
        except Exception as e:
            print(f"语音生成失败: {e}")
            return False
    
    def create_video(self, pages_data, output_path):
        """创建视频"""
        print("\n🎬 开始创建视频...")
        clips = []
        
        for page_data in pages_data:
            page_num = page_data['page_num']
            print(f"  处理第 {page_num} 页...")
            
            # 处理图片
            img = page_data['image']
            img_resized = img.resize((1280, 720), self.deps['Image'].Resampling.LANCZOS)
            
            # 添加页码
            draw = self.deps['ImageDraw'].Draw(img_resized)
            try:
                font = self.deps['ImageFont'].truetype("arial.ttf", 24)
            except:
                font = self.deps['ImageFont'].load_default()
            
            # 添加标识
            overlay = self.deps['Image'].new('RGBA', (250, 40), (0, 0, 0, 128))
            img_resized.paste(overlay, (10, 10), overlay)
            
            draw.text((15, 20), f"第 {page_num} 页 (AI分析)", fill=(255, 255, 255), font=font)
            
            # 保存图片
            img_path = self.temp_dir / f"page_{page_num}.png"
            img_resized.save(img_path)
            
            # 处理音频
            duration = 8  # AI生成的文案通常需要更长时间
            audio_path = self.temp_dir / f"audio_{page_num}.wav"
            script_text = page_data['script_text']
            
            if self.generate_speech(script_text, str(audio_path)):
                try:
                    audio_clip = self.deps['AudioFileClip'](str(audio_path))
                    duration = max(audio_clip.duration, 5)
                    audio_clip.close()
                except:
                    pass
            
            # 创建视频片段
            try:
                video_clip = self.deps['ImageClip'](str(img_path), duration=duration)
                
                if Path(audio_path).exists():
                    audio_clip = self.deps['AudioFileClip'](str(audio_path))
                    video_clip = video_clip.set_audio(audio_clip)
                
                clips.append(video_clip)
                print(f"    ✅ 第 {page_num} 页完成 (时长: {duration:.1f}秒)")
            except Exception as e:
                print(f"    ❌ 第 {page_num} 页失败: {e}")
                continue
        
        if not clips:
            print("❌ 没有成功创建任何视频片段")
            return False
        
        try:
            print("正在合并视频...")
            final_video = self.deps['concatenate_videoclips'](clips)
            
            print("正在输出视频文件...")
            final_video.write_videofile(
                str(output_path),
                fps=24,
                codec='libx264',
                audio_codec='aac',
                verbose=False,
                logger=None
            )
            
            final_video.close()
            for clip in clips:
                clip.close()
            
            return True
        except Exception as e:
            print(f"❌ 视频生成失败: {e}")
            return False
    
    def convert(self, pdf_path):
        """转换PDF为视频"""
        pdf_path = Path(pdf_path)
        if not pdf_path.exists():
            print(f"❌ PDF文件不存在: {pdf_path}")
            return False
        
        pdf_name = pdf_path.stem
        print(f"🎯 开始处理: {pdf_path}")
        print("="*60)
        
        try:
            # 1. 提取PDF页面图像
            pages_data = self.extract_pdf_content(pdf_path)
            if not pages_data:
                return False
            
            # 2. AI视觉分析生成文案
            pages_data = self.generate_ai_scripts(pages_data, pdf_name)
            
            # 3. 确认生成
            print(f"\n📋 准备生成视频:")
            print(f"总页数: {len(pages_data)}")
            print(f"文案类型: AI视觉分析生成")
            
            confirm = input("\n确认开始生成视频？(y/n): ").lower()
            if confirm != 'y':
                print("❌ 用户取消操作")
                return False
            
            # 4. 创建视频
            output_name = f"{pdf_name}_ai_vision.mp4"
            output_path = self.output_dir / output_name
            
            success = self.create_video(pages_data, output_path)
            
            if success:
                print(f"\n🎉 转换成功!")
                print(f"输出文件: {output_path}")
                if output_path.exists():
                    size_mb = output_path.stat().st_size / 1024 / 1024
                    print(f"文件大小: {size_mb:.1f} MB")
                return True
            else:
                print("\n❌ 转换失败")
                return False
                
        except Exception as e:
            print(f"❌ 转换过程出错: {e}")
            return False
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理临时文件"""
        try:
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
        except:
            pass

def main():
    print("PDF转视频工具 - AI视觉版")
    print("=" * 30)
    print("🤖 使用AI视觉分析PDF页面生成讲解文案")
    print("=" * 30)
    
    # 检查并安装依赖
    deps = check_and_install_dependencies()
    if not deps:
        return
    
    # 获取PDF文件
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    else:
        pdf_files = list(Path(".").glob("*.pdf"))
        if not pdf_files:
            print("❌ 当前目录下没有找到PDF文件")
            return
        elif len(pdf_files) == 1:
            pdf_path = pdf_files[0]
        else:
            print("找到多个PDF文件:")
            for i, pdf_file in enumerate(pdf_files, 1):
                print(f"{i}. {pdf_file}")
            
            try:
                choice = int(input("请选择: ")) - 1
                pdf_path = pdf_files[choice]
            except:
                print("❌ 选择错误")
                return
    
    # 开始转换
    converter = AIVisionPDFConverter(deps)
    converter.convert(pdf_path)

if __name__ == "__main__":
    main()
