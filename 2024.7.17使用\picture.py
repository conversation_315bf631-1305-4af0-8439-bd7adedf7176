# common.py
"""
存放打开的图片
"""
from PIL import Image, ImageTk, ImageSequence
#globals()[img_obj_dic==dict[str, PhotoImage]]
img_obj_dic = {'str': ' PhotoImage'}

def load_image(path):
    """
    加载图片
    """
    global img_obj_dic
    key = path
    key = key.encode("utf-8")
    if key in img_obj_dic.keys():
        return img_obj_dic[key]
    img = ImageTk.PhotoImage(image=Image.open(path))
    img_obj_dic.update({key: img})
    return img_obj_dic[key]