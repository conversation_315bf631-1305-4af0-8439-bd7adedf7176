#coding:utf-8
import zipfile
####################################################抬头
import threading
import pandas as pd
import numpy as np
import time
import os
import xlsxwriter
from pathlib import Path
import warnings
import tkinter as tk
import tkinter.messagebox #弹出框
from tkinter import *
from PIL import Image, ImageTk, ImageSequence
import xlsxwriter
import traceback
import datetime
from tkinter import scrolledtext
from datetime import datetime
warnings.filterwarnings('ignore')
window_total = tk.Tk()
window_total.title('项目财经自动化程序8-8')  # 窗口的标题
window_total.geometry('1200x900')  # 窗口的大小
window_total.iconbitmap('软件附带文件\头像.ico')

global frame_total
frame_total = tk.Canvas(window_total,width=1200,height=900,background='silver',scrollregion=(0,0,1200,1000))

###转移变更处理
global button_cal_del
image_del = ImageTk.PhotoImage(file=r'软件附带文件\Maldives.jpg')
button_cal_del= tk.Button(frame_total, text='核算转移变更处理', width=180, height=26, image=image_del,fg='black',font=('华文行楷', 16),compound=CENTER,cursor="star")
